import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ref } from 'vue'
import { useAssessmentManagement } from '@/lecturer/composables/useAssessmentManagement'
import type { Assessment } from '@/lecturer/types/models/assessment'

// Mock the API composable
vi.mock('@/lecturer/composables/useAssessmentApi', () => ({
  useAssessmentApi: () => ({
    assessments: {
      getAll: vi.fn().mockResolvedValue({
        success: true,
        data: [],
      }),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      validateWeights: vi.fn().mockResolvedValue({
        success: true,
        data: {
          total_weight: 0,
          is_valid: true,
          exceeds_limit: false,
          missing_weight: 100,
          component_validations: [],
        },
      }),
    },
    assessmentDetails: {
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      reorder: vi.fn(),
    },
  }),
}))

// Mock the weight validation composable
vi.mock('@/lecturer/composables/useWeightValidation', () => ({
  useWeightValidation: (assessments: unknown) => ({
    totalWeight: ref(0),
    isWeightValid: ref(true),
    weightExceeded: ref(0),
    weightRemaining: ref(100),
    weightPercentage: ref(0),
    validationStatus: ref('empty'),
    statusColor: ref('text-gray-400'),
    statusIcon: ref('Circle'),
    componentValidations: ref([]),
    hasComponentErrors: ref(false),
    validationMessages: ref([]),
    validateWeightChange: vi.fn().mockReturnValue({ isValid: true, message: 'Valid' }),
    validateComponentWeights: vi.fn().mockReturnValue({ isValid: true, message: 'Valid' }),
    getWeightSuggestions: vi.fn().mockReturnValue([]),
  }),
}))

describe('useAssessmentManagement - Weight Validation', () => {
  let composable: ReturnType<typeof useAssessmentManagement>

  beforeEach(() => {
    composable = useAssessmentManagement(1)
  })

  it('should initialize with empty assessments and valid weight state', () => {
    expect(composable.assessments.value).toEqual([])
    expect(composable.totalWeight.value).toBe(0)
    expect(composable.isWeightValid.value).toBe(true)
    expect(composable.validationStatus.value).toBe('empty')
  })

  it('should provide real-time weight validation methods', () => {
    expect(typeof composable.validateWeightChangeRealTime).toBe('function')
    expect(typeof composable.validateComponentWeightsRealTime).toBe('function')
    expect(typeof composable.getWeightChangePreview).toBe('function')
  })

  it('should manage pending weight changes', () => {
    expect(composable.pendingWeightChanges.value.size).toBe(0)
    expect(typeof composable.clearPendingWeightChanges).toBe('function')
    expect(typeof composable.applyPendingWeightChanges).toBe('function')
  })

  it('should allow toggling real-time validation', () => {
    expect(composable.realTimeValidation.value).toBe(true)

    composable.toggleRealTimeValidation(false)
    expect(composable.realTimeValidation.value).toBe(false)

    composable.toggleRealTimeValidation(true)
    expect(composable.realTimeValidation.value).toBe(true)
  })

  it('should provide weight validation summary', () => {
    const summary = composable.weightValidationSummary.value

    expect(summary).toHaveProperty('totalWeight')
    expect(summary).toHaveProperty('isValid')
    expect(summary).toHaveProperty('status')
    expect(summary).toHaveProperty('statusColor')
    expect(summary).toHaveProperty('statusIcon')
    expect(summary).toHaveProperty('weightExceeded')
    expect(summary).toHaveProperty('weightRemaining')
    expect(summary).toHaveProperty('weightPercentage')
    expect(summary).toHaveProperty('hasComponentErrors')
    expect(summary).toHaveProperty('componentValidations')
    expect(summary).toHaveProperty('messages')
    expect(summary).toHaveProperty('suggestions')
  })

  it('should validate weight changes in real-time', () => {
    const result = composable.validateWeightChangeRealTime(1, 50)

    expect(result).toHaveProperty('isValid')
    expect(result).toHaveProperty('message')
    expect(result.isValid).toBe(true)
  })

  it('should provide weight change preview', () => {
    // Test with non-existent assessment (should return null)
    const preview = composable.getWeightChangePreview(999, 40)
    expect(preview).toBeNull()

    // Test that the function exists and is callable
    expect(typeof composable.getWeightChangePreview).toBe('function')
  })

  it('should handle validation errors and warnings', () => {
    expect(composable.hasValidationErrors.value).toBe(false)
    expect(composable.hasValidationWarnings.value).toBe(false)
    expect(composable.allValidationMessages.value).toEqual([])
  })
})
