[{"id": 2, "semester_id": 1, "curriculum_unit_id": 2, "lecture_id": null, "section_code": null, "max_capacity": 1000, "current_enrollment": 100, "waitlist_capacity": 10, "current_waitlist": 0, "delivery_mode": "in_person", "schedule_days": null, "schedule_time_start": null, "schedule_time_end": null, "location": null, "is_active": true, "enrollment_status": "open", "registration_start_date": null, "registration_end_date": null, "special_requirements": null, "notes": null, "created_at": "2025-07-23T07:45:58.000000Z", "updated_at": "2025-07-23T07:53:02.000000Z", "deleted_at": null, "course_code": "COS10009", "course_title": "Introduction to Programming", "credit_hours": 12, "status": "open", "max_enrollment": 1000, "tuition_per_credit": 500, "additional_fees": 50, "drop_deadline": null, "withdrawal_deadline": null, "curriculum_unit": {"id": 2, "curriculum_version_id": 1, "unit_id": 2, "semester_id": 1, "type": "core", "unit_scope": "program", "year_level": 1, "semester_number": 1, "is_required": 1, "minimum_grade": null, "note": "AI Specialization - core unit for Year 1, Semester 1", "created_at": "2025-07-23T07:18:56.000000Z", "updated_at": "2025-07-23T07:18:56.000000Z", "unit": {"id": 2, "code": "COS10009", "name": "Introduction to Programming", "credit_points": "12.50", "created_at": "2025-07-23T07:18:56.000000Z", "updated_at": "2025-07-23T07:18:56.000000Z"}}}, {"id": 3, "semester_id": 1, "curriculum_unit_id": 1, "lecture_id": 1, "section_code": "A", "max_capacity": 500, "current_enrollment": 50, "waitlist_capacity": 10, "current_waitlist": 0, "delivery_mode": "in_person", "schedule_days": null, "schedule_time_start": null, "schedule_time_end": null, "location": null, "is_active": true, "enrollment_status": "open", "registration_start_date": null, "registration_end_date": null, "special_requirements": null, "notes": null, "created_at": "2025-07-23T07:55:16.000000Z", "updated_at": "2025-07-25T02:52:20.000000Z", "deleted_at": null, "course_code": "COS10004", "course_title": "Computer Systems", "credit_hours": 12, "status": "open", "max_enrollment": 500, "tuition_per_credit": 500, "additional_fees": 50, "drop_deadline": null, "withdrawal_deadline": null, "curriculum_unit": {"id": 1, "curriculum_version_id": 1, "unit_id": 1, "semester_id": 1, "type": "core", "unit_scope": "program", "year_level": 1, "semester_number": 1, "is_required": 1, "minimum_grade": null, "note": "AI Specialization - core unit for Year 1, Semester 1", "created_at": "2025-07-23T07:18:56.000000Z", "updated_at": "2025-07-23T07:18:56.000000Z", "unit": {"id": 1, "code": "COS10004", "name": "Computer Systems", "credit_points": "12.50", "created_at": "2025-07-23T07:18:56.000000Z", "updated_at": "2025-07-23T07:18:56.000000Z"}}}, {"id": 4, "semester_id": 1, "curriculum_unit_id": 1, "lecture_id": null, "section_code": "B", "max_capacity": 500, "current_enrollment": 50, "waitlist_capacity": 10, "current_waitlist": 0, "delivery_mode": "in_person", "schedule_days": null, "schedule_time_start": null, "schedule_time_end": null, "location": null, "is_active": true, "enrollment_status": "open", "registration_start_date": null, "registration_end_date": null, "special_requirements": null, "notes": null, "created_at": "2025-07-23T07:55:16.000000Z", "updated_at": "2025-07-23T07:55:16.000000Z", "deleted_at": null, "course_code": "COS10004", "course_title": "Computer Systems", "credit_hours": 12, "status": "open", "max_enrollment": 500, "tuition_per_credit": 500, "additional_fees": 50, "drop_deadline": null, "withdrawal_deadline": null, "curriculum_unit": {"id": 1, "curriculum_version_id": 1, "unit_id": 1, "semester_id": 1, "type": "core", "unit_scope": "program", "year_level": 1, "semester_number": 1, "is_required": 1, "minimum_grade": null, "note": "AI Specialization - core unit for Year 1, Semester 1", "created_at": "2025-07-23T07:18:56.000000Z", "updated_at": "2025-07-23T07:18:56.000000Z", "unit": {"id": 1, "code": "COS10004", "name": "Computer Systems", "credit_points": "12.50", "created_at": "2025-07-23T07:18:56.000000Z", "updated_at": "2025-07-23T07:18:56.000000Z"}}}]