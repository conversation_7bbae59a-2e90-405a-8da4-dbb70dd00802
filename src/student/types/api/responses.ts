// API Response Types (aligned with database schema)

import type {
  Student,
  <PERSON><PERSON><PERSON>,
  CreditProgress,
  GPACalculation,
  AcademicStanding,
  Enrollment,
  SemesterExtended,
} from '../models/student'
import type { CourseOffering, CourseRegistration, ClassSession } from '../models/course'
import type { AcademicHold } from '../models/hold'
import type { AssessmentComponentDetail, AcademicRecord, AssessmentScore } from '../models/academic'
import type { Attendance } from '../models/attendance'
import type { Notification } from '../models/notification'

// Standardized API Response
export interface ApiResponse<T = unknown> {
  success: boolean
  message?: string
  data?: T
  errors?: Record<string, string[]>
  meta?: {
    pagination?: PaginationMeta
    filters?: FilterMeta
  }
}

export interface PaginationMeta {
  current_page: number
  per_page: number
  total: number
  last_page: number
  from: number
  to: number
}

export interface FilterMeta {
  applied_filters: Record<string, unknown>
  available_filters: Record<string, unknown[]>
}

// Dashboard API Response (aligned with database schema)
export interface DashboardResponse {
  current_semester: SemesterExtended & {
    academic_calendar_url: string
  }
  credit_progress: CreditProgress
  current_gpa: GPACalculation & {
    rank?: number
    trend: 'up' | 'down' | 'stable'
    semester_gpa: number
    cumulative_gpa: number
  }
  academic_holds: AcademicHold[]
  upcoming_assessments: AssessmentComponentDetail[]
  notifications: Notification[]
  enrollment_status: Enrollment
  academic_standing: AcademicStanding
}

export interface AvailableCourseOffering {
  id: number
  unit: AvailableCourseUnit
  lecturer: AvailableCourseLecturer
  registration_eligibility: RegistrationEligibility
}
// Available Courses API Response Types
export interface AvailableCourseUnit {
  code: string
  name: string
  description: string
  credit_hours: number
}

export interface AvailableCourseLecturer {
  id: number | null
  name: string | null
  email: string | null
}

export interface RegistrationEligibility {
  can_register: boolean
  prerequisites_met: boolean
  has_conflicts: boolean
  capacity_available: boolean
  eligibility_summary: string
}

// Course Registration API Response
export interface CourseRegistrationResponse {
  id: number
}
// Course registered API Response
export interface CourseRegisteredResponse {
  id: number
  status: string
  status_display: string
  registration_date: string
  drop_date: string | null
  unit: {
    code: string
    name: string
    credit_hours: number
  }
  lecturer: {
    name: string | null
    email: string | null
  }
  semester: {
    name: string
    code: string
  }
  actions: {
    action: string
    label: string
    method: string
    confirmation_required: boolean
  }[]
}

// Timetable API Response
export interface TimetableResponse {
  class_sessions: ClassSession[]
  semester: Semester
  week_range: {
    start_date: string
    end_date: string
  }
}

// Grades API Response
export interface GradesResponse {
  academic_records: AcademicRecord[]
  assessment_scores: AssessmentScore[]
  gpa_calculations: GPACalculation[]
  semester_summaries: {
    semester: Semester
    total_credits: number
    semester_gpa: number
    units_completed: number
  }[]
}

// Attendance API Response
export interface AttendanceResponse {
  attendance_records: Attendance[]
  attendance_summary: {
    course_offering: CourseOffering
    total_sessions: number
    attended_sessions: number
    attendance_percentage: number
    minimum_required: number
    status: 'good' | 'warning' | 'critical'
  }[]
}

// Profile API Response
export interface ProfileResponse {
  student: Student
  study_plan: {
    curriculum_version: unknown // Will be defined when curriculum components are implemented
    progress: CreditProgress
  }
}
