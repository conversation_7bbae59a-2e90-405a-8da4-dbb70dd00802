// Student Profile Types
export type EnrollmentStatus = 'active' | 'inactive' | 'graduated' | 'suspended' | 'withdrawn'
export type Gender = 'male' | 'female' | 'other' | 'prefer_not_to_say'

// Facilities Models (aligned with database schema)
export interface Campus {
  id: string
  name: string
  code: string
  address: string
  buildings: Building[]
}

export interface Building {
  id: string
  campus_id: string
  name: string
  code: string
  campus: Campus
}

export interface Room {
  id: string
  campus_id: string
  name: string
  code: string
  campus: Campus
}

// Academic Program Models (aligned with database schema)
export type DegreeType = 'bachelor' | 'master' | 'phd' | 'diploma' | 'certificate'

export interface Program {
  id: string
  name: string
  code: string
  description: string
  degree_type: DegreeType
  duration_years: number
  total_credits: number
}

export interface Specialization {
  id: string
  program_id: string
  name: string
  code: string
  required_credits: number
  program: Program
}

// System Models
export interface Semester {
  id: string
  name: string
  code: string
  start_date: string
  end_date: string
  academic_year: string
  is_current: boolean
  registration_start: string
  registration_end: string
  academic_calendar_url?: string
}

export interface CurriculumVersion {
  id: string
  program_id: string
  specialization_id?: string
  semester_id: string
  program: Program
  specialization?: Specialization
  semester: Semester
}

// Student Profile (aligned with database schema)
export interface Student {
  id: string
  student_id: string
  full_name: string
  email: string
  program_id: string
  specialization_id?: string
  campus_id: string
  curriculum_version_id: string
  program: Program
  specialization?: Specialization
  campus: Campus
  curriculum_version: CurriculumVersion
  // Additional fields for UI
  phone?: string
  date_of_birth?: string
  gender?: Gender
  nationality?: string
  address?: string
  avatar_url?: string
  enrollment_status: EnrollmentStatus
  admission_date: string
  expected_graduation_date: string
  can_register: boolean
  has_active_holds: boolean
  created_at: string
  updated_at: string
}

// Course & Unit Models (aligned with database schema)
export interface Unit {
  id: string
  code: string
  name: string
  credit_points: number
}

export interface CurriculumUnit {
  id: string
  curriculum_version_id: string
  unit_id: string
  semester_id: string
  curriculum_version: CurriculumVersion
  unit: Unit
  semester: Semester
}

// Staff Models
export interface Lecturer {
  id: string | null
  // employee_id: string
  name: string | null
  email: string | null
  // campus_id: string
  // campus: Campus
}

export interface User {
  id: string
  name: string
  email: string
}

// Academic Progress Models (aligned with database schema)
export interface CreditProgress {
  earned_credits: number
  required_credits: number
  remaining_requirements: CurriculumUnit[]
  completion_percentage: number
  credits_this_semester: number
  credits_in_progress: number
}

export interface Enrollment {
  id: string
  student_id: string
  semester_id: string
  curriculum_version_id: string
  student: Student
  semester: Semester
  curriculum_version: CurriculumVersion
}

export interface AcademicStanding {
  id: string
  student_id: string
  semester_id: string
  student: Student
  semester: Semester
}

export interface GPACalculation {
  id: string
  student_id: string
  semester_id: string
  program_id: string
  student: Student
  semester: Semester
  program: Program
  semester_gpa: number
  cumulative_gpa: number
  trend: 'up' | 'down' | 'stable'
  rank?: number
}

// Alias for backward compatibility
export type SemesterExtended = Semester

// Legacy GPAData interface for backward compatibility
export interface GPAData {
  current_gpa: number
  semester_gpa: number
  cumulative_gpa: number
  trend?: 'up' | 'down' | 'stable'
  academic_standing: string
  total_courses: number
  grade_distribution: Record<string, number>
}
