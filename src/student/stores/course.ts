import { use<PERSON><PERSON><PERSON><PERSON> } from '@/shared/composables/useBase<PERSON>pi'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type {
  AvailableCourseOffering,
  CourseRegisteredResponse,
  CourseRegistrationResponse,
} from '../types/api/responses'
import type { RegistrationStatus, Schedule, TimeConflict } from '../types/models/course'

export const useCourseStore = defineStore('course', () => {
  // API client
  const api = useBaseApi()

  // State (updated for database schema)
  const registrationStatus = ref<RegistrationStatus>('idle')
  const conflicts = ref<TimeConflict[]>([])
  const registrationPeriod = ref<{
    start_date: string
    end_date: string
    is_open: boolean
  } | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // New state for available courses API response
  const availableCourseOfferings = ref<AvailableCourseOffering[]>([])
  const selectedCourseOfferings = ref<AvailableCourseOffering[]>([])
  const registeredCourseOfferings = ref<CourseRegisteredResponse[]>([])

  // Computed properties for available course offerings
  const availableCourseOfferingsFiltered = computed(() => {
    return availableCourseOfferings.value.filter(
      (offering: AvailableCourseOffering) =>
        !selectedCourseOfferings.value.some(
          (selected: AvailableCourseOffering) => selected.id === offering.id,
        ),
    )
  })

  // Helper function to detect schedule overlaps
  const detectScheduleOverlap = (schedule1: Schedule[], schedule2: Schedule[]): boolean => {
    for (const slot1 of schedule1) {
      for (const slot2 of schedule2) {
        // Check if same day
        if (slot1.day_of_week === slot2.day_of_week) {
          // Convert times to minutes for easier comparison
          const start1 = timeToMinutes(slot1.start_time)
          const end1 = timeToMinutes(slot1.end_time)
          const start2 = timeToMinutes(slot2.start_time)
          const end2 = timeToMinutes(slot2.end_time)

          // Check for overlap
          if (start1 < end2 && start2 < end1) {
            return true
          }
        }
      }
    }
    return false
  }

  // Helper to convert HH:MM to minutes
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number)
    return hours * 60 + minutes
  }

  const submitRegistration = async () => {
    if (selectedCourseOfferings.value.length === 0) {
      return { success: false, message: 'No courses selected' }
    }

    // Final conflict check before submission

    if (conflicts.value.length > 0) {
      return {
        success: false,
        message: 'Please resolve conflicts before submitting registration',
      }
    }

    try {
      const courseIds = selectedCourseOfferings.value.map((course) => course.id)

      const response = await api.post<CourseRegistrationResponse>(
        `/student/course-registration/register`,
        {
          course_offering_id: courseIds,
        },
      )

      return {
        success: response.success || false,
        message: response.message || 'Registration failed',
      }
    } catch (err) {
      return {
        success: false,
        message: err instanceof Error ? err.message : 'Registration failed',
      }
    }
  }

  const clearSelection = () => {
    selectedCourseOfferings.value = []
    conflicts.value = []
  }

  const resetStore = () => {
    conflicts.value = []
    error.value = null
  }

  // Methods for course offerings (updated for actual API response)
  const fetchAvailableOfferings = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.get<AvailableCourseOffering[]>(
        `/student/course-registration/available-courses`,
      )

      if (response.success && response.data) {
        availableCourseOfferings.value = response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch available courses'
    } finally {
      isLoading.value = false
    }
  }
  // Methods for handling available course offerings
  const addAvailableCourseOffering = (offering: AvailableCourseOffering) => {
    // Check if offering is already selected
    if (selectedCourseOfferings.value.some((o) => o.id === offering.id)) {
      return { success: false, error: 'Course offering is already selected' }
    }

    // Check if the user can register for this course
    if (!offering.registration_eligibility.can_register) {
      return { success: false, error: 'You are not eligible to register for this course' }
    }

    selectedCourseOfferings.value.push(offering)
    return { success: true }
  }

  const removeAvailableCourseOffering = (offeringId: number) => {
    selectedCourseOfferings.value = selectedCourseOfferings.value.filter(
      (offering) => offering.id !== offeringId,
    )
    return { success: true }
  }

  // Actions
  const fetchRegisteredCourses = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.get<CourseRegisteredResponse[]>(
        `/student/course-registration/my-registrations`,
      )

      if (response.success && response.data) {
        registeredCourseOfferings.value = response.data
      } else {
        throw new Error(response.message || 'Failed to fetch registered courses')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch registered courses'
      console.error('Registered courses fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }
  return {
    // State (database schema aligned)
    conflicts,
    isLoading,
    error,
    registrationStatus,
    registrationPeriod,
    // New state for available courses API
    availableCourseOfferings,
    selectedCourseOfferings,
    availableCourseOfferingsFiltered,

    // Legacy state for backward compatibility

    // Computed (database schema aligned)

    // New computed for available course offerings

    // Legacy computed for backward compatibility

    // Actions (database schema aligned)
    fetchAvailableOfferings,

    // Actions for available course offerings
    addAvailableCourseOffering,
    removeAvailableCourseOffering,

    // Legacy actions for backward compatibility
    submitRegistration,
    clearSelection,
    resetStore,

    // Helper methods exposed for testing
    detectScheduleOverlap,
    timeToMinutes,

    // Actions for registered courses
    fetchRegisteredCourses,
    registeredCourseOfferings,

    // Drop course action
    dropCourse: async (registrationId: number) => {
      try {
        const response = await api.post<{ success: boolean; message: string }>(
          `/student/course-registration/drop/${registrationId}`,
          {}
        )

        if (response.success) {
          // Remove the dropped course from local state
          registeredCourseOfferings.value = registeredCourseOfferings.value.filter(
            course => course.id !== registrationId
          )
          return { success: true, message: response.message || 'Course dropped successfully' }
        } else {
          throw new Error(response.message || 'Failed to drop course')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to drop course'
        console.error('Drop course error:', err)
        return { success: false, message: errorMessage }
      }
    },
  }
})
