<script setup lang="ts">
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Separator } from '@/shared/components/ui/separator'
import {
  AlertTriangle,
  BookOpen,
  CheckCircle,
  CreditCard,
  ShoppingCart,
  X
} from 'lucide-vue-next'
import { computed } from 'vue'
import type { AvailableCourseOffering } from '../../types'

interface Props {
  selectedOfferings: AvailableCourseOffering[]
  conflicts: Array<{
    offering1: AvailableCourseOffering
    offering2: AvailableCourseOffering
    reason: string
  }>
  isSubmitting?: boolean
  onRemove?: (offering: AvailableCourseOffering) => void
  onSubmit?: () => void
  onClear?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  isSubmitting: false,
})

// Computed properties
const totalCredits = computed(() => {
  return props.selectedOfferings.reduce((sum, offering) => {
    return sum + offering.unit.credit_hours
  }, 0)
})

const hasConflicts = computed(() => {
  return props.conflicts.length > 0
})

// const isUnderMinimum = computed(() => {
//   return totalCredits.value < props.creditLimits.minimum
// })

// const isOverMaximum = computed(() => {
//   return totalCredits.value > props.creditLimits.maximum
// })

// const isOverload = computed(() => {
//   return totalCredits.value > props.creditLimits.overload_threshold
// })

const canSubmit = computed(() => {
  return props.selectedOfferings.length > 0 &&
    !hasConflicts.value &&
    // !isUnderMinimum.value &&
    // !isOverMaximum.value &&
    !props.isSubmitting
})

// const creditStatus = computed(() => {
// if (isOverMaximum.value) return 'destructive'
// if (isOverload.value) return 'secondary'
// if (isUnderMinimum.value) return 'outline'
// return 'default'
// })

// const creditStatusText = computed(() => {
//   if (isOverMaximum.value) return 'Exceeds Maximum'
//   if (isOverload.value) return 'Overload'
//   if (isUnderMinimum.value) return 'Below Minimum'
//   return 'Valid'
// })

// Actions
const handleRemove = (offering: AvailableCourseOffering) => {
  if (props.onRemove) {
    props.onRemove(offering)
  }
}

const handleSubmit = () => {
  if (canSubmit.value && props.onSubmit) {
    props.onSubmit()
  }
}

const handleClear = () => {
  if (props.onClear) {
    props.onClear()
  }
}
</script>

<template>
  <Card class="sticky top-4">
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <ShoppingCart class="h-5 w-5" />
        Registration Cart
        <Badge v-if="selectedOfferings.length > 0" variant="secondary">
          {{ selectedOfferings.length }}
        </Badge>
      </CardTitle>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Empty State -->
      <div v-if="selectedOfferings.length === 0" class="text-center py-8">
        <ShoppingCart class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <p class="text-muted-foreground">No courses selected</p>
        <p class="text-sm text-muted-foreground">Add courses to your cart to register</p>
      </div>

      <!-- Selected Offerings -->
      <div v-else class="space-y-3">
        <div v-for="offering in selectedOfferings" :key="offering.id"
          class="flex items-start justify-between p-3 bg-muted/50 rounded-lg">
          <div class="flex-1 min-w-0">
            <div class="font-medium text-sm">
              {{ offering.unit.code }}
            </div>
            <div class="text-xs text-muted-foreground truncate">
              {{ offering.unit.name }}
            </div>
            <div class="flex items-center gap-2 mt-1">
              <BookOpen class="h-3 w-3 text-muted-foreground" />
              <span class="text-xs text-muted-foreground">
                {{ offering.unit.credit_hours }} credits
              </span>
            </div>
          </div>
          <Button variant="ghost" size="sm" @click="handleRemove(offering)" class="h-8 w-8 p-0 shrink-0"
            :disabled="isSubmitting">
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Separator v-if="selectedOfferings.length > 0" />

      <!-- Credit Summary -->
      <div v-if="selectedOfferings.length > 0" class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Total Credits</span>
          <div class="flex items-center gap-2">
            <span class="font-bold">{{ totalCredits }}</span>
            <!-- <Badge :variant="creditStatus" class="text-xs">
              {{ creditStatusText }}
            </Badge> -->
          </div>
        </div>

        <!-- Credit Limits Info -->
        <!-- <div class="text-xs text-muted-foreground space-y-1">
          <div>Minimum: {{ creditLimits.minimum }} credits</div>
          <div>Maximum: {{ creditLimits.maximum }} credits</div>
          <div>Overload threshold: {{ creditLimits.overload_threshold }} credits</div>
        </div> -->
      </div>

      <!-- Conflicts Warning -->
      <div v-if="hasConflicts" class="space-y-2">
        <Separator />
        <div class="space-y-2">
          <div class="flex items-center gap-2 text-destructive">
            <AlertTriangle class="h-4 w-4" />
            <span class="text-sm font-medium">Schedule Conflicts</span>
          </div>
          <div class="space-y-1">
            <div v-for="(conflict, index) in conflicts" :key="index"
              class="text-xs text-destructive bg-destructive/10 p-2 rounded">
              {{ conflict.offering1.unit.code }} conflicts with
              {{ conflict.offering2.unit.code }}: {{ conflict.reason }}
            </div>
          </div>
        </div>
      </div>

      <!-- Validation Messages -->
      <!-- <div v-if="selectedOfferings.length > 0" class="space-y-2">
        <div v-if="isUnderMinimum" class="flex items-center gap-2 text-yellow-600">
          <AlertTriangle class="h-4 w-4" />
          <span class="text-sm">Below minimum credit requirement</span>
        </div>
        <div v-if="isOverMaximum" class="flex items-center gap-2 text-destructive">
          <AlertTriangle class="h-4 w-4" />
          <span class="text-sm">Exceeds maximum credit limit</span>
        </div>
        <div v-if="isOverload && !isOverMaximum" class="flex items-center gap-2 text-yellow-600">
          <AlertTriangle class="h-4 w-4" />
          <span class="text-sm">Overload registration (requires approval)</span>
        </div>
      </div> -->

      <!-- Action Buttons -->
      <div v-if="selectedOfferings.length > 0" class="space-y-2 pt-2">
        <Button @click="handleSubmit" :disabled="!canSubmit" :loading="isSubmitting" class="w-full">
          <CreditCard class="h-4 w-4 mr-2" />
          {{ isSubmitting ? 'Submitting...' : 'Submit Registration' }}
        </Button>

        <Button variant="outline" @click="handleClear" :disabled="isSubmitting" class="w-full">
          Clear Cart
        </Button>
      </div>

      <!-- Success State -->
      <div v-if="canSubmit && !hasConflicts" class="flex items-center gap-2 text-green-600 text-sm">
        <CheckCircle class="h-4 w-4" />
        <span>Ready to submit</span>
      </div>
    </CardContent>
  </Card>
</template>
