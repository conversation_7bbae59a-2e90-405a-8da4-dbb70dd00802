<script setup lang="ts">
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/shared/components/ui/popover'
import { ScrollArea } from '@/shared/components/ui/scroll-area'
import { AlertCircle, Bell, Info, X } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'

// Mock notification data - in real app, this would come from a store
const notifications = ref([
  {
    id: '1',
    type: 'academic' as const,
    title: 'Assignment Due Soon',
    message: 'CS101 Assignment 2 is due in 2 days',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    read: false,
    priority: 'high' as const,
  },
  {
    id: '2',
    type: 'financial' as const,
    title: 'Payment Reminder',
    message: 'Semester fees payment due next week',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    read: false,
    priority: 'medium' as const,
  },
  {
    id: '3',
    type: 'academic' as const,
    title: 'Grade Posted',
    message: 'Your grade for MATH201 Quiz 3 has been posted',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    read: true,
    priority: 'low' as const,
  },
])

const isOpen = ref(false)

// Computed properties
const unreadCount = computed(() =>
  notifications.value.filter(n => !n.read).length
)

const sortedNotifications = computed(() =>
  [...notifications.value].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
)

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'academic':
      return Info
    case 'financial':
      return AlertCircle
    default:
      return Bell
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-500'
    case 'medium':
      return 'bg-yellow-500'
    case 'low':
      return 'bg-blue-500'
    default:
      return 'bg-gray-500'
  }
}

const formatTimestamp = (timestamp: Date) => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}m ago`
  } else if (hours < 24) {
    return `${hours}h ago`
  } else {
    return `${days}d ago`
  }
}

// Actions
const markAsRead = (notificationId: string) => {
  const notification = notifications.value.find(n => n.id === notificationId)
  if (notification) {
    notification.read = true
  }
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.read = true)
}

const removeNotification = (notificationId: string) => {
  const index = notifications.value.findIndex(n => n.id === notificationId)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

// Handle keyboard navigation
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})
</script>

<template>
  <Popover v-model:open="isOpen">
    <PopoverTrigger as-child>
      <Button variant="ghost" size="icon" class="relative"
        :aria-label="`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`">
        <Bell class="h-5 w-5" />
        <Badge v-if="unreadCount > 0" variant="destructive"
          class="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
          {{ unreadCount > 9 ? '9+' : unreadCount }}
        </Badge>
      </Button>
    </PopoverTrigger>

    <PopoverContent class="w-80 p-0" align="end" role="dialog" aria-label="Notifications">
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b">
        <h3 class="font-semibold">Notifications</h3>
        <div class="flex items-center gap-2">
          <Button v-if="unreadCount > 0" variant="ghost" size="sm" @click="markAllAsRead" class="text-xs">
            Mark all read
          </Button>
          <Button variant="ghost" size="icon" @click="isOpen = false" aria-label="Close notifications">
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>

      <!-- Notifications List -->
      <ScrollArea class="max-h-96">
        <div v-if="notifications.length === 0" class="p-4 text-center text-muted-foreground">
          No notifications
        </div>

        <div v-else class="divide-y">
          <div v-for="notification in sortedNotifications" :key="notification.id" :class="[
            'p-4 hover:bg-accent/50 transition-colors cursor-pointer',
            { 'bg-accent/20': !notification.read }
          ]" @click="markAsRead(notification.id)" role="button"
            :aria-label="`${notification.title}: ${notification.message}`" tabindex="0">
            <div class="flex items-start gap-3">
              <!-- Icon and Priority Indicator -->
              <div class="relative">
                <component :is="getNotificationIcon(notification.type)" class="h-5 w-5 text-muted-foreground mt-0.5" />
                <div :class="[
                  'absolute -top-1 -right-1 h-2 w-2 rounded-full',
                  getPriorityColor(notification.priority)
                ]" />
              </div>

              <!-- Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between gap-2">
                  <h4 :class="[
                    'font-medium text-sm truncate',
                    { 'font-semibold': !notification.read }
                  ]">
                    {{ notification.title }}
                  </h4>
                  <span class="text-xs text-muted-foreground whitespace-nowrap">
                    {{ formatTimestamp(notification.timestamp) }}
                  </span>
                </div>
                <p class="text-sm text-muted-foreground mt-1 line-clamp-2">
                  {{ notification.message }}
                </p>
                <div class="flex items-center justify-between mt-2">
                  <Badge variant="outline" class="text-xs">
                    {{ notification.type }}
                  </Badge>
                  <Button variant="ghost" size="sm" @click.stop="removeNotification(notification.id)"
                    class="h-6 w-6 p-0" :aria-label="`Remove ${notification.title} notification`">
                    <X class="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>

      <!-- Footer -->
      <div class="p-3 border-t">
        <Button variant="outline" size="sm" class="w-full">
          View All Notifications
        </Button>
      </div>
    </PopoverContent>
  </Popover>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
