<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle,
  Calendar,
  MapPin,
  User,
  FileText
} from 'lucide-vue-next'
import type { Attendance } from '../../types/models/attendance'
import type { ClassSession } from '../../types/models/course'

interface SessionAttendanceRecord {
  session: ClassSession
  attendance?: Attendance
  status: 'present' | 'absent' | 'late' | 'excused' | 'unknown'
  date: string
  notes?: string
}

interface Props {
  sessions: SessionAttendanceRecord[]
  courseOfferingId?: string
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Mock session data (in real app, this would come from API)
const mockSessions = computed(() => {
  const sessions: SessionAttendanceRecord[] = []
  const statuses: Array<'present' | 'absent' | 'late' | 'excused'> = ['present', 'present', 'present', 'late', 'present', 'absent', 'excused', 'present']
  
  for (let i = 0; i < 8; i++) {
    const date = new Date()
    date.setDate(date.getDate() - (7 - i) * 7) // Weekly sessions
    
    sessions.push({
      session: {
        id: `session-${i}`,
        course_offering_id: props.courseOfferingId || 'default',
        instructor_id: 'instructor-1',
        room_id: 'room-1',
        room_booking_id: 'booking-1',
        course_offering: {
          id: props.courseOfferingId || 'default',
          curriculum_unit_id: 'unit-1',
          semester_id: 'semester-1',
          lecture_id: 'lecturer-1',
          unit: {
            id: 'unit-1',
            code: 'CS101',
            name: 'Introduction to Computer Science',
            credit_points: 3
          },
          curriculum_unit: {
            id: 'unit-1',
            curriculum_version_id: 'cv-1',
            unit_id: 'unit-1',
            semester_id: 'semester-1',
            curriculum_version: {} as any,
            unit: {
              id: 'unit-1',
              code: 'CS101',
              name: 'Introduction to Computer Science',
              credit_points: 3
            },
            semester: {
              id: 'semester-1',
              name: 'Spring 2024',
              code: 'SP24',
              start_date: '2024-03-01',
              end_date: '2024-07-15',
              academic_year: '2024',
              is_current: true,
              registration_start: '2024-01-15',
              registration_end: '2024-02-29'
            }
          },
          semester: {
            id: 'semester-1',
            name: 'Spring 2024',
            code: 'SP24',
            start_date: '2024-03-01',
            end_date: '2024-07-15',
            academic_year: '2024',
            is_current: true,
            registration_start: '2024-01-15',
            registration_end: '2024-02-29'
          },
          lecturer: {
            id: 'lecturer-1',
            name: 'Dr. John Doe',
            email: '<EMAIL>'
          },
          class_sessions: []
        },
        instructor: {
          id: 'lecturer-1',
          name: 'Dr. John Doe',
          email: '<EMAIL>'
        },
        room: {
          id: 'room-1',
          campus_id: 'campus-1',
          name: 'Room 101',
          code: 'R101',
          campus: {
            id: 'campus-1',
            name: 'Main Campus',
            code: 'MAIN',
            address: '123 University Ave',
            buildings: []
          }
        }
      },
      status: statuses[i],
      date: date.toISOString().split('T')[0],
      notes: statuses[i] === 'excused' ? 'Medical certificate provided' : 
             statuses[i] === 'late' ? 'Arrived 15 minutes late' : undefined
    })
  }
  
  return sessions
})

const attendanceStats = computed(() => {
  const total = mockSessions.value.length
  const present = mockSessions.value.filter(s => s.status === 'present').length
  const late = mockSessions.value.filter(s => s.status === 'late').length
  const absent = mockSessions.value.filter(s => s.status === 'absent').length
  const excused = mockSessions.value.filter(s => s.status === 'excused').length
  
  return {
    total,
    present,
    late,
    absent,
    excused,
    attendanceRate: total > 0 ? Math.round(((present + late + excused) / total) * 100) : 0
  }
})

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'present': return CheckCircle
    case 'late': return Clock
    case 'absent': return XCircle
    case 'excused': return AlertCircle
    default: return Clock
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'present': return 'text-green-600'
    case 'late': return 'text-yellow-600'
    case 'absent': return 'text-red-600'
    case 'excused': return 'text-blue-600'
    default: return 'text-muted-foreground'
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'present': return 'default' as const
    case 'late': return 'outline' as const
    case 'absent': return 'destructive' as const
    case 'excused': return 'secondary' as const
    default: return 'outline' as const
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  })
}

const formatTime = (index: number) => {
  // Mock time based on session index
  const hour = 9 + Math.floor(index / 2)
  return `${hour}:00 - ${hour + 1}:00`
}
</script>

<template>
  <div class="space-y-6">
    <!-- Attendance Statistics -->
    <Card>
      <CardHeader>
        <CardTitle>Session Attendance Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div class="h-8 bg-muted animate-pulse rounded" />
          <div class="grid grid-cols-5 gap-4">
            <div v-for="i in 5" :key="i" class="h-16 bg-muted animate-pulse rounded" />
          </div>
        </div>
        
        <div v-else class="space-y-4">
          <!-- Overall Rate -->
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">
              {{ attendanceStats.attendanceRate }}%
            </div>
            <div class="text-sm text-muted-foreground">Attendance Rate</div>
          </div>
          
          <!-- Detailed Stats -->
          <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
            <div>
              <div class="text-xl font-bold text-muted-foreground">{{ attendanceStats.total }}</div>
              <div class="text-sm text-muted-foreground">Total Sessions</div>
            </div>
            <div>
              <div class="text-xl font-bold text-green-600">{{ attendanceStats.present }}</div>
              <div class="text-sm text-muted-foreground">Present</div>
            </div>
            <div>
              <div class="text-xl font-bold text-yellow-600">{{ attendanceStats.late }}</div>
              <div class="text-sm text-muted-foreground">Late</div>
            </div>
            <div>
              <div class="text-xl font-bold text-red-600">{{ attendanceStats.absent }}</div>
              <div class="text-sm text-muted-foreground">Absent</div>
            </div>
            <div>
              <div class="text-xl font-bold text-blue-600">{{ attendanceStats.excused }}</div>
              <div class="text-sm text-muted-foreground">Excused</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Session List -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Session History</h3>
      
      <div v-if="isLoading" class="space-y-3">
        <div v-for="i in 6" :key="i" class="h-20 bg-muted animate-pulse rounded-lg" />
      </div>
      
      <div v-else-if="mockSessions.length > 0" class="space-y-3">
        <Card
          v-for="(sessionRecord, index) in mockSessions"
          :key="sessionRecord.session.id"
          class="transition-all duration-200 hover:shadow-md"
        >
          <CardContent class="p-4">
            <div class="flex items-center justify-between">
              <!-- Session Info -->
              <div class="flex items-center gap-3 flex-1">
                <component 
                  :is="getStatusIcon(sessionRecord.status)" 
                  :class="['h-5 w-5', getStatusColor(sessionRecord.status)]"
                />
                <div class="min-w-0 flex-1">
                  <h4 class="font-medium">
                    Session {{ index + 1 }} - {{ sessionRecord.session.course_offering.curriculum_unit.unit.code }}
                  </h4>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                    <div class="flex items-center gap-1">
                      <Calendar class="h-4 w-4" />
                      <span>{{ formatDate(sessionRecord.date) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <Clock class="h-4 w-4" />
                      <span>{{ formatTime(index) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <MapPin class="h-4 w-4" />
                      <span>{{ sessionRecord.session.room.name }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <User class="h-4 w-4" />
                      <span>{{ sessionRecord.session.instructor.email }}</span>
                    </div>
                  </div>
                  
                  <!-- Notes -->
                  <div v-if="sessionRecord.notes" class="mt-2 text-sm text-muted-foreground">
                    <div class="flex items-start gap-1">
                      <FileText class="h-4 w-4 mt-0.5" />
                      <span>{{ sessionRecord.notes }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Status Badge -->
              <Badge :variant="getStatusVariant(sessionRecord.status)" class="shrink-0">
                {{ sessionRecord.status.charAt(0).toUpperCase() + sessionRecord.status.slice(1) }}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Calendar class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <p class="text-muted-foreground">No session records available</p>
        <p class="text-sm text-muted-foreground">
          Session attendance will be tracked here once classes begin
        </p>
      </div>
    </div>
  </div>
</template>
