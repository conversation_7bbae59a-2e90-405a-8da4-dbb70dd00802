<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/shared/stores/auth'
import { useDashboardStore } from '@/student/stores/dashboard'
import {
  SemesterCard,
  CreditProgress,
  GPADisplay,
  HoldsAlert,
  AlertNotifications,
} from '@/student/components/dashboard'

const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// Load dashboard data using the store
const loadDashboardData = async () => {
  await dashboardStore.fetchDashboardData()
}

onMounted(() => {
  loadDashboardData()
})
</script>

<template>
  <!-- Welcome Header -->
  <div class="space-y-2">
    <h1 class="text-3xl font-bold">Welcome back, {{ authStore.user?.name || 'Student' }}!</h1>
    <p class="text-muted-foreground">
      Here's an overview of your academic progress and important updates.
    </p>
  </div>

  <!-- Alerts Section -->
  <div class="space-y-4">
    <HoldsAlert :holds="dashboardStore.academicHolds" :is-loading="dashboardStore.isLoading" />
    <AlertNotifications
      :notifications="dashboardStore.notifications"
      :is-loading="dashboardStore.isLoading"
    />
  </div>

  <!-- Main Dashboard Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    <!-- Semester Information -->
    <div class="lg:col-span-1">
      <SemesterCard
        :semester="dashboardStore.currentSemester"
        :is-loading="dashboardStore.isLoading"
      />
    </div>

    <!-- Credit Progress -->
    <div class="lg:col-span-1">
      <CreditProgress
        :credit-progress="dashboardStore.creditProgress"
        :is-loading="dashboardStore.isLoading"
      />
    </div>

    <!-- GPA Display -->
    <div class="lg:col-span-1 xl:col-span-1">
      <GPADisplay :gpa-data="dashboardStore.currentGPA" :is-loading="dashboardStore.isLoading" />
    </div>
  </div>
</template>
