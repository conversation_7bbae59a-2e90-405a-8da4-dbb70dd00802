<script setup lang="ts">
import { useCourseStore } from '../stores/course'
import { onMounted, ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import type { CourseRegisteredResponse } from '../types/api/responses'
import { 
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import ConfirmModal from '@/shared/components/ui/ConfirmModal.vue'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import EmptyState from '@/shared/components/ui/EmptyState.vue'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Skeleton } from '@/shared/components/ui/skeleton'
import { 
  Calendar,
  Clock,
  User,
  BookOpen,
  GraduationCap,
  AlertCircle,
  Trash2,
  RefreshCw
} from 'lucide-vue-next'

const courseStore = useCourseStore()

// Store references
const { registeredCourseOfferings, isLoading, error } = storeToRefs(courseStore)

// Component state
const selectedCourseForDrop = ref<CourseRegisteredResponse | null>(null)
const isDropModalOpen = ref(false)
const isDropping = ref(false)

// Computed properties
const hasRegisteredCourses = computed(() => registeredCourseOfferings.value.length > 0)

// Format date helper
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-AU', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Get status badge variant
const getStatusVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case 'registered': return 'default'
    case 'dropped': return 'destructive'
    case 'completed': return 'secondary'
    case 'in_progress': return 'outline'
    default: return 'outline'
  }
}

// Handle course drop action
const handleDropCourse = (course: CourseRegisteredResponse) => {
  const dropAction = course.actions.find(action => action.action === 'drop')
  if (!dropAction) {
    alert('This course cannot be dropped at this time')
    return
  }

  selectedCourseForDrop.value = course
  isDropModalOpen.value = true
}

// Confirm course drop
const confirmDropCourse = async () => {
  if (!selectedCourseForDrop.value) return

  isDropping.value = true
  try {
    const result = await courseStore.dropCourse(selectedCourseForDrop.value.id)
    
    if (result.success) {
      isDropModalOpen.value = false
      selectedCourseForDrop.value = null
      // Course will be removed from the list automatically by the store
    } else {
      alert(result.message)
    }
  } catch (err) {
    alert('Failed to drop course. Please try again.')
  } finally {
    isDropping.value = false
  }
}

// Cancel course drop
const cancelDropCourse = () => {
  isDropModalOpen.value = false
  selectedCourseForDrop.value = null
}

// Refresh registrations
const refreshRegistrations = async () => {
  await courseStore.fetchRegisteredCourses()
}

// Check if course can be dropped
const canDropCourse = (course: CourseRegisteredResponse) => {
  return course.actions.some(action => action.action === 'drop')
}

// Initialize data
onMounted(() => {
  courseStore.fetchRegisteredCourses()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">My Course Registrations</h1>
        <p class="text-muted-foreground mt-2">
          Manage your registered courses and view enrollment details
        </p>
      </div>
      <Button
        variant="outline"
        @click="refreshRegistrations"
        :disabled="isLoading"
        class="gap-2"
      >
        <RefreshCw :class="['h-4 w-4', { 'animate-spin': isLoading }]" />
        Refresh
      </Button>
    </div>

    <!-- Error State -->
    <Alert v-if="error && !isLoading" variant="destructive">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>
        {{ error }}
      </AlertDescription>
    </Alert>

    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-4">
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card v-for="n in 6" :key="n" class="h-64">
          <CardHeader class="space-y-2">
            <Skeleton class="h-4 w-20" />
            <Skeleton class="h-6 w-full" />
          </CardHeader>
          <CardContent class="space-y-3">
            <Skeleton class="h-3 w-24" />
            <Skeleton class="h-3 w-32" />
            <Skeleton class="h-3 w-28" />
            <div class="flex justify-between items-center pt-4">
              <Skeleton class="h-5 w-16" />
              <Skeleton class="h-9 w-20" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Empty State -->
    <EmptyState
      v-else-if="!hasRegisteredCourses && !isLoading"
      title="No Course Registrations"
      description="You haven't registered for any courses yet. Visit the course catalog to find and register for available courses."
      icon="book"
    >
      <template #actions>
        <Button variant="outline" @click="refreshRegistrations">
          <RefreshCw class="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </template>
    </EmptyState>

    <!-- Course Registrations Grid -->
    <div 
      v-else-if="hasRegisteredCourses"
      class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
    >
      <Card 
        v-for="course in registeredCourseOfferings" 
        :key="course.id"
        class="hover:shadow-md transition-shadow"
      >
        <CardHeader class="pb-4">
          <div class="flex items-start justify-between gap-2">
            <div class="space-y-2">
              <div class="text-sm font-medium text-muted-foreground">
                {{ course.unit.code }}
              </div>
              <CardTitle class="text-lg leading-tight">
                {{ course.unit.name }}
              </CardTitle>
            </div>
            <Badge 
              :variant="getStatusVariant(course.status)"
              class="shrink-0"
            >
              {{ course.status_display }}
            </Badge>
          </div>
        </CardHeader>

        <CardContent class="space-y-4">
          <!-- Course Details -->
          <div class="space-y-3 text-sm">
            <!-- Credit Hours -->
            <div class="flex items-center gap-2">
              <GraduationCap class="h-4 w-4 text-muted-foreground" />
              <span class="text-muted-foreground">Credit Hours:</span>
              <span class="font-medium">{{ course.unit.credit_hours }}</span>
            </div>

            <!-- Semester -->
            <div class="flex items-center gap-2">
              <BookOpen class="h-4 w-4 text-muted-foreground" />
              <span class="text-muted-foreground">Semester:</span>
              <span class="font-medium">{{ course.semester.name }}</span>
            </div>

            <!-- Lecturer -->
            <div class="flex items-center gap-2">
              <User class="h-4 w-4 text-muted-foreground" />
              <span class="text-muted-foreground">Lecturer:</span>
              <span class="font-medium">
                {{ course.lecturer.name || 'TBA' }}
              </span>
            </div>

            <!-- Registration Date -->
            <div class="flex items-center gap-2">
              <Calendar class="h-4 w-4 text-muted-foreground" />
              <span class="text-muted-foreground">Registered:</span>
              <span class="font-medium">{{ formatDate(course.registration_date) }}</span>
            </div>

            <!-- Drop Date (if applicable) -->
            <div v-if="course.drop_date" class="flex items-center gap-2">
              <Clock class="h-4 w-4 text-muted-foreground" />
              <span class="text-muted-foreground">Dropped:</span>
              <span class="font-medium">{{ formatDate(course.drop_date) }}</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="pt-2 border-t">
            <Button
              v-if="canDropCourse(course)"
              variant="outline"
              size="sm"
              @click="handleDropCourse(course)"
              class="w-full text-destructive hover:text-destructive hover:bg-destructive/10"
            >
              <Trash2 class="h-4 w-4 mr-2" />
              Drop Course
            </Button>
            <div
              v-else
              class="text-xs text-muted-foreground text-center py-2"
            >
              No actions available
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Drop Course Confirmation Modal -->
    <ConfirmModal
      v-model:open="isDropModalOpen"
      type="danger"
      icon="delete"
      title="Drop Course"
      :description="`Are you sure you want to drop ${selectedCourseForDrop?.unit.name}? This action cannot be undone and may affect your academic progress.`"
      confirm-text="Drop Course"
      cancel-text="Cancel"
      :loading="isDropping"
      @confirm="confirmDropCourse"
      @cancel="cancelDropCourse"
    >
      <div v-if="selectedCourseForDrop" class="bg-muted/50 rounded-lg p-4 space-y-2">
        <div class="font-semibold">Course Details:</div>
        <div class="text-sm space-y-1">
          <div><strong>Code:</strong> {{ selectedCourseForDrop.unit.code }}</div>
          <div><strong>Name:</strong> {{ selectedCourseForDrop.unit.name }}</div>
          <div><strong>Credit Hours:</strong> {{ selectedCourseForDrop.unit.credit_hours }}</div>
          <div><strong>Semester:</strong> {{ selectedCourseForDrop.semester.name }}</div>
        </div>
      </div>
    </ConfirmModal>
  </div>
</template>
