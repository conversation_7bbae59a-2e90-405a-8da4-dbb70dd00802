<script setup lang="ts">
import { Badge } from '@/shared/components/ui/badge'
import { Search } from 'lucide-vue-next'
import { computed, onMounted } from 'vue'
import { toast } from 'vue-sonner'
import { ConflictDetection, CourseOfferingCard, RegistrationCart } from '../components/registration'
import { useCourseStore } from '../stores/course'
import type { AvailableCourseOffering } from '../types'

const courseStore = useCourseStore()
// Computed properties
const filteredOfferings = computed(() => {
  console.log('offerings', courseStore.availableCourseOfferingsFiltered);
  return courseStore.availableCourseOfferingsFiltered
})

const conflicts = computed(() => {
  // Mock conflict detection - in real app, this would use the store's conflict detection
  return []
})

// Actions
const handleAddOffering = (offering: AvailableCourseOffering) => {
  courseStore.addAvailableCourseOffering(offering)
}

const handleRemoveOffering = (offering: AvailableCourseOffering) => {
  courseStore.removeAvailableCourseOffering(offering.id)
}

const handleSubmitRegistration = async () => {
  // This would call the store's submit registration method
  const response = await courseStore.submitRegistration()
  if (response.success) {
    toast.success(response.message)
    loadCourseOfferings()
    handleClearCart()
  } else {
    toast.error(response.message)
  }
}

const handleClearCart = () => {
  courseStore.selectedCourseOfferings = []
}

const loadCourseOfferings = async () => {
  await courseStore.fetchAvailableOfferings()
}

// Lifecycle
onMounted(() => {
  loadCourseOfferings()
})
</script>

<template>
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Course Registration</h1>
      <p class="text-muted-foreground">Browse and register for available course offerings</p>
    </div>
    <Badge v-if="courseStore.registrationPeriod?.is_open" variant="default">
      Registration Open
    </Badge>
    <Badge v-else variant="secondary"> Registration Closed </Badge>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Course Offerings List -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Conflict Detection -->
      <ConflictDetection :conflicts="conflicts" />

      <!-- Loading State -->
      <div v-if="courseStore.isLoading" class="space-y-4">
        <div v-for="i in 3" :key="i" class="h-48 bg-muted animate-pulse rounded-lg" />
      </div>

      <!-- Course Offerings -->
      <div v-else-if="filteredOfferings.length > 0" class="space-y-4">
        <CourseOfferingCard v-for="offering in filteredOfferings" :key="offering.id" :offering="offering"
          :is-selected="courseStore.selectedCourseOfferings.some((o: AvailableCourseOffering) => o.id === offering.id)"
          :is-registered="false" :is-registration-open="offering.registration_eligibility.can_register"
          :on-add="handleAddOffering" :on-remove="handleRemoveOffering" />
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <div class="text-muted-foreground mb-4">
          <Search class="h-12 w-12 mx-auto mb-4" />
          <p>No course offerings found</p>
          <p class="text-sm">Try adjusting your search or filters</p>
        </div>
      </div>
    </div>

    <!-- Registration Cart -->
    <div class="lg:col-span-1">
      <RegistrationCart :selected-offerings="courseStore.selectedCourseOfferings" :conflicts="conflicts"
        :is-submitting="courseStore.registrationStatus === 'registering'" :on-remove="handleRemoveOffering"
        :on-submit="handleSubmitRegistration" :on-clear="handleClearCart" />
    </div>
  </div>
</template>
