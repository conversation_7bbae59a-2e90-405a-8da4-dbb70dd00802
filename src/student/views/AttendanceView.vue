<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAttendanceStore } from '../stores/attendance'
import { Button } from '@/shared/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { AttendanceOverview, AttendanceAlerts, SessionAttendance } from '../components/attendance'
import { Users, AlertTriangle, Calendar, Download, Filter } from 'lucide-vue-next'

const attendanceStore = useAttendanceStore()

// Local state
const selectedCourse = ref<string>('all')
const activeTab = ref('overview')

// Mock course options (in real app, this would come from API)
const courseOptions = ref([
  { id: 'all', name: 'All Courses' },
  { id: 'cs101', name: 'CS101 - Introduction to Computer Science' },
  { id: 'math201', name: 'MATH201 - Calculus II' },
  { id: 'phys101', name: 'PHYS101 - Physics I' },
])

// Computed properties
const filteredSummary = computed(() => {
  if (selectedCourse.value === 'all') {
    return attendanceStore.attendanceSummary
  }
  return attendanceStore.attendanceSummary.filter(
    (summary) => summary.course_offering.id === selectedCourse.value,
  )
})

// Actions
const loadAttendance = async () => {
  await attendanceStore.fetchAttendance()
}

const handleCourseChange = (value: string | number | bigint | null | Record<string, any>) => {
  if (typeof value === 'string') {
    selectedCourse.value = value
    if (value !== 'all') {
      attendanceStore.fetchAttendanceForOffering(value)
    }
  }
}

const handleDismissAlert = (alertIndex: number) => {
  // Mock dismiss functionality
  console.log('Dismissing alert:', alertIndex)
}

const handleContactLecturer = (contactInfo: any) => {
  // Mock contact functionality
  console.log('Contacting lecturer:', contactInfo)
}

const handleExport = () => {
  // Mock export functionality
  console.log('Exporting attendance data...')
}

// Lifecycle
onMounted(() => {
  loadAttendance()
})
</script>

<template>
  <!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
    <div>
      <h1 class="text-3xl font-bold">Attendance Monitoring</h1>
      <p class="text-muted-foreground">
        Track your class attendance and manage attendance requirements
      </p>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center gap-2">
      <!-- Course Filter -->
      <Select v-model="selectedCourse" @update:model-value="handleCourseChange">
        <SelectTrigger class="w-64">
          <Filter class="h-4 w-4 mr-2" />
          <SelectValue placeholder="Select course" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem v-for="course in courseOptions" :key="course.id" :value="course.id">
            {{ course.name }}
          </SelectItem>
        </SelectContent>
      </Select>

      <!-- Export Button -->
      <Button variant="outline" size="sm" @click="handleExport">
        <Download class="h-4 w-4 mr-2" />
        Export
      </Button>
    </div>
  </div>

  <!-- Attendance Alerts -->
  <div v-if="attendanceStore.hasAttendanceIssues" class="mb-6">
    <AttendanceAlerts
      :alerts="attendanceStore.attendanceAlerts"
      :on-dismiss="handleDismissAlert"
      :on-contact="handleContactLecturer"
    />
  </div>

  <!-- Main Content -->
  <Tabs v-model="activeTab" class="space-y-6">
    <TabsList class="grid w-full grid-cols-3">
      <TabsTrigger value="overview" class="flex items-center gap-2">
        <Users class="h-4 w-4" />
        Overview
      </TabsTrigger>
      <TabsTrigger value="sessions" class="flex items-center gap-2">
        <Calendar class="h-4 w-4" />
        Sessions
      </TabsTrigger>
      <TabsTrigger value="alerts" class="flex items-center gap-2">
        <AlertTriangle class="h-4 w-4" />
        Alerts
      </TabsTrigger>
    </TabsList>

    <!-- Overview Tab -->
    <TabsContent value="overview" class="space-y-6">
      <AttendanceOverview
        :attendance-summary="filteredSummary"
        :overall-attendance-rate="attendanceStore.overallAttendanceRate"
        :is-loading="attendanceStore.isLoading"
      />
    </TabsContent>

    <!-- Sessions Tab -->
    <TabsContent value="sessions" class="space-y-6">
      <SessionAttendance
        :sessions="[]"
        :course-offering-id="selectedCourse === 'all' ? undefined : selectedCourse"
        :is-loading="attendanceStore.isLoading"
      />
    </TabsContent>

    <!-- Alerts Tab -->
    <TabsContent value="alerts" class="space-y-6">
      <AttendanceAlerts
        :alerts="attendanceStore.attendanceAlerts"
        :on-dismiss="handleDismissAlert"
        :on-contact="handleContactLecturer"
      />
    </TabsContent>
  </Tabs>

  <!-- Error State -->
  <div v-if="attendanceStore.error" class="text-center py-12">
    <div class="text-destructive mb-4">
      <Users class="h-12 w-12 mx-auto mb-4" />
      <p>Failed to load attendance data</p>
      <p class="text-sm">{{ attendanceStore.error }}</p>
    </div>
    <Button @click="loadAttendance" variant="outline"> Try Again </Button>
  </div>
</template>
