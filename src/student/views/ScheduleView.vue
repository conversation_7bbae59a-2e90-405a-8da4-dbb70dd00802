<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTimetableStore } from '../stores/timetable'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { TimetableGrid, WeekNavigation, DayView } from '../components/timetable'
import { Calendar, Grid3X3, List, Download, Share } from 'lucide-vue-next'

const timetableStore = useTimetableStore()

// Local state
const viewMode = ref<'week' | 'day'>('week')
const currentWeekStart = ref(new Date())
const selectedDate = ref(new Date())

// Set current week start to Monday
const setWeekStart = (date: Date) => {
  const monday = new Date(date)
  const day = monday.getDay()
  const diff = monday.getDate() - day + (day === 0 ? -6 : 1) // Adjust when day is Sunday
  monday.setDate(diff)
  monday.setHours(0, 0, 0, 0)
  return monday
}

// Initialize current week
onMounted(() => {
  currentWeekStart.value = setWeekStart(new Date())
  selectedDate.value = new Date()
  loadTimetable()
})

// Computed properties
const isCurrentWeek = computed(() => {
  const today = new Date()
  const weekStart = new Date(currentWeekStart.value)
  const weekEnd = new Date(currentWeekStart.value)
  weekEnd.setDate(weekStart.getDate() + 6)

  return today >= weekStart && today <= weekEnd
})

const isMobile = computed(() => {
  // In a real app, this would be reactive based on screen size
  return window.innerWidth < 768
})

// Actions
const loadTimetable = async () => {
  if (timetableStore.currentSemester) {
    const weekStart = currentWeekStart.value.toISOString().split('T')[0]
    await timetableStore.fetchTimetable(timetableStore.currentSemester.id, weekStart)
  }
}

const handlePreviousWeek = () => {
  const newWeek = new Date(currentWeekStart.value)
  newWeek.setDate(newWeek.getDate() - 7)
  currentWeekStart.value = newWeek
  loadTimetable()
}

const handleNextWeek = () => {
  const newWeek = new Date(currentWeekStart.value)
  newWeek.setDate(newWeek.getDate() + 7)
  currentWeekStart.value = newWeek
  loadTimetable()
}

const handleToday = () => {
  currentWeekStart.value = setWeekStart(new Date())
  selectedDate.value = new Date()
  loadTimetable()
}

const handlePreviousDay = () => {
  const newDate = new Date(selectedDate.value)
  newDate.setDate(newDate.getDate() - 1)
  selectedDate.value = newDate
}

const handleNextDay = () => {
  const newDate = new Date(selectedDate.value)
  newDate.setDate(newDate.getDate() + 1)
  selectedDate.value = newDate
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'week' ? 'day' : 'week'
}

const handleExport = () => {
  // Mock export functionality
  console.log('Exporting timetable...')
}

const handleShare = () => {
  // Mock share functionality
  console.log('Sharing timetable...')
}
</script>

<template>
  <!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
    <div>
      <h1 class="text-3xl font-bold">My Schedule</h1>
      <p class="text-muted-foreground">View your class timetable and upcoming sessions</p>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center gap-2">
      <!-- View Mode Toggle -->
      <Button variant="outline" size="sm" @click="toggleViewMode" class="hidden sm:flex">
        <component :is="viewMode === 'week' ? List : Grid3X3" class="h-4 w-4 mr-2" />
        {{ viewMode === 'week' ? 'Day View' : 'Week View' }}
      </Button>

      <!-- Export Button -->
      <Button variant="outline" size="sm" @click="handleExport">
        <Download class="h-4 w-4 mr-2" />
        <span class="hidden sm:inline">Export</span>
      </Button>

      <!-- Share Button -->
      <Button variant="outline" size="sm" @click="handleShare">
        <Share class="h-4 w-4 mr-2" />
        <span class="hidden sm:inline">Share</span>
      </Button>
    </div>
  </div>

  <!-- Week View -->
  <div v-if="viewMode === 'week'" class="space-y-6">
    <!-- Week Navigation -->
    <WeekNavigation
      :current-week="currentWeekStart"
      :on-previous-week="handlePreviousWeek"
      :on-next-week="handleNextWeek"
      :on-today="handleToday"
    />

    <!-- Timetable Grid -->
    <TimetableGrid
      :sessions="timetableStore.classSessions"
      :week-start="currentWeekStart"
      :is-loading="timetableStore.isLoading"
    />
  </div>

  <!-- Day View -->
  <div v-else class="space-y-6">
    <!-- Day View Component -->
    <DayView
      :sessions="timetableStore.classSessions"
      :selected-date="selectedDate"
      :is-loading="timetableStore.isLoading"
      :on-previous-day="handlePreviousDay"
      :on-next-day="handleNextDay"
    />
  </div>

  <!-- Mobile View Mode Toggle -->
  <div class="fixed bottom-4 right-4 sm:hidden">
    <Button @click="toggleViewMode" size="lg" class="rounded-full shadow-lg">
      <component :is="viewMode === 'week' ? List : Grid3X3" class="h-5 w-5" />
    </Button>
  </div>

  <!-- Error State -->
  <div v-if="timetableStore.error" class="text-center py-12">
    <div class="text-destructive mb-4">
      <Calendar class="h-12 w-12 mx-auto mb-4" />
      <p>Failed to load timetable</p>
      <p class="text-sm">{{ timetableStore.error }}</p>
    </div>
    <Button @click="loadTimetable" variant="outline"> Try Again </Button>
  </div>
</template>
