<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '@/shared/components/ui/button'
import { CurriculumView } from '../components/curriculum'
import { BookOpen, Download, Share } from 'lucide-vue-next'
import type { CurriculumVersion, CurriculumUnit } from '../types/models/student'

// Mock data for demonstration
const curriculumVersion = ref<CurriculumVersion | null>(null)
const curriculumUnits = ref<CurriculumUnit[]>([])
const completedUnits = ref<string[]>([])
const isLoading = ref(true)

// Mock curriculum data
const loadCurriculumData = async () => {
  isLoading.value = true

  try {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Mock curriculum version
    curriculumVersion.value = {
      id: 'cv-1',
      program_id: 'prog-1',
      specialization_id: 'spec-1',
      semester_id: 'sem-1',
      program: {
        id: 'prog-1',
        name: 'Bachelor of Computer Science',
        code: 'BCS',
        description: 'Comprehensive computer science program',
        degree_type: 'bachelor' as const,
        duration_years: 4,
        total_credits: 144,
      },
      specialization: {
        id: 'spec-1',
        program_id: 'prog-1',
        name: 'Software Engineering',
        code: 'SE',
        required_credits: 144,
        program: {
          id: 'prog-1',
          name: 'Bachelor of Computer Science',
          code: 'BCS',
          description: 'Comprehensive computer science program',
          degree_type: 'bachelor' as const,
          duration_years: 4,
          total_credits: 144,
        },
      },
      semester: {
        id: 'sem-1',
        name: 'Spring 2024',
        code: 'SP24',
        start_date: '2024-03-01',
        end_date: '2024-07-15',
        academic_year: '2024',
        is_current: true,
        registration_start: '2024-01-15',
        registration_end: '2024-02-29',
      },
    }

    // Mock curriculum units
    const mockUnits = [
      { code: 'CS101', name: 'Introduction to Programming', credits: 3, semester: 'Semester 1' },
      { code: 'MATH101', name: 'Mathematics for Computing', credits: 3, semester: 'Semester 1' },
      { code: 'ENG101', name: 'Technical Communication', credits: 3, semester: 'Semester 1' },
      { code: 'CS102', name: 'Data Structures', credits: 3, semester: 'Semester 2' },
      { code: 'CS103', name: 'Computer Systems', credits: 3, semester: 'Semester 2' },
      { code: 'MATH201', name: 'Discrete Mathematics', credits: 3, semester: 'Semester 2' },
      { code: 'CS201', name: 'Algorithms', credits: 3, semester: 'Semester 3' },
      { code: 'CS202', name: 'Database Systems', credits: 3, semester: 'Semester 3' },
      { code: 'CS203', name: 'Software Engineering', credits: 3, semester: 'Semester 3' },
      { code: 'CS301', name: 'Advanced Programming', credits: 3, semester: 'Semester 4' },
      { code: 'CS302', name: 'Web Development', credits: 3, semester: 'Semester 4' },
      { code: 'ELEC301', name: 'Technical Elective 1', credits: 3, semester: 'Semester 4' },
    ]

    curriculumUnits.value = mockUnits.map((unit, index) => ({
      id: `cu-${index + 1}`,
      curriculum_version_id: 'cv-1',
      unit_id: `unit-${index + 1}`,
      semester_id: `sem-${Math.floor(index / 3) + 1}`,
      curriculum_version: curriculumVersion.value!,
      unit: {
        id: `unit-${index + 1}`,
        code: unit.code,
        name: unit.name,
        credit_points: unit.credits,
      },
      semester: {
        id: `sem-${Math.floor(index / 3) + 1}`,
        name: unit.semester,
        code: `S${Math.floor(index / 3) + 1}`,
        start_date: '2024-03-01',
        end_date: '2024-07-15',
        academic_year: '2024',
        is_current: Math.floor(index / 3) + 1 === 1,
        registration_start: '2024-01-15',
        registration_end: '2024-02-29',
      },
    }))

    // Mock completed units (first 6 units)
    completedUnits.value = curriculumUnits.value.slice(0, 6).map((unit) => unit.unit.id)
  } catch (error) {
    console.error('Failed to load curriculum data:', error)
  } finally {
    isLoading.value = false
  }
}

// Actions
const handleExport = () => {
  console.log('Exporting curriculum roadmap...')
}

const handleShare = () => {
  console.log('Sharing curriculum roadmap...')
}

// Lifecycle
onMounted(() => {
  loadCurriculumData()
})
</script>

<template>
  <!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
    <div>
      <h1 class="text-3xl font-bold">Curriculum Roadmap</h1>
      <p class="text-muted-foreground">Track your academic progress and plan your study path</p>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center gap-2">
      <Button variant="outline" size="sm" @click="handleExport">
        <Download class="h-4 w-4 mr-2" />
        Export
      </Button>
      <Button variant="outline" size="sm" @click="handleShare">
        <Share class="h-4 w-4 mr-2" />
        Share
      </Button>
    </div>
  </div>

  <!-- Curriculum View -->
  <CurriculumView
    :curriculum-version="curriculumVersion"
    :curriculum-units="curriculumUnits"
    :completed-units="completedUnits"
    :is-loading="isLoading"
  />

  <!-- Error State -->
  <div v-if="!isLoading && !curriculumVersion" class="text-center py-12">
    <div class="text-destructive mb-4">
      <BookOpen class="h-12 w-12 mx-auto mb-4" />
      <p>Failed to load curriculum data</p>
      <p class="text-sm">Please try again later</p>
    </div>
    <Button @click="loadCurriculumData" variant="outline"> Try Again </Button>
  </div>
</template>
