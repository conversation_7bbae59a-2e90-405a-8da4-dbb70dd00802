import { createFetch } from '@vueuse/core'
import { ref, computed, readonly } from 'vue'
import { useAuthStore } from '@/shared/stores/auth'
import type { ApiResponse } from '../types/api/api'
import type { ClassSession, CourseOffering } from '@/lecturer/types'
import type { Unit } from '@/student/types'

// Enhanced error types for better categorization
export interface ApiError extends Error {
  code?: string
  status?: number
  category: 'network' | 'authentication' | 'authorization' | 'validation' | 'server' | 'client'
  retryable: boolean
  context?: Record<string, unknown>
}

export interface RequestConfig extends RequestInit {
  retries?: number
  retryDelay?: number
  timeout?: number
  skipAuth?: boolean
  skipRetry?: boolean
}

export interface LoadingState {
  isLoading: boolean
  loadingMessage?: string
}

// Specialized API response types
export interface DashboardData {
  current_semester: {
    name: string
    start_date: string
    end_date: string
    academic_calendar_url: string
  }
  credit_progress: {
    earned_credits: number
    required_credits: number
    remaining_requirements: string[]
  }
  current_gpa: {
    gpa: number
    rank?: number
    trend: 'up' | 'down' | 'stable'
  }
  academic_holds: Array<{
    id: string
    type: string
    title: string
    description: string
    severity: 'low' | 'medium' | 'high' | 'critical'
  }>
  upcoming_deadlines: Array<{
    id: string
    title: string
    due_date: string
    course_code: string
  }>
}

export interface CourseData {
  id: string
  code: string
  name: string
  credits: number
  instructor: string
  schedule: Array<{
    day: string
    start_time: string
    end_time: string
    location: string
  }>
  enrollment_capacity: number
  enrolled_count: number
  status: 'open' | 'closed' | 'waitlist'
}

export interface GradeData {
  course_id: string
  course_code: string
  course_name: string
  grade: string
  points: number
  semester: string
  year: number
  date_recorded: string
}

export const useApi = () => {
  const authStore = useAuthStore()
  const baseURL = import.meta.env.VITE_API_BASE_URL

  // Global loading state
  const globalLoading = ref<LoadingState>({ isLoading: false })
  const requestQueue = ref<Set<string>>(new Set())

  // Request/Response interceptors
  const requestInterceptors: Array<(config: RequestConfig) => RequestConfig | Promise<RequestConfig>> = []
  const responseInterceptors: Array<(response: unknown) => unknown | Promise<unknown>> = []
  const errorInterceptors: Array<(error: ApiError) => ApiError | Promise<ApiError>> = []

  const useFetch = createFetch({
    options: {
      async beforeFetch({ options, url }) {
        // Apply request interceptors
        let config = options as RequestConfig
        for (const interceptor of requestInterceptors) {
          config = await interceptor(config)
        }

        // Add default headers
        config.headers = {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          ...(config.headers as Record<string, string>),
        }

        // Add authorization header if token exists and not skipped
        if (authStore.token && !config.skipAuth) {
          ;(config.headers as Record<string, string>).Authorization = `Bearer ${authStore.token}`
        }

        return { options: config, url }
      },
      async afterFetch({ data, response }) {
        // Apply response interceptors
        let processedData = data
        for (const interceptor of responseInterceptors) {
          processedData = await interceptor(processedData)
        }

        return { data: processedData, response }
      },
      onFetchError({ error, response }) {
        // Create enhanced error
        const apiError = createApiError(error, response)

        // Apply error interceptors
        errorInterceptors.forEach(async (interceptor) => {
          await interceptor(apiError)
        })

        return { error: apiError }
      },
    },
    fetchOptions: {
      mode: 'cors',
    },
  })

  const updateGlobalLoading = () => {
    globalLoading.value.isLoading = requestQueue.value.size > 0
  }

  const createApiError = (error: Error, response?: Response | null): ApiError => {
    const apiError = error as ApiError
    const status = response?.status || 0

    // Categorize errors
    if (status === 401) {
      apiError.category = 'authentication'
      apiError.retryable = true
    } else if (status === 403) {
      apiError.category = 'authorization'
      apiError.retryable = false
    } else if (status >= 400 && status < 500) {
      apiError.category = 'client'
      apiError.retryable = false
    } else if (status >= 500) {
      apiError.category = 'server'
      apiError.retryable = true
    } else if (error.message.includes('fetch')) {
      apiError.category = 'network'
      apiError.retryable = true
    } else {
      apiError.category = 'client'
      apiError.retryable = false
    }

    apiError.status = status
    apiError.code = `HTTP_${status}`

    return apiError
  }

  const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

  const calculateBackoffDelay = (attempt: number, baseDelay: number = 1000): number => {
    // Exponential backoff with jitter
    const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt), 30000)
    const jitter = Math.random() * 0.1 * exponentialDelay
    return exponentialDelay + jitter
  }

  const buildUrl = (endpoint: string): string => {
    // If URL is already absolute, use as-is
    if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
      return endpoint
    }

    // If URL starts with /api/, remove it since baseURL already includes /api
    if (endpoint.startsWith('/api/')) {
      return `${baseURL}${endpoint.substring(4)}`
    }

    // For relative URLs, build the complete URL
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`

    // Role-based routing
    if (endpoint.startsWith('/lecturer/') || endpoint.startsWith('/student/')) {
      return `${baseURL}${path}`
    } else {
      // For backward compatibility, non-prefixed URLs default to student API
      if (authStore.isLecturer && !endpoint.startsWith('/auth/')) {
        return `${baseURL}/lecturer${path}`
      } else if (authStore.isStudent && !endpoint.startsWith('/auth/')) {
        return `${baseURL}/student${path}`
      } else {
        return `${baseURL}${path}`
      }
    }
  }

  const apiCall = async <T = unknown>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> => {
    const { retries = 3, retryDelay = 1000, timeout = 30000, skipRetry = false, ...options } = config

    const finalUrl = buildUrl(endpoint)
    const requestId = `${options.method || 'GET'}-${finalUrl}`
    let attempt = 0
    let lastError: ApiError

    while (attempt <= retries) {
      try {
        // Track request for loading state
        requestQueue.value.add(requestId)
        updateGlobalLoading()

        // Set timeout
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout)

        const { data, error, statusCode, execute } = useFetch(
          finalUrl,
          {
            ...options,
            signal: controller.signal,
          },
          {
            immediate: false,
          },
        ).json<ApiResponse<T>>()

        try {
          await execute()
          clearTimeout(timeoutId)
        } catch (fetchError) {
          clearTimeout(timeoutId)
          throw fetchError
        }

        // Remove from request queue
        requestQueue.value.delete(requestId)
        updateGlobalLoading()

        // Handle 401 errors with automatic token refresh
        if (statusCode.value === 401 && authStore.token && attempt === 0 && !config.skipAuth) {
          console.log('Token expired, attempting refresh and retry...')

          try {
            const refreshResult = await authStore.refreshToken()

            if (refreshResult.success) {
              console.log('Token refreshed successfully, retrying request...')
              attempt++
              continue // Retry the request with the new token
            } else {
              // Refresh failed, logout user
              console.error('Token refresh failed:', refreshResult.error)
              await authStore.logout()
              throw createApiError(new Error('Authentication expired'))
            }
          } catch (refreshError) {
            await authStore.logout()
            throw createApiError(new Error('Authentication expired'))
          }
        }

        if (error.value) {
          throw createApiError(new Error(error.value.message || 'Request failed'))
        }

        return data.value || { success: false, message: 'No data received' }
      } catch (err) {
        lastError = err as ApiError

        // Remove from request queue on error
        requestQueue.value.delete(requestId)
        updateGlobalLoading()

        // Don't retry if explicitly disabled or error is not retryable
        if (skipRetry || !lastError.retryable || attempt >= retries) {
          console.error(`API call failed after ${attempt + 1} attempts:`, lastError)
          throw lastError
        }

        // Calculate delay for next attempt
        const delay = calculateBackoffDelay(attempt, retryDelay)
        console.warn(`API call attempt ${attempt + 1} failed, retrying in ${delay}ms:`, lastError.message)

        await sleep(delay)
        attempt++
      }
    }

    throw lastError!
  }

  // Basic HTTP methods
  const get = <T = unknown>(endpoint: string, config: RequestConfig = {}) => {
    return apiCall<T>(endpoint, { ...config, method: 'GET' })
  }

  const post = <T = unknown>(endpoint: string, data?: unknown, config: RequestConfig = {}) => {
    return apiCall<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  const put = <T = unknown>(endpoint: string, data?: unknown, config: RequestConfig = {}) => {
    return apiCall<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  const patch = <T = unknown>(endpoint: string, data?: unknown, config: RequestConfig = {}) => {
    return apiCall<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  const del = <T = unknown>(endpoint: string, config: RequestConfig = {}) => {
    return apiCall<T>(endpoint, { ...config, method: 'DELETE' })
  }

  // Specialized API methods for different data types
  const dashboard = {
    async getData(): Promise<ApiResponse<DashboardData>> {
      return get<DashboardData>('/dashboard')
    },

    async getCurrentSemester() {
      return get('/dashboard/semester')
    },

    async getCreditProgress() {
      return get('/dashboard/credits')
    },

    async getGPA() {
      return get('/dashboard/gpa')
    },

    async getHolds() {
      return get('/dashboard/holds')
    },

    async getUpcomingDeadlines() {
      return get('/dashboard/deadlines')
    },
  }

  const courses = {
    async getAvailable(semesterId?: string): Promise<ApiResponse<CourseData[]>> {
      const params = semesterId ? `?semester_id=${semesterId}` : ''
      return get<CourseData[]>(`/courses/available${params}`)
    },

    async getRegistered(semesterId?: string): Promise<ApiResponse<CourseData[]>> {
      const params = semesterId ? `?semester_id=${semesterId}` : ''
      return get<CourseData[]>(`/courses/registered${params}`)
    },

    async register(courseIds: string[], semesterId: string) {
      return post('/courses/register', { course_ids: courseIds, semester_id: semesterId })
    },

    async drop(courseId: string) {
      return del(`/courses/${courseId}/drop`)
    },

    async getDetails(courseId: string): Promise<ApiResponse<CourseData>> {
      return get<CourseData>(`/courses/${courseId}`)
    },

    async checkConflicts(courseIds: string[]) {
      return post('/courses/check-conflicts', { course_ids: courseIds })
    },
  }

  const grades = {
    async getTranscript(): Promise<ApiResponse<GradeData[]>> {
      return get<GradeData[]>('/grades/transcript')
    },

    async getBySemester(semesterId: string): Promise<ApiResponse<GradeData[]>> {
      return get<GradeData[]>(`/grades/semester/${semesterId}`)
    },

    async getGPATrend() {
      return get('/grades/gpa-trend')
    },

    async getCourseGrade(courseId: string) {
      return get(`/grades/course/${courseId}`)
    },
  }

  const assignments = {
    async getAll() {
      return get('/assignments')
    },

    async getByCourse(courseId: string) {
      return get(`/assignments/course/${courseId}`)
    },

    async submit(assignmentId: string, files: File[]) {
      const formData = new FormData()
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file)
      })

      return apiCall(`/assignments/${assignmentId}/submit`, {
        method: 'POST',
        body: formData,
        headers: {}, // Let browser set Content-Type for FormData
      })
    },
  }

  const schedule = {
    async getWeekly(weekStart?: string) {
      const params = weekStart ? `?week_start=${weekStart}` : ''
      return get(`/schedule/weekly${params}`)
    },

    async getDaily(date?: string) {
      const params = date ? `?date=${date}` : ''
      return get(`/schedule/daily${params}`)
    },

    async getExams() {
      return get('/schedule/exams')
    },
  }

  const notifications = {
    async getAll() {
      return get('/notifications')
    },

    async markAsRead(notificationId: string) {
      return patch(`/notifications/${notificationId}/read`)
    },

    async markAllAsRead() {
      return patch('/notifications/mark-all-read')
    },

    async getUnreadCount() {
      return get('/notifications/unread-count')
    },
  }

  // Lecturer-specific API methods
  const lecturer = {
    // Dashboard
    dashboard: {
      async getData() {
        return get('/lecturer/dashboard')
      },

      async getTeachingSummary() {
        return get('/lecturer/dashboard/teaching-summary')
      },

      async getStudentAlerts() {
        return get('/lecturer/dashboard/student-alerts')
      },

      async getRecentActivities() {
        return get('/lecturer/dashboard/activities')
      },
    },

    // Course Management
    courses: {
      async getAll() {
        return get('/lecturer/courses')
      },
      async getUnitById(courseId: string) {
        return get<Unit>(`/lecturer/courses/${courseId}/unit`)
      },

      async getById(courseId: string) {
        return get<CourseOffering>(`/lecturer/courses/${courseId}`)
      },

      async getStudents(courseId: string) {
        return get(`/lecturer/courses/${courseId}/students`)
      },

      async getStatistics(courseId: string) {
        return get(`/lecturer/courses/${courseId}/statistics`)
      },

      async uploadMaterial(courseId: string, material: FormData) {
        return apiCall(`/lecturer/courses/${courseId}/materials`, {
          method: 'POST',
          body: material,
          headers: {}, // Let browser set Content-Type for FormData
        })
      },

      async updateSyllabus(courseId: string, syllabusData: any) {
        return put(`/lecturer/courses/${courseId}/syllabus`, syllabusData)
      },
      async getSessions(courseId: string) {
        return get<ClassSession[]>(`/lecturer/courses/${courseId}/sessions`)
      },
      async getSessionAttendance(sessionId: string) {
        return get(`/lecturer/courses/sessions/${sessionId}/attendance`)
      },
    },

    // Attendance Management
    attendance: {
      async getSessions(courseId?: string) {
        const params = courseId ? `?course_id=${courseId}` : ''
        return get(`/lecturer/attendance/sessions${params}`)
      },

      async getSessionAttendance(sessionId: string) {
        return get(`/lecturer/attendance/sessions/${sessionId}`)
      },

      async markAttendance(sessionId: string, attendanceData: any) {
        return post(`/lecturer/attendance/sessions/${sessionId}/mark`, attendanceData)
      },

      async bulkUpdateAttendance(sessionId: string, updates: any) {
        return put(`/lecturer/attendance/sessions/${sessionId}/bulk`, updates)
      },

      async getAnalytics(courseId: string, period?: string) {
        const params = period ? `?period=${period}` : ''
        return get(`/lecturer/attendance/analytics/${courseId}${params}`)
      },

      async exportReport(courseId: string, format: string = 'pdf') {
        return get(`/lecturer/attendance/export/${courseId}?format=${format}`)
      },
    },

    // Student Management
    students: {
      async getAll(courseId?: string) {
        const params = courseId ? `?course_id=${courseId}` : ''
        return get(`/lecturer/students${params}`)
      },

      async getById(studentId: string, courseId?: string) {
        const params = courseId ? `?course_id=${courseId}` : ''
        return get(`/lecturer/students/${studentId}${params}`)
      },

      async updateNotes(studentId: string, courseId: string, notes: string) {
        return put(`/lecturer/students/${studentId}/notes`, {
          course_id: courseId,
          notes,
        })
      },

      async getAttendanceDetails(studentId: string, courseId: string) {
        return get(`/lecturer/students/${studentId}/attendance?course_id=${courseId}`)
      },

      async getAlerts(studentId?: string) {
        const params = studentId ? `/${studentId}` : ''
        return get(`/lecturer/students/alerts${params}`)
      },
    },

    // Timetable and Sessions
    timetable: {
      async getSessions(weekStart?: string) {
        const params = weekStart ? `?week_start=${weekStart}` : ''
        return get(`/lecturer/timetable/sessions${params}`)
      },

      async createSession(sessionData: any) {
        return post('/lecturer/timetable/sessions', sessionData)
      },

      async updateSession(sessionId: string, sessionData: any) {
        return put(`/lecturer/timetable/sessions/${sessionId}`, sessionData)
      },

      async cancelSession(sessionId: string, reason?: string) {
        return patch(`/lecturer/timetable/sessions/${sessionId}/cancel`, { reason })
      },

      async rescheduleSession(sessionId: string, newDateTime: any) {
        return patch(`/lecturer/timetable/sessions/${sessionId}/reschedule`, newDateTime)
      },
    },

    // Communication and Feedback
    communication: {
      async sendMessage(messageData: any) {
        return post('/lecturer/communication/messages', messageData)
      },

      async getMessages(type?: string) {
        const params = type ? `?type=${type}` : ''
        return get(`/lecturer/communication/messages${params}`)
      },

      async getFeedback(courseId?: string) {
        const params = courseId ? `?course_id=${courseId}` : ''
        return get(`/lecturer/communication/feedback${params}`)
      },

      async respondToFeedback(feedbackId: string, response: string) {
        return post(`/lecturer/communication/feedback/${feedbackId}/respond`, { response })
      },
    },
  }

  // Interceptor management
  const addRequestInterceptor = (interceptor: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>) => {
    requestInterceptors.push(interceptor)
  }

  const addResponseInterceptor = (interceptor: (response: any) => any | Promise<any>) => {
    responseInterceptors.push(interceptor)
  }

  const addErrorInterceptor = (interceptor: (error: ApiError) => ApiError | Promise<ApiError>) => {
    errorInterceptors.push(interceptor)
  }

  // Computed properties for reactive state
  const isLoading = computed(() => globalLoading.value.isLoading)
  const loadingMessage = computed(() => globalLoading.value.loadingMessage)

  return {
    // Core methods
    apiCall,
    get,
    post,
    put,
    patch,
    delete: del,

    // Student-specific API methods
    dashboard,
    courses,
    grades,
    assignments,
    schedule,
    notifications,

    // Lecturer-specific API methods
    lecturer,

    // Interceptor management
    addRequestInterceptor,
    addResponseInterceptor,
    addErrorInterceptor,

    // Loading state
    isLoading,
    loadingMessage,
    globalLoading: readonly(globalLoading),

    // Utility methods
    createApiError,
  }
}

// Export useApi as useBaseApi for clarity
export { useApi as useBaseApi }
