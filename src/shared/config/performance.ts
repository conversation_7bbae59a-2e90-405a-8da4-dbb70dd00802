// Performance configuration and optimization settings

export interface PerformanceConfig {
  // Code splitting
  codeSplitting: {
    enabled: boolean
    chunkSizeWarningLimit: number
    maxChunks: number
  }
  
  // Lazy loading
  lazyLoading: {
    enabled: boolean
    intersectionMargin: string
    threshold: number
    retryAttempts: number
    retryDelay: number
  }
  
  // Image optimization
  images: {
    quality: number
    formats: string[]
    breakpoints: number[]
    lazyLoading: boolean
    placeholder: boolean
  }
  
  // Caching
  caching: {
    staticAssets: number // Cache duration in seconds
    apiResponses: number
    images: number
  }
  
  // Bundle optimization
  bundle: {
    minify: boolean
    treeshaking: boolean
    compression: boolean
    sourcemaps: boolean
  }
  
  // Performance monitoring
  monitoring: {
    enabled: boolean
    reportingEndpoint?: string
    sampleRate: number
    vitalsThresholds: {
      lcp: number
      fid: number
      cls: number
    }
  }
}

// Default performance configuration
export const defaultPerformanceConfig: PerformanceConfig = {
  codeSplitting: {
    enabled: true,
    chunkSizeWarningLimit: 500000, // 500KB
    maxChunks: 20
  },
  
  lazyLoading: {
    enabled: true,
    intersectionMargin: '50px',
    threshold: 0.1,
    retryAttempts: 3,
    retryDelay: 1000
  },
  
  images: {
    quality: 80,
    formats: ['webp', 'jpeg'],
    breakpoints: [320, 640, 768, 1024, 1280, 1536],
    lazyLoading: true,
    placeholder: true
  },
  
  caching: {
    staticAssets: 31536000, // 1 year
    apiResponses: 300, // 5 minutes
    images: 2592000 // 30 days
  },
  
  bundle: {
    minify: true,
    treeshaking: true,
    compression: true,
    sourcemaps: false
  },
  
  monitoring: {
    enabled: true,
    sampleRate: 0.1, // 10% of users
    vitalsThresholds: {
      lcp: 2500, // 2.5 seconds
      fid: 100,  // 100 milliseconds
      cls: 0.1   // 0.1 score
    }
  }
}

// Environment-specific configurations
export const developmentConfig: Partial<PerformanceConfig> = {
  bundle: {
    minify: false,
    treeshaking: true,
    compression: false,
    sourcemaps: true
  },
  monitoring: {
    enabled: true,
    sampleRate: 1.0, // 100% in development
    vitalsThresholds: {
      lcp: 2500, // 2.5s for development
      fid: 100,  // 100ms
      cls: 0.1   // 0.1 score
    }
  }
}

export const productionConfig: Partial<PerformanceConfig> = {
  bundle: {
    minify: true,
    treeshaking: true,
    compression: true,
    sourcemaps: false
  },
  monitoring: {
    enabled: true,
    reportingEndpoint: '/api/performance',
    sampleRate: 0.05, // 5% in production
    vitalsThresholds: {
      lcp: 2500, // 2.5s for production
      fid: 100,  // 100ms
      cls: 0.1   // 0.1 score
    }
  }
}

// Get configuration based on environment
export function getPerformanceConfig(): PerformanceConfig {
  const baseConfig = { ...defaultPerformanceConfig }
  
  if (import.meta.env.MODE === 'development') {
    return { ...baseConfig, ...developmentConfig }
  }
  
  if (import.meta.env.MODE === 'production') {
    return { ...baseConfig, ...productionConfig }
  }
  
  return baseConfig
}

// Performance optimization utilities
export class PerformanceOptimizer {
  private config: PerformanceConfig
  
  constructor(config?: Partial<PerformanceConfig>) {
    this.config = { ...getPerformanceConfig(), ...config }
  }
  
  // Initialize all performance optimizations
  async initialize(): Promise<void> {
    console.log('Initializing performance optimizations...')
    
    // Setup lazy loading
    if (this.config.lazyLoading.enabled) {
      await this.setupLazyLoading()
    }
    
    // Setup image optimization
    if (this.config.images.lazyLoading) {
      this.setupImageOptimization()
    }
    
    // Setup performance monitoring
    if (this.config.monitoring.enabled) {
      this.setupPerformanceMonitoring()
    }
    
    // Setup caching
    this.setupCaching()
    
    console.log('Performance optimizations initialized')
  }
  
  private async setupLazyLoading(): Promise<void> {
    // Dynamic import for lazy loading utilities
    const { addResourceHints, preloadCriticalComponents } = await import('../utils/lazyLoading')
    
    // Add resource hints
    addResourceHints()
    
    // Preload critical components
    await preloadCriticalComponents()
  }
  
  private setupImageOptimization(): void {
    // Setup image lazy loading observer
    const images = document.querySelectorAll('img[data-src]')
    
    if (images.length > 0) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              const src = img.dataset.src
              
              if (src) {
                img.src = src
                img.removeAttribute('data-src')
                observer.unobserve(img)
              }
            }
          })
        },
        {
          rootMargin: this.config.lazyLoading.intersectionMargin,
          threshold: this.config.lazyLoading.threshold
        }
      )
      
      images.forEach(img => observer.observe(img))
    }
  }
  
  private setupPerformanceMonitoring(): void {
    // Setup Core Web Vitals monitoring
    if ('PerformanceObserver' in window) {
      // LCP Observer
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        
        if (lastEntry.startTime > this.config.monitoring.vitalsThresholds.lcp) {
          console.warn(`LCP threshold exceeded: ${lastEntry.startTime}ms`)
        }
      })
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (e) {
        // LCP not supported
      }
      
      // FID Observer
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          const fid = entry.processingStart - entry.startTime
          
          if (fid > this.config.monitoring.vitalsThresholds.fid) {
            console.warn(`FID threshold exceeded: ${fid}ms`)
          }
        })
      })
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (e) {
        // FID not supported
      }
    }
  }
  
  private setupCaching(): void {
    // Setup service worker caching if available
    if ('serviceWorker' in navigator && 'caches' in window) {
      // Cache configuration is handled by the service worker
      console.log('Service Worker caching available')
    }
    
    // Setup memory-based caching for API responses
    this.setupApiCaching()
  }
  
  private setupApiCaching(): void {
    // Simple in-memory cache for API responses
    const cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
    
    // Intercept fetch requests (simplified example)
    const originalFetch = window.fetch
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString()
      const method = init?.method || 'GET'
      
      // Only cache GET requests to API endpoints
      if (method === 'GET' && url.includes('/api/')) {
        const cacheKey = url
        const cached = cache.get(cacheKey)
        
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
          // Return cached response
          return new Response(JSON.stringify(cached.data), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          })
        }
      }
      
      // Make actual request
      const response = await originalFetch(input, init)
      
      // Cache successful API responses
      if (method === 'GET' && url.includes('/api/') && response.ok) {
        try {
          const data = await response.clone().json()
          cache.set(url, {
            data,
            timestamp: Date.now(),
            ttl: this.config.caching.apiResponses * 1000
          })
        } catch (e) {
          // Not JSON, skip caching
        }
      }
      
      return response
    }
  }
  
  // Get current performance metrics
  getMetrics(): any {
    if (!window.performance) return null
    
    const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = window.performance.getEntriesByType('paint')
    
    return {
      navigation: {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        loadComplete: navigation.loadEventEnd - navigation.fetchStart,
        firstByte: navigation.responseStart - navigation.fetchStart
      },
      paint: {
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime
      },
      memory: (window.performance as any).memory ? {
        used: (window.performance as any).memory.usedJSHeapSize,
        total: (window.performance as any).memory.totalJSHeapSize,
        limit: (window.performance as any).memory.jsHeapSizeLimit
      } : null
    }
  }
  
  // Report performance data
  async reportMetrics(): Promise<void> {
    if (!this.config.monitoring.reportingEndpoint) return
    
    const metrics = this.getMetrics()
    if (!metrics) return
    
    // Sample based on configured rate
    if (Math.random() > this.config.monitoring.sampleRate) return
    
    try {
      await fetch(this.config.monitoring.reportingEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metrics,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      })
    } catch (error) {
      console.warn('Failed to report performance metrics:', error)
    }
  }
}

// Global performance optimizer instance
export const performanceOptimizer = new PerformanceOptimizer()

// Initialize performance optimizations when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      performanceOptimizer.initialize()
    })
  } else {
    performanceOptimizer.initialize()
  }
}
