import type { ClassSession, CourseOffering } from '@/lecturer/types'

// Course and Academic Types
export type CourseStatus = 'available' | 'full' | 'cancelled' | 'waitlist' | 'closed'
export type RegistrationStatus = 'idle' | 'registering' | 'success' | 'error' | 'conflict'
export type ClassType = 'lecture' | 'lab' | 'tutorial' | 'exam' | 'seminar'
export type DeliveryMode = 'in_person' | 'online' | 'hybrid'

export interface Instructor {
  id: string
  name: string
  email: string
  title: string
  department: string
  avatar_url?: string
  office_location?: string
  office_hours?: string
}

export interface Schedule {
  id: string
  day_of_week: number // 0 = Sunday, 1 = Monday, etc.
  start_time: string // HH:MM format
  end_time: string // HH:MM format
  location: string
  room?: string
  building?: string
  class_type: ClassType
  delivery_mode: DeliveryMode
  zoom_link?: string
  recurring: boolean
}

export interface Course {
  id: string
  code: string
  name: string
  description: string
  credits: number
  prerequisites: string[]
  co_requisites: string[]
  semester: string
  instructor: Instructor
  schedule: Schedule[]
  status: CourseStatus
  enrollment_capacity: number
  current_enrollment: number
  waitlist_count: number
  delivery_mode: DeliveryMode
  location: string
  syllabus_url?: string
  materials_url?: string
  is_registered: boolean
  grade?: string
  grade_points?: number
}

export interface TimeConflict {
  course_id: string
  course_code: string
  course_name: string
  conflicting_schedules: {
    day: string
    time_range: string
    location: string
  }[]
  reason: string
}
