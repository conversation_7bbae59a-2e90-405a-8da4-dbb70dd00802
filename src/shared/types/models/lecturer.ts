// Lecturer-specific types for the lecturer portal extension

// Lecturer Profile Types
export interface Department {
  id: number
  name: string
  code: string
  faculty?: string
  head_of_department?: string
}

export interface Lecturer {
  id: number
  employee_id: string
  first_name: string
  last_name: string
  full_name: string
  email: string
  phone?: string
  date_of_birth?: string
  gender?: string
  nationality?: string
  address?: string
  avatar_url?: string
  title: string // e.g., "Professor", "Associate Professor", "Senior Lecturer"
  department: Department
  office_location?: string
  biography?: string
  academic_rank?: string
  specializations?: string[]
  research_interests?: string[]
  office_hours?: string
  created_at: string
  updated_at: string
}

// Course Management Types
export interface CourseOffering {
  id: number
  curriculum_unit: {
    id: number
    code: string
    name: string
    credits: number
    description?: string
    prerequisites?: string[]
  }
  semester: {
    id: number
    name: string
    code: string
    start_date: string
    end_date: string
    academic_year: string
    is_active: boolean
  }
  lecturer_id: number
  section_code: string
  max_capacity: number
  current_enrollment: number
  delivery_mode: 'in_person' | 'online' | 'hybrid' | 'blended'
  schedule_days: string[] // JSON array of days
  schedule_time_start: string
  schedule_time_end: string
  location?: string
  syllabus_url?: string
  course_materials: CourseMaterial[]
  class_sessions: ClassSession[]
  created_at: string
  updated_at: string
}

export interface CourseMaterial {
  id: number
  course_offering_id: number
  title: string
  description?: string
  file_url: string
  file_type: string
  file_size: number
  upload_date: string
  is_required: boolean
  week_number?: number
  topic?: string
}

// Class Session Types
export interface ClassSession {
  id: number
  course_offering_id: number
  instructor_id: number
  session_title: string
  session_description?: string
  session_date: string
  start_time: string
  end_time: string
  session_type:
    | 'lecture'
    | 'tutorial'
    | 'practical'
    | 'laboratory'
    | 'seminar'
    | 'workshop'
    | 'exam'
    | 'assessment'
    | 'review'
    | 'presentation'
  delivery_mode: 'in_person' | 'online' | 'hybrid' | 'blended'
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed'
  
  // Online meeting details
  online_meeting_url?: string
  meeting_id?: string
  meeting_password?: string
  
  // Location
  location?: string
  
  // Learning content
  learning_objectives?: string[]
  required_materials?: string[]
  topics_covered?: string[]
  
  // Attendance tracking
  attendance_required: boolean
  attendance_tracking_enabled: boolean
  attendance_marked: boolean
  expected_attendees: number
  actual_attendees: number
  attendance_percentage: number
  
  // Assessment details
  is_assessment: boolean
  assessment_weight?: number
  assessment_duration_minutes?: number
  
  // Administrative fields
  instructor_notes?: string
  student_instructions?: string
  cancellation_reason?: string
  
  // Related data
  materials?: SessionMaterial[]
  attendance_records?: AttendanceRecord[]
  
  created_at: string
  updated_at: string
}

export interface SessionMaterial {
  id: number
  class_session_id: number
  title: string
  description?: string
  file_url: string
  file_type: string
  upload_date: string
}

// Attendance Management Types
export interface AttendanceRecord {
  id: number
  class_session_id: number
  student_id: number
  student_name: string
  student_email: string
  status: 'present' | 'absent' | 'late' | 'excused' | 'partial'
  check_in_time?: string
  check_out_time?: string
  minutes_late: number
  participation_level?: 'excellent' | 'good' | 'average' | 'poor' | 'none'
  participation_score?: number
  notes?: string
  recorded_by_lecturer_id: number
  recorded_at: string
  updated_at: string
}

export interface AttendanceSession {
  id: number
  class_session_id: number
  course_code: string
  course_name: string
  session_date: string
  session_type: string
  attendance_records: AttendanceRecord[]
  marked_by: number
  marked_at?: string
  total_students: number
  present_count: number
  absent_count: number
  late_count: number
  attendance_percentage: number
}

// Student Management Types
export interface EnrolledStudent {
  student_id: number
  student_name: string
  student_email: string
  student_avatar_url?: string
  enrollment_status: string
  registration_date: string
  attendance_percentage: number
  participation_score?: number
  academic_standing?: string
  current_gpa?: number
  total_credits?: number
  notes?: string
}

export interface StudentAlert {
  id: number
  student_id: number
  student_name: string
  course_code: string
  course_name: string
  alert_type: 'low_attendance' | 'poor_participation' | 'multiple_absences' | 'academic_concern'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  details: string
  action_required: boolean
  attendance_percentage?: number
  absence_count?: number
  created_at: string
  resolved_at?: string
  resolved_by?: number
}

// Dashboard Types
export interface LecturerDashboard {
  teaching_summary: {
    total_courses: number
    total_students: number
    active_sessions_today: ClassSession[]
    upcoming_sessions: ClassSession[]
    completed_sessions_this_week: number
  }
  attendance_summary: {
    sessions_requiring_attendance: number
    sessions_completed_today: number
    average_class_attendance: number
    attendance_trend: 'improving' | 'declining' | 'stable'
  }
  student_alerts: {
    low_attendance_students: StudentAlert[]
    students_needing_attention: StudentAlert[]
    total_alerts: number
    critical_alerts: number
  }
  recent_activities: Activity[]
  upcoming_deadlines: {
    assignment_due_dates: Assignment[]
    exam_schedules: ClassSession[]
    administrative_tasks: AdminTask[]
  }
}

export interface Activity {
  id: number
  type: 'attendance_marked' | 'material_uploaded' | 'session_completed' | 'student_message'
  title: string
  description: string
  course_code?: string
  student_name?: string
  timestamp: string
  metadata?: Record<string, any>
}

export interface Assignment {
  id: number
  course_offering_id: number
  title: string
  description: string
  due_date: string
  total_submissions: number
  pending_grades: number
  completion_rate: number
}

export interface AdminTask {
  id: number
  title: string
  description: string
  due_date: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'in_progress' | 'completed'
}

// Course Statistics Types
export interface CourseStatistics {
  course_offering_id: number
  total_sessions: number
  completed_sessions: number
  average_attendance: number
  attendance_trend: 'improving' | 'declining' | 'stable'
  student_performance_summary: {
    excellent_attendance: number // >90%
    good_attendance: number // 75-90%
    poor_attendance: number // <75%
  }
  engagement_metrics: {
    average_participation_score: number
    active_students: number
    at_risk_students: number
  }
}

// Communication Types
export interface LecturerMessage {
  id: number
  sender_id: number
  recipient_type: 'student' | 'class' | 'department'
  recipient_ids: number[]
  subject: string
  content: string
  priority: 'normal' | 'high' | 'urgent'
  delivery_status: 'draft' | 'sent' | 'delivered' | 'read'
  read_receipts: MessageReceipt[]
  attachments?: MessageAttachment[]
  sent_at?: string
  created_at: string
}

export interface MessageReceipt {
  recipient_id: number
  recipient_name: string
  delivered_at?: string
  read_at?: string
  status: 'pending' | 'delivered' | 'read' | 'failed'
}

export interface MessageAttachment {
  id: number
  filename: string
  file_url: string
  file_size: number
  file_type: string
}
