// Common UI models, enums, and constants used across roles
export interface BaseEntity {
  id: string | number
  created_at?: string
  updated_at?: string
}

export interface PaginationMeta {
  current_page: number
  per_page: number
  total: number
  last_page: number
}

// ApiResponse is now exported from ../api/api.ts to avoid conflicts

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

export enum UserRole {
  STUDENT = 'student',
  LECTURER = 'lecturer',
  ADMIN = 'admin'
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium', 
  HIGH = 'high',
  URGENT = 'urgent'
}

// Export specific models from student.ts
export type { Campus, Program, Specialization } from './student'