<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { usePerformance } from '../../composables/usePerformance'
import { 
  Gauge, 
  Zap, 
  Clock, 
  Activity,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download
} from 'lucide-vue-next'

interface Props {
  autoRefresh?: boolean
  refreshInterval?: number
  showRecommendations?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: false,
  refreshInterval: 30000, // 30 seconds
  showRecommendations: true,
})

const performance = usePerformance()
const refreshTimer = ref<number | null>(null)
const lastRefresh = ref<Date>(new Date())

// Computed properties
const performanceScore = computed(() => performance.getPerformanceScore())
const performanceGrade = computed(() => performance.getPerformanceGrade())
const recommendations = computed(() => performance.getRecommendations())

const gradeColor = computed(() => {
  switch (performanceGrade.value) {
    case 'A': return 'text-green-600'
    case 'B': return 'text-blue-600'
    case 'C': return 'text-yellow-600'
    case 'D': return 'text-orange-600'
    case 'F': return 'text-red-600'
    default: return 'text-gray-600'
  }
})

const gradeVariant = computed(() => {
  switch (performanceGrade.value) {
    case 'A': return 'default' as const
    case 'B': return 'secondary' as const
    case 'C': return 'outline' as const
    case 'D': return 'outline' as const
    case 'F': return 'destructive' as const
    default: return 'outline' as const
  }
})

// Core Web Vitals status
const getLCPStatus = (lcp?: number) => {
  if (!lcp) return { status: 'unknown', color: 'text-gray-500' }
  if (lcp <= 2500) return { status: 'good', color: 'text-green-600' }
  if (lcp <= 4000) return { status: 'needs improvement', color: 'text-yellow-600' }
  return { status: 'poor', color: 'text-red-600' }
}

const getFIDStatus = (fid?: number) => {
  if (!fid) return { status: 'unknown', color: 'text-gray-500' }
  if (fid <= 100) return { status: 'good', color: 'text-green-600' }
  if (fid <= 300) return { status: 'needs improvement', color: 'text-yellow-600' }
  return { status: 'poor', color: 'text-red-600' }
}

const getCLSStatus = (cls?: number) => {
  if (!cls) return { status: 'unknown', color: 'text-gray-500' }
  if (cls <= 0.1) return { status: 'good', color: 'text-green-600' }
  if (cls <= 0.25) return { status: 'needs improvement', color: 'text-yellow-600' }
  return { status: 'poor', color: 'text-red-600' }
}

// Format time values
const formatTime = (ms?: number) => {
  if (!ms) return 'N/A'
  if (ms < 1000) return `${Math.round(ms)}ms`
  return `${(ms / 1000).toFixed(2)}s`
}

// Format file size
const formatSize = (bytes?: number) => {
  if (!bytes) return 'N/A'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
}

// Actions
const refreshMetrics = async () => {
  performance.initializeMonitoring()
  lastRefresh.value = new Date()
}

const exportReport = async () => {
  const report = await performance.reportPerformance()
  
  const blob = new Blob([JSON.stringify(report, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Auto refresh
const startAutoRefresh = () => {
  if (props.autoRefresh && !refreshTimer.value) {
    refreshTimer.value = setInterval(refreshMetrics, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// Lifecycle
onMounted(() => {
  if (props.autoRefresh) {
    startAutoRefresh()
  }
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold">Performance Dashboard</h2>
        <p class="text-muted-foreground">
          Monitor your application's performance metrics and Core Web Vitals
        </p>
      </div>
      
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="refreshMetrics">
          <RefreshCw class="h-4 w-4 mr-2" />
          Refresh
        </Button>
        <Button variant="outline" size="sm" @click="exportReport">
          <Download class="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>
    </div>

    <!-- Overall Score -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Gauge class="h-5 w-5" />
          Performance Score
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="text-4xl font-bold" :class="gradeColor">
              {{ performanceScore }}
            </div>
            <div>
              <Badge :variant="gradeVariant" class="text-lg px-3 py-1">
                Grade {{ performanceGrade }}
              </Badge>
              <div class="text-sm text-muted-foreground mt-1">
                Last updated: {{ lastRefresh.toLocaleTimeString() }}
              </div>
            </div>
          </div>
          
          <div class="w-32">
            <Progress :value="performanceScore" class="h-3" />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Core Web Vitals -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Largest Contentful Paint -->
      <Card>
        <CardHeader class="pb-3">
          <CardTitle class="text-base flex items-center gap-2">
            <Zap class="h-4 w-4" />
            LCP
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div class="text-2xl font-bold">
              {{ formatTime(performance.metrics.value.lcp) }}
            </div>
            <div class="flex items-center gap-2">
              <component 
                :is="getLCPStatus(performance.metrics.value.lcp).status === 'good' ? CheckCircle : AlertTriangle"
                class="h-4 w-4"
                :class="getLCPStatus(performance.metrics.value.lcp).color"
              />
              <span 
                class="text-sm capitalize"
                :class="getLCPStatus(performance.metrics.value.lcp).color"
              >
                {{ getLCPStatus(performance.metrics.value.lcp).status }}
              </span>
            </div>
            <div class="text-xs text-muted-foreground">
              Largest Contentful Paint
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- First Input Delay -->
      <Card>
        <CardHeader class="pb-3">
          <CardTitle class="text-base flex items-center gap-2">
            <Clock class="h-4 w-4" />
            FID
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div class="text-2xl font-bold">
              {{ formatTime(performance.metrics.value.fid) }}
            </div>
            <div class="flex items-center gap-2">
              <component 
                :is="getFIDStatus(performance.metrics.value.fid).status === 'good' ? CheckCircle : AlertTriangle"
                class="h-4 w-4"
                :class="getFIDStatus(performance.metrics.value.fid).color"
              />
              <span 
                class="text-sm capitalize"
                :class="getFIDStatus(performance.metrics.value.fid).color"
              >
                {{ getFIDStatus(performance.metrics.value.fid).status }}
              </span>
            </div>
            <div class="text-xs text-muted-foreground">
              First Input Delay
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Cumulative Layout Shift -->
      <Card>
        <CardHeader class="pb-3">
          <CardTitle class="text-base flex items-center gap-2">
            <Activity class="h-4 w-4" />
            CLS
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div class="text-2xl font-bold">
              {{ performance.metrics.value.cls?.toFixed(3) || 'N/A' }}
            </div>
            <div class="flex items-center gap-2">
              <component 
                :is="getCLSStatus(performance.metrics.value.cls).status === 'good' ? CheckCircle : AlertTriangle"
                class="h-4 w-4"
                :class="getCLSStatus(performance.metrics.value.cls).color"
              />
              <span 
                class="text-sm capitalize"
                :class="getCLSStatus(performance.metrics.value.cls).color"
              >
                {{ getCLSStatus(performance.metrics.value.cls).status }}
              </span>
            </div>
            <div class="text-xs text-muted-foreground">
              Cumulative Layout Shift
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Detailed Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Navigation Timing -->
      <Card>
        <CardHeader>
          <CardTitle>Navigation Timing</CardTitle>
        </CardHeader>
        <CardContent class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm">DOM Content Loaded</span>
            <span class="font-medium">{{ formatTime(performance.metrics.value.domContentLoaded) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm">Load Complete</span>
            <span class="font-medium">{{ formatTime(performance.metrics.value.loadComplete) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm">First Paint</span>
            <span class="font-medium">{{ formatTime(performance.metrics.value.firstPaint) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm">First Contentful Paint</span>
            <span class="font-medium">{{ formatTime(performance.metrics.value.firstContentfulPaint) }}</span>
          </div>
        </CardContent>
      </Card>

      <!-- Resource Performance -->
      <Card>
        <CardHeader>
          <CardTitle>Resource Performance</CardTitle>
        </CardHeader>
        <CardContent class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm">Total Resources</span>
            <span class="font-medium">{{ performance.metrics.value.totalResources || 0 }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm">Total Size</span>
            <span class="font-medium">{{ formatSize(performance.metrics.value.totalResourceSize) }}</span>
          </div>
          <div v-if="performance.metrics.value.slowestResource" class="space-y-1">
            <div class="text-sm font-medium">Slowest Resource:</div>
            <div class="text-xs text-muted-foreground truncate">
              {{ performance.metrics.value.slowestResource.name }}
            </div>
            <div class="text-xs">
              {{ formatTime(performance.metrics.value.slowestResource.duration) }} • 
              {{ formatSize(performance.metrics.value.slowestResource.size) }}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Recommendations -->
    <Card v-if="showRecommendations && recommendations.length > 0">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <TrendingUp class="h-5 w-5" />
          Performance Recommendations
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul class="space-y-2">
          <li 
            v-for="(recommendation, index) in recommendations"
            :key="index"
            class="flex items-start gap-2 text-sm"
          >
            <AlertTriangle class="h-4 w-4 text-yellow-600 mt-0.5 shrink-0" />
            <span>{{ recommendation }}</span>
          </li>
        </ul>
      </CardContent>
    </Card>

    <!-- No Recommendations -->
    <Card v-else-if="showRecommendations">
      <CardContent class="text-center py-8">
        <CheckCircle class="h-12 w-12 mx-auto text-green-600 mb-4" />
        <p class="text-lg font-medium">Great Performance!</p>
        <p class="text-muted-foreground">No recommendations at this time.</p>
      </CardContent>
    </Card>
  </div>
</template>
