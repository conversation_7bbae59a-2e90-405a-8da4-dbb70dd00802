<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'white'
  overlay?: boolean
  text?: string
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'primary',
  overlay: false,
  fullscreen: false,
})

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm': return 'h-4 w-4'
    case 'md': return 'h-6 w-6'
    case 'lg': return 'h-8 w-8'
    case 'xl': return 'h-12 w-12'
    default: return 'h-6 w-6'
  }
})

const colorClasses = computed(() => {
  switch (props.variant) {
    case 'primary': return 'text-red-600'
    case 'secondary': return 'text-gray-600'
    case 'white': return 'text-white'
    default: return 'text-red-600'
  }
})

const textSizeClasses = computed(() => {
  switch (props.size) {
    case 'sm': return 'text-sm'
    case 'md': return 'text-base'
    case 'lg': return 'text-lg'
    case 'xl': return 'text-xl'
    default: return 'text-base'
  }
})
</script>

<template>
  <!-- Fullscreen Overlay -->
  <div v-if="fullscreen" class="fixed inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm"
    role="status" aria-live="polite" aria-label="Loading">
    <div class="flex flex-col items-center gap-4">
      <!-- Spinner -->
      <svg :class="['animate-spin', sizeClasses, colorClasses]" xmlns="http://www.w3.org/2000/svg" fill="none"
        viewBox="0 0 24 24" aria-hidden="true">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
        <path class="opacity-75" fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
      </svg>

      <!-- Loading Text -->
      <div v-if="text" :class="['font-medium', textSizeClasses, colorClasses]">
        {{ text }}
      </div>
    </div>
  </div>

  <!-- Regular Overlay -->
  <div v-else-if="overlay"
    class="absolute inset-0 z-10 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg" role="status"
    aria-live="polite" aria-label="Loading">
    <div class="flex flex-col items-center gap-2">
      <!-- Spinner -->
      <svg :class="['animate-spin', sizeClasses, colorClasses]" xmlns="http://www.w3.org/2000/svg" fill="none"
        viewBox="0 0 24 24" aria-hidden="true">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
        <path class="opacity-75" fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
      </svg>

      <!-- Loading Text -->
      <div v-if="text" :class="['font-medium text-center', textSizeClasses, colorClasses]">
        {{ text }}
      </div>
    </div>
  </div>

  <!-- Inline Spinner -->
  <div v-else class="flex items-center gap-2" role="status" aria-live="polite" aria-label="Loading">
    <!-- Spinner -->
    <svg :class="['animate-spin', sizeClasses, colorClasses]" xmlns="http://www.w3.org/2000/svg" fill="none"
      viewBox="0 0 24 24" aria-hidden="true">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
      <path class="opacity-75" fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
    </svg>

    <!-- Loading Text -->
    <span v-if="text" :class="['font-medium', textSizeClasses, colorClasses]">
      {{ text }}
    </span>
  </div>
</template>
