import { defineAsyncComponent, type AsyncComponentLoader, type Component } from 'vue'
import LoadingSpinner from '../components/ui/LoadingSpinner.vue'

interface LazyLoadOptions {
  loadingComponent?: Component
  errorComponent?: Component
  delay?: number
  timeout?: number
  suspensible?: boolean
  retryDelay?: number
  maxRetries?: number
}

// Default loading component
const DefaultLoadingComponent = {
  template: `
    <div class="flex items-center justify-center p-8">
      <LoadingSpinner size="lg" text="Loading..." />
    </div>
  `,
  components: { LoadingSpinner }
}

// Default error component
const DefaultErrorComponent = {
  template: `
    <div class="flex flex-col items-center justify-center p-8 text-center">
      <div class="text-red-600 mb-4">
        <svg class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to load component</h3>
      <p class="text-gray-600 mb-4">There was an error loading this part of the application.</p>
      <button 
        @click="$emit('retry')"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Try Again
      </button>
    </div>
  `,
  emits: ['retry']
}

// Create lazy loaded component with retry logic
export function createLazyComponent(
  loader: AsyncComponentLoader,
  options: LazyLoadOptions = {}
) {
  const {
    loadingComponent = DefaultLoadingComponent,
    errorComponent = DefaultErrorComponent,
    delay = 200,
    timeout = 30000,
    suspensible = false,
    retryDelay = 1000,
    maxRetries = 3
  } = options

  let retryCount = 0

  const retryableLoader = async () => {
    try {
      const component = await loader()
      retryCount = 0 // Reset retry count on success
      return component
    } catch (error) {
      console.error('Component loading failed:', error)
      
      if (retryCount < maxRetries) {
        retryCount++
        console.log(`Retrying component load (attempt ${retryCount}/${maxRetries})`)
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay * retryCount))
        
        // Retry the load
        return retryableLoader()
      }
      
      throw error
    }
  }

  return defineAsyncComponent({
    loader: retryableLoader,
    loadingComponent,
    errorComponent,
    delay,
    timeout,
    suspensible
  })
}

// Preload component for better UX
export function preloadComponent(loader: AsyncComponentLoader): Promise<any> {
  return loader().catch(error => {
    console.warn('Component preload failed:', error)
    return null
  })
}

// Lazy load with intersection observer for viewport-based loading
export function createIntersectionLazyComponent(
  loader: AsyncComponentLoader,
  options: LazyLoadOptions & { rootMargin?: string; threshold?: number } = {}
) {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    ...lazyOptions
  } = options

  return defineAsyncComponent({
    loader: async () => {
      // Create a promise that resolves when the component enters viewport
      return new Promise((resolve, reject) => {
        const placeholder = document.createElement('div')
        placeholder.style.cssText = 'min-height: 1px; visibility: hidden;'
        
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                observer.disconnect()
                loader().then(resolve).catch(reject)
              }
            })
          },
          { rootMargin, threshold }
        )

        // This is a simplified approach - in practice, you'd need to handle
        // the placeholder element insertion more carefully
        observer.observe(placeholder)
      })
    },
    ...lazyOptions
  })
}

// Route-based code splitting helpers
export const lazyRouteComponent = (importFn: () => Promise<any>) => {
  return createLazyComponent(importFn, {
    delay: 0, // No delay for route components
    timeout: 15000 // Shorter timeout for routes
  })
}

// Chunk naming for better debugging
export const createNamedLazyComponent = (
  name: string,
  loader: AsyncComponentLoader,
  options: LazyLoadOptions = {}
) => {
  const namedLoader = () => {
    console.log(`Loading component: ${name}`)
    return loader()
  }

  return createLazyComponent(namedLoader, options)
}

// Image lazy loading utility
export class LazyImageLoader {
  private observer: IntersectionObserver
  private images: Set<HTMLImageElement> = new Set()

  constructor(options: IntersectionObserverInit = {}) {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options
      }
    )
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement
        this.loadImage(img)
        this.observer.unobserve(img)
        this.images.delete(img)
      }
    })
  }

  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src
    const srcset = img.dataset.srcset

    if (src) {
      img.src = src
    }
    if (srcset) {
      img.srcset = srcset
    }

    img.classList.remove('lazy')
    img.classList.add('loaded')
  }

  observe(img: HTMLImageElement) {
    this.images.add(img)
    this.observer.observe(img)
  }

  unobserve(img: HTMLImageElement) {
    this.images.delete(img)
    this.observer.unobserve(img)
  }

  disconnect() {
    this.observer.disconnect()
    this.images.clear()
  }
}

// Create global lazy image loader instance
export const lazyImageLoader = new LazyImageLoader()

// Vue directive for lazy loading images
export const vLazyImage = {
  mounted(el: HTMLImageElement) {
    el.classList.add('lazy')
    lazyImageLoader.observe(el)
  },
  unmounted(el: HTMLImageElement) {
    lazyImageLoader.unobserve(el)
  }
}

// Utility for lazy loading with priority hints
export function createPriorityLazyComponent(
  loader: AsyncComponentLoader,
  priority: 'high' | 'low' = 'low',
  options: LazyLoadOptions = {}
) {
  const priorityOptions = {
    ...options,
    delay: priority === 'high' ? 0 : options.delay || 200,
    timeout: priority === 'high' ? 15000 : options.timeout || 30000
  }

  return createLazyComponent(loader, priorityOptions)
}

// Bundle analyzer helper for development
export function logChunkInfo(chunkName: string, component: any) {
  if (import.meta.env.MODE === 'development') {
    console.log(`Loaded chunk: ${chunkName}`, component)
  }
}

// Preload critical components
export async function preloadCriticalComponents() {
  const criticalComponents = [
    () => import('@/student/views/DashboardView.vue'),
    () => import('@/student/components/layout/StudentLayout.vue'),
    () => import('@/shared/components/ui/LoadingSpinner.vue')
  ]

  const preloadPromises = criticalComponents.map(loader => 
    preloadComponent(loader)
  )

  try {
    await Promise.allSettled(preloadPromises)
    console.log('Critical components preloaded')
  } catch (error) {
    console.warn('Some critical components failed to preload:', error)
  }
}

// Resource hints for better loading performance
export function addResourceHints() {
  // Preconnect to external domains
  const preconnectDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ]

  preconnectDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })

  // DNS prefetch for API domains
  const dnsPrefetchDomains = [
    'https://api.university.edu'
  ]

  dnsPrefetchDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = domain
    document.head.appendChild(link)
  })
}
