// Image optimization utilities for better performance

interface ImageOptimizationOptions {
  quality?: number
  maxWidth?: number
  maxHeight?: number
  format?: 'webp' | 'jpeg' | 'png'
  progressive?: boolean
}

interface ResponsiveImageOptions {
  breakpoints?: number[]
  formats?: string[]
  quality?: number
  lazy?: boolean
}

// Compress image using canvas
export async function compressImage(
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<Blob> {
  const {
    quality = 0.8,
    maxWidth = 1920,
    maxHeight = 1080,
    format = 'jpeg'
  } = options

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }

      canvas.width = width
      canvas.height = height

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        `image/${format}`,
        quality
      )
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}

// Generate responsive image srcset
export function generateSrcSet(
  baseUrl: string,
  options: ResponsiveImageOptions = {}
): string {
  const {
    breakpoints = [320, 640, 768, 1024, 1280, 1536],
    formats = ['webp', 'jpeg'],
    quality = 80
  } = options

  const srcsets: string[] = []

  breakpoints.forEach(width => {
    formats.forEach(format => {
      const url = `${baseUrl}?w=${width}&q=${quality}&f=${format}`
      srcsets.push(`${url} ${width}w`)
    })
  })

  return srcsets.join(', ')
}

// Create picture element with multiple formats
export function createPictureElement(
  src: string,
  alt: string,
  options: ResponsiveImageOptions = {}
): HTMLPictureElement {
  const {
    breakpoints = [320, 640, 768, 1024, 1280],
    formats = ['webp', 'jpeg'],
    quality = 80,
    lazy = true
  } = options

  const picture = document.createElement('picture')

  // Add source elements for each format
  formats.forEach(format => {
    const source = document.createElement('source')
    source.type = `image/${format}`
    source.srcset = generateSrcSet(src, { breakpoints, formats: [format], quality })
    picture.appendChild(source)
  })

  // Add fallback img element
  const img = document.createElement('img')
  img.src = src
  img.alt = alt
  
  if (lazy) {
    img.loading = 'lazy'
    img.decoding = 'async'
  }

  picture.appendChild(img)
  return picture
}

// Preload critical images
export function preloadImage(src: string, priority: 'high' | 'low' = 'low'): Promise<void> {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = src
    
    if (priority === 'high') {
      link.setAttribute('fetchpriority', 'high')
    }

    link.onload = () => resolve()
    link.onerror = () => reject(new Error(`Failed to preload image: ${src}`))
    
    document.head.appendChild(link)
  })
}

// Image format detection and fallback
export function getOptimalImageFormat(): string {
  // Check WebP support
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  
  const webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
  
  if (webpSupport) return 'webp'
  
  // Check AVIF support (modern browsers)
  const avifSupport = canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
  
  if (avifSupport) return 'avif'
  
  return 'jpeg'
}

// Blur placeholder generation
export function generateBlurPlaceholder(
  imageUrl: string,
  width = 40,
  height = 40
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    canvas.width = width
    canvas.height = height

    img.onload = () => {
      ctx?.drawImage(img, 0, 0, width, height)
      
      // Apply blur effect
      if (ctx) {
        ctx.filter = 'blur(2px)'
        ctx.drawImage(canvas, 0, 0)
      }
      
      resolve(canvas.toDataURL('image/jpeg', 0.1))
    }

    img.onerror = () => reject(new Error('Failed to generate blur placeholder'))
    img.crossOrigin = 'anonymous'
    img.src = imageUrl
  })
}

// Image loading with progressive enhancement
export class ProgressiveImageLoader {
  private container: HTMLElement
  private placeholder: string
  private fullImage: string
  private loaded = false

  constructor(container: HTMLElement, placeholder: string, fullImage: string) {
    this.container = container
    this.placeholder = placeholder
    this.fullImage = fullImage
  }

  async load(): Promise<void> {
    // Show placeholder immediately
    this.showPlaceholder()

    // Load full image in background
    try {
      await this.preloadFullImage()
      this.showFullImage()
    } catch (error) {
      console.error('Failed to load full image:', error)
    }
  }

  private showPlaceholder(): void {
    const img = document.createElement('img')
    img.src = this.placeholder
    img.style.cssText = `
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: blur(5px);
      transition: opacity 0.3s ease;
    `
    img.className = 'placeholder-image'
    
    this.container.appendChild(img)
  }

  private preloadFullImage(): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = this.fullImage
    })
  }

  private showFullImage(): void {
    const fullImg = document.createElement('img')
    fullImg.src = this.fullImage
    fullImg.style.cssText = `
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      top: 0;
      left: 0;
    `
    fullImg.className = 'full-image'

    this.container.style.position = 'relative'
    this.container.appendChild(fullImg)

    // Fade in full image
    requestAnimationFrame(() => {
      fullImg.style.opacity = '1'
    })

    // Remove placeholder after transition
    setTimeout(() => {
      const placeholder = this.container.querySelector('.placeholder-image')
      if (placeholder) {
        placeholder.remove()
      }
      this.loaded = true
    }, 300)
  }

  isLoaded(): boolean {
    return this.loaded
  }
}

// Vue composable for image optimization
export function useImageOptimization() {
  const getOptimizedImageUrl = (
    baseUrl: string,
    width?: number,
    height?: number,
    quality = 80,
    format?: string
  ): string => {
    const params = new URLSearchParams()
    
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    
    if (format) {
      params.set('f', format)
    } else {
      params.set('f', getOptimalImageFormat())
    }

    return `${baseUrl}?${params.toString()}`
  }

  const createResponsiveImage = (
    src: string,
    alt: string,
    options: ResponsiveImageOptions = {}
  ): HTMLPictureElement => {
    return createPictureElement(src, alt, options)
  }

  const loadProgressiveImage = async (
    container: HTMLElement,
    placeholder: string,
    fullImage: string
  ): Promise<ProgressiveImageLoader> => {
    const loader = new ProgressiveImageLoader(container, placeholder, fullImage)
    await loader.load()
    return loader
  }

  return {
    getOptimizedImageUrl,
    createResponsiveImage,
    loadProgressiveImage,
    preloadImage,
    compressImage,
    generateBlurPlaceholder
  }
}

// Image CDN integration helpers
export class ImageCDN {
  private baseUrl: string
  private defaultQuality: number

  constructor(baseUrl: string, defaultQuality = 80) {
    this.baseUrl = baseUrl.replace(/\/$/, '')
    this.defaultQuality = defaultQuality
  }

  url(
    path: string,
    options: {
      width?: number
      height?: number
      quality?: number
      format?: string
      crop?: 'fill' | 'fit' | 'crop'
    } = {}
  ): string {
    const {
      width,
      height,
      quality = this.defaultQuality,
      format = getOptimalImageFormat(),
      crop = 'fill'
    } = options

    const params = new URLSearchParams()
    
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    params.set('f', format)
    params.set('fit', crop)

    return `${this.baseUrl}/${path.replace(/^\//, '')}?${params.toString()}`
  }

  responsive(
    path: string,
    breakpoints: number[] = [320, 640, 768, 1024, 1280, 1536]
  ): string {
    return breakpoints
      .map(width => `${this.url(path, { width })} ${width}w`)
      .join(', ')
  }
}

// Default CDN instance (can be configured)
export const imageCDN = new ImageCDN('https://images.university.edu')

// Performance monitoring for images
export function monitorImagePerformance(): void {
  if (!window.PerformanceObserver) return

  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    
    entries.forEach((entry) => {
      const resourceEntry = entry as PerformanceResourceTiming
      if (resourceEntry.initiatorType === 'img') {
        const duration = resourceEntry.responseEnd - resourceEntry.startTime
        const size = (entry as any).transferSize || 0
        
        console.log(`Image loaded: ${entry.name}`)
        console.log(`Duration: ${duration.toFixed(2)}ms`)
        console.log(`Size: ${(size / 1024).toFixed(2)}KB`)
        
        // Report slow images
        if (duration > 1000) {
          console.warn(`Slow image detected: ${entry.name} (${duration.toFixed(2)}ms)`)
        }
        
        // Report large images
        if (size > 500000) { // 500KB
          console.warn(`Large image detected: ${entry.name} (${(size / 1024).toFixed(2)}KB)`)
        }
      }
    })
  })

  observer.observe({ entryTypes: ['resource'] })
}
