import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useEventListener } from '@vueuse/core'

export interface KeyboardShortcut {
  key: string
  description: string
  handler: () => void
  category: string
  modifiers?: {
    ctrl?: boolean
    alt?: boolean
    shift?: boolean
    meta?: boolean
  }
  preventDefault?: boolean
  stopPropagation?: boolean
  enabled?: () => boolean
}

export interface ShortcutCategory {
  name: string
  shortcuts: KeyboardShortcut[]
}

export const useKeyboardShortcuts = () => {
  const shortcuts = ref<Map<string, KeyboardShortcut>>(new Map())
  const isEnabled = ref(true)
  const showHelp = ref(false)

  // Helper function to create shortcut key
  const createShortcutKey = (key: string, modifiers?: KeyboardShortcut['modifiers']): string => {
    const parts: string[] = []

    if (modifiers?.ctrl) parts.push('ctrl')
    if (modifiers?.alt) parts.push('alt')
    if (modifiers?.shift) parts.push('shift')
    if (modifiers?.meta) parts.push('meta')

    parts.push(key.toLowerCase())

    return parts.join('+')
  }

  // Register a keyboard shortcut
  const registerShortcut = (shortcut: KeyboardShortcut) => {
    const shortcutKey = createShortcutKey(shortcut.key, shortcut.modifiers)
    shortcuts.value.set(shortcutKey, shortcut)
  }

  // Unregister a keyboard shortcut
  const unregisterShortcut = (key: string, modifiers?: KeyboardShortcut['modifiers']) => {
    const shortcutKey = createShortcutKey(key, modifiers)
    shortcuts.value.delete(shortcutKey)
  }

  // Clear all shortcuts
  const clearShortcuts = () => {
    shortcuts.value.clear()
  }

  // Handle keyboard events
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isEnabled.value) return

    // Don't trigger shortcuts when typing in inputs (unless specifically allowed)
    const target = event.target as HTMLElement
    const isInputElement = ['INPUT', 'TEXTAREA', 'SELECT'].includes(target.tagName) || target.contentEditable === 'true'

    const shortcutKey = createShortcutKey(event.key, {
      ctrl: event.ctrlKey,
      alt: event.altKey,
      shift: event.shiftKey,
      meta: event.metaKey,
    })

    const shortcut = shortcuts.value.get(shortcutKey)

    if (shortcut) {
      // Check if shortcut is enabled
      if (shortcut.enabled && !shortcut.enabled()) return

      // Skip if we're in an input and the shortcut doesn't allow it
      if (isInputElement && !shortcut.key.startsWith('F') && !event.ctrlKey && !event.metaKey) {
        return
      }

      if (shortcut.preventDefault !== false) {
        event.preventDefault()
      }

      if (shortcut.stopPropagation) {
        event.stopPropagation()
      }

      shortcut.handler()
    }
  }

  // Get shortcuts grouped by category
  const shortcutsByCategory = computed((): ShortcutCategory[] => {
    const categories = new Map<string, KeyboardShortcut[]>()

    shortcuts.value.forEach((shortcut) => {
      if (!categories.has(shortcut.category)) {
        categories.set(shortcut.category, [])
      }
      categories.get(shortcut.category)!.push(shortcut)
    })

    return Array.from(categories.entries()).map(([name, shortcuts]) => ({
      name,
      shortcuts: shortcuts.sort((a, b) => a.key.localeCompare(b.key)),
    }))
  })

  // Format shortcut key for display
  const formatShortcutKey = (shortcut: KeyboardShortcut): string => {
    const parts: string[] = []

    if (shortcut.modifiers?.ctrl) parts.push('Ctrl')
    if (shortcut.modifiers?.alt) parts.push('Alt')
    if (shortcut.modifiers?.shift) parts.push('Shift')
    if (shortcut.modifiers?.meta) parts.push('Cmd')

    // Format special keys
    let key = shortcut.key
    switch (key.toLowerCase()) {
      case 'arrowup':
        key = '↑'
        break
      case 'arrowdown':
        key = '↓'
        break
      case 'arrowleft':
        key = '←'
        break
      case 'arrowright':
        key = '→'
        break
      case ' ':
        key = 'Space'
        break
      case 'enter':
        key = 'Enter'
        break
      case 'escape':
        key = 'Esc'
        break
      case 'tab':
        key = 'Tab'
        break
      default:
        key = key.toUpperCase()
    }

    parts.push(key)

    return parts.join(' + ')
  }

  // Enable/disable shortcuts
  const enable = () => {
    isEnabled.value = true
  }

  const disable = () => {
    isEnabled.value = false
  }

  const toggle = () => {
    isEnabled.value = !isEnabled.value
  }

  // Show/hide help
  const toggleHelp = () => {
    showHelp.value = !showHelp.value
  }

  // Set up event listeners
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })

  return {
    shortcuts,
    isEnabled,
    showHelp,
    shortcutsByCategory,
    registerShortcut,
    unregisterShortcut,
    clearShortcuts,
    formatShortcutKey,
    enable,
    disable,
    toggle,
    toggleHelp,
  }
}

// Predefined shortcut sets for common grading operations
export const createGradingShortcuts = (handlers: {
  save?: () => void
  undo?: () => void
  redo?: () => void
  nextStudent?: () => void
  previousStudent?: () => void
  nextAssessment?: () => void
  previousAssessment?: () => void
  switchToByStudent?: () => void
  switchToByComponent?: () => void
  markFinal?: () => void
  markProvisional?: () => void
  markDraft?: () => void
  openBulkActions?: () => void
  focusSearch?: () => void
  showHelp?: () => void
  closeModal?: () => void
}): KeyboardShortcut[] => {
  const shortcuts: KeyboardShortcut[] = []

  if (handlers.save) {
    shortcuts.push({
      key: 's',
      modifiers: { ctrl: true },
      description: 'Save all changes',
      handler: handlers.save,
      category: 'General',
    })
  }

  if (handlers.undo) {
    shortcuts.push({
      key: 'z',
      modifiers: { ctrl: true },
      description: 'Undo last change',
      handler: handlers.undo,
      category: 'General',
    })
  }

  if (handlers.redo) {
    shortcuts.push({
      key: 'y',
      modifiers: { ctrl: true },
      description: 'Redo last undone change',
      handler: handlers.redo,
      category: 'General',
    })
  }

  if (handlers.nextStudent) {
    shortcuts.push({
      key: 'ArrowDown',
      modifiers: { ctrl: true },
      description: 'Move to next student',
      handler: handlers.nextStudent,
      category: 'Navigation',
    })
  }

  if (handlers.previousStudent) {
    shortcuts.push({
      key: 'ArrowUp',
      modifiers: { ctrl: true },
      description: 'Move to previous student',
      handler: handlers.previousStudent,
      category: 'Navigation',
    })
  }

  if (handlers.nextAssessment) {
    shortcuts.push({
      key: 'ArrowRight',
      modifiers: { ctrl: true },
      description: 'Move to next assessment',
      handler: handlers.nextAssessment,
      category: 'Navigation',
    })
  }

  if (handlers.previousAssessment) {
    shortcuts.push({
      key: 'ArrowLeft',
      modifiers: { ctrl: true },
      description: 'Move to previous assessment',
      handler: handlers.previousAssessment,
      category: 'Navigation',
    })
  }

  if (handlers.switchToByStudent) {
    shortcuts.push({
      key: '1',
      modifiers: { ctrl: true },
      description: 'Switch to grade by student mode',
      handler: handlers.switchToByStudent,
      category: 'View Modes',
    })
  }

  if (handlers.switchToByComponent) {
    shortcuts.push({
      key: '2',
      modifiers: { ctrl: true },
      description: 'Switch to grade by component mode',
      handler: handlers.switchToByComponent,
      category: 'View Modes',
    })
  }

  if (handlers.markFinal) {
    shortcuts.push({
      key: 'f',
      modifiers: { ctrl: true, shift: true },
      description: 'Mark selected grades as final',
      handler: handlers.markFinal,
      category: 'Grading',
    })
  }

  if (handlers.markProvisional) {
    shortcuts.push({
      key: 'p',
      modifiers: { ctrl: true, shift: true },
      description: 'Mark selected grades as provisional',
      handler: handlers.markProvisional,
      category: 'Grading',
    })
  }

  if (handlers.markDraft) {
    shortcuts.push({
      key: 'd',
      modifiers: { ctrl: true, shift: true },
      description: 'Mark selected grades as draft',
      handler: handlers.markDraft,
      category: 'Grading',
    })
  }

  if (handlers.openBulkActions) {
    shortcuts.push({
      key: 'b',
      modifiers: { ctrl: true },
      description: 'Open bulk actions modal',
      handler: handlers.openBulkActions,
      category: 'Bulk Operations',
    })
  }

  if (handlers.focusSearch) {
    shortcuts.push({
      key: '/',
      description: 'Focus search input',
      handler: handlers.focusSearch,
      category: 'Search',
      preventDefault: true,
    })
  }

  if (handlers.showHelp) {
    shortcuts.push({
      key: 'F1',
      description: 'Show keyboard shortcuts help',
      handler: handlers.showHelp,
      category: 'Help',
    })
  }

  if (handlers.closeModal) {
    shortcuts.push({
      key: 'Escape',
      description: 'Close modal or cancel current action',
      handler: handlers.closeModal,
      category: 'General',
    })
  }

  return shortcuts
}

// Focus management utilities
export const useFocusManagement = () => {
  const focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ].join(', ')

  const getFocusableElements = (container?: HTMLElement): HTMLElement[] => {
    const root = container || document
    return Array.from(root.querySelectorAll(focusableSelectors)) as HTMLElement[]
  }

  const getFirstFocusableElement = (container?: HTMLElement): HTMLElement | null => {
    const elements = getFocusableElements(container)
    return elements[0] || null
  }

  const getLastFocusableElement = (container?: HTMLElement): HTMLElement | null => {
    const elements = getFocusableElements(container)
    return elements[elements.length - 1] || null
  }

  const trapFocus = (container: HTMLElement) => {
    const focusableElements = getFocusableElements(container)
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleKeyDown)

    // Focus first element
    firstElement?.focus()

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }

  const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', priority)
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message
    document.body.appendChild(announcement)

    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  return {
    getFocusableElements,
    getFirstFocusableElement,
    getLastFocusableElement,
    trapFocus,
    announceToScreenReader,
  }
}
