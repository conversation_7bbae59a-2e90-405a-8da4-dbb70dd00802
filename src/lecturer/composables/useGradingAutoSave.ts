import { ref, computed } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import type {
  GradeEntry,
  UpdateGradeRequest,
  BulkGradeUpdate,
  BulkOperationType,
} from '@/lecturer/types/models/assessment'
import type { useAssessmentApi } from './useAssessmentApi'

export interface AutoSaveState {
  isSaving: boolean
  lastAutoSave: Date | null
  saveQueue: UpdateGradeRequest[]
  saveErrors: string[]
  retryCount: number
  maxRetries: number
}

export function useGradingAutoSave(
  api: ReturnType<typeof useAssessmentApi>,
  getUnsavedChanges: () => UpdateGradeRequest[],
  onSaveSuccess: (savedGrades: GradeEntry[]) => void,
  autoSaveInterval: number = 2000,
  maxRetries: number = 3,
) {
  // State
  const state = ref<AutoSaveState>({
    isSaving: false,
    lastAutoSave: null,
    saveQueue: [],
    saveErrors: [],
    retryCount: 0,
    maxRetries,
  })

  let autoSaveTimer: number | null = null
  let isAutoSaveActive = false

  // Computed
  const isSaving = computed(() => state.value.isSaving)
  const lastAutoSave = computed(() => state.value.lastAutoSave)
  const hasSaveErrors = computed(() => state.value.saveErrors.length > 0)
  const saveErrors = computed(() => state.value.saveErrors)

  // Internal helper functions
  const addToQueue = (changes: UpdateGradeRequest[]): void => {
    // Add new changes to queue, avoiding duplicates
    changes.forEach((change) => {
      const existingIndex = state.value.saveQueue.findIndex((item) => item.id === change.id)
      if (existingIndex !== -1) {
        // Update existing item in queue
        state.value.saveQueue[existingIndex] = change
      } else {
        // Add new item to queue
        state.value.saveQueue.push(change)
      }
    })
  }

  const clearQueue = (): void => {
    state.value.saveQueue = []
  }

  const addError = (error: string): void => {
    state.value.saveErrors.push(error)
    console.error('Auto-save error:', error)
  }

  const clearErrors = (): void => {
    state.value.saveErrors = []
  }

  // Core save functionality
  const performSave = async (changes: UpdateGradeRequest[]): Promise<GradeEntry[]> => {
    if (changes.length === 0) return []

    try {
      // For single grade updates, use individual API calls
      if (changes.length === 1) {
        const response = await api.grades.update(changes[0])
        if (response.success && response.data) {
          return [response.data]
        } else {
          throw new Error(response.message || 'Failed to save grade')
        }
      }

      // For multiple changes, use bulk update
      const bulkUpdate: BulkGradeUpdate = {
        grade_entries: changes,
        operation_type: BulkOperationType.UPDATE_GRADES,
        validate_before_update: true,
      }

      const response = await api.grades.bulkUpdate(bulkUpdate)
      if (response.success && response.data) {
        return response.data.updated_entries
      } else {
        throw new Error(response.message || 'Failed to bulk save grades')
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Unknown save error')
    }
  }

  // Auto-save with retry logic
  const executeAutoSave = async (): Promise<boolean> => {
    if (state.value.isSaving) return false

    const unsavedChanges = getUnsavedChanges()
    if (unsavedChanges.length === 0) return true

    state.value.isSaving = true
    clearErrors()

    try {
      const savedGrades = await performSave(unsavedChanges)

      // Success callback
      onSaveSuccess(savedGrades)

      state.value.lastAutoSave = new Date()
      state.value.retryCount = 0

      return true
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Auto-save failed'
      addError(errorMessage)

      state.value.retryCount++

      // Retry logic
      if (state.value.retryCount < state.value.maxRetries) {
        console.warn(`Auto-save failed, retrying (${state.value.retryCount}/${state.value.maxRetries})...`)

        // Exponential backoff for retries
        const retryDelay = Math.min(1000 * Math.pow(2, state.value.retryCount - 1), 10000)
        setTimeout(() => {
          executeAutoSave()
        }, retryDelay)
      } else {
        console.error('Auto-save failed after maximum retries')
        // Could trigger a user notification here
      }

      return false
    } finally {
      state.value.isSaving = false
    }
  }

  // Debounced auto-save function
  const debouncedAutoSave = useDebounceFn(executeAutoSave, autoSaveInterval)

  const cancelDebouncedAutoSave = () => {
    // VueUse useDebounceFn doesn't have cancel method, so we'll implement our own
    clearQueue()
  }

  // Manual save function
  const saveChanges = async (): Promise<boolean> => {
    return await executeAutoSave()
  }

  // Auto-save management
  const startAutoSave = (): void => {
    if (isAutoSaveActive) return

    isAutoSaveActive = true

    // Set up periodic auto-save
    autoSaveTimer = setInterval(() => {
      const unsavedChanges = getUnsavedChanges()
      if (unsavedChanges.length > 0 && !state.value.isSaving) {
        debouncedAutoSave()
      }
    }, autoSaveInterval * 2) // Check twice as often as the debounce interval
  }

  const stopAutoSave = (): void => {
    isAutoSaveActive = false

    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
    }

    // Cancel any pending debounced saves
    cancelDebouncedAutoSave()
  }

  const pauseAutoSave = (): void => {
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
    }
    cancelDebouncedAutoSave()
  }

  const resumeAutoSave = (): void => {
    if (isAutoSaveActive && !autoSaveTimer) {
      startAutoSave()
    }
  }

  // Force save (ignores debounce)
  const forceSave = async (): Promise<boolean> => {
    debouncedAutoSave.cancel()
    return await executeAutoSave()
  }

  // Offline support
  const saveToLocalStorage = (changes: UpdateGradeRequest[]): void => {
    try {
      const key = `grading_unsaved_changes_${Date.now()}`
      localStorage.setItem(
        key,
        JSON.stringify({
          timestamp: new Date().toISOString(),
          changes,
        }),
      )
    } catch (error) {
      console.error('Failed to save changes to localStorage:', error)
    }
  }

  const loadFromLocalStorage = (): UpdateGradeRequest[] => {
    try {
      const keys = Object.keys(localStorage).filter((key) => key.startsWith('grading_unsaved_changes_'))
      const allChanges: UpdateGradeRequest[] = []

      keys.forEach((key) => {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}')
          if (data.changes && Array.isArray(data.changes)) {
            allChanges.push(...data.changes)
          }
          // Clean up old entries (older than 24 hours)
          const timestamp = new Date(data.timestamp)
          const now = new Date()
          if (now.getTime() - timestamp.getTime() > 24 * 60 * 60 * 1000) {
            localStorage.removeItem(key)
          }
        } catch (error) {
          console.error('Failed to parse localStorage data:', error)
          localStorage.removeItem(key)
        }
      })

      return allChanges
    } catch (error) {
      console.error('Failed to load changes from localStorage:', error)
      return []
    }
  }

  const clearLocalStorage = (): void => {
    try {
      const keys = Object.keys(localStorage).filter((key) => key.startsWith('grading_unsaved_changes_'))
      keys.forEach((key) => localStorage.removeItem(key))
    } catch (error) {
      console.error('Failed to clear localStorage:', error)
    }
  }

  // Network status handling
  const handleOnline = (): void => {
    console.log('Network connection restored, attempting to save pending changes...')

    // Load any changes saved offline
    const offlineChanges = loadFromLocalStorage()
    if (offlineChanges.length > 0) {
      addToQueue(offlineChanges)
      executeAutoSave().then((success) => {
        if (success) {
          clearLocalStorage()
        }
      })
    }

    // Resume auto-save
    resumeAutoSave()
  }

  const handleOffline = (): void => {
    console.log('Network connection lost, pausing auto-save...')

    // Save current unsaved changes to localStorage
    const unsavedChanges = getUnsavedChanges()
    if (unsavedChanges.length > 0) {
      saveToLocalStorage(unsavedChanges)
    }

    // Pause auto-save
    pauseAutoSave()
  }

  // Set up network status listeners
  if (typeof window !== 'undefined') {
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
  }

  // Cleanup function
  const cleanup = (): void => {
    stopAutoSave()
    clearQueue()
    clearErrors()

    if (typeof window !== 'undefined') {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }

  return {
    // State
    isSaving,
    lastAutoSave,
    hasSaveErrors,
    saveErrors,

    // Actions
    startAutoSave,
    stopAutoSave,
    pauseAutoSave,
    resumeAutoSave,
    saveChanges,
    forceSave,

    // Offline support
    saveToLocalStorage,
    loadFromLocalStorage,
    clearLocalStorage,

    // Cleanup
    cleanup,
  }
}
