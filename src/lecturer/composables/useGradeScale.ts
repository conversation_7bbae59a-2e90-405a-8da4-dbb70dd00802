import { ref, computed } from 'vue'

export interface GradeScaleEntry {
  letter: string
  minPercentage: number
  maxPercentage: number
  gradePoints: number
  color: string
  description: string
  passingGrade: boolean
}

export interface GradeScale {
  id: string
  name: string
  description: string
  entries: GradeScaleEntry[]
  isDefault: boolean
}

// Default Swinburne grade scale
const defaultGradeScale: GradeScale = {
  id: 'swinburne-default',
  name: 'Swinburne Standard Scale',
  description: 'Standard grading scale used across Swinburne University',
  isDefault: true,
  entries: [
    {
      letter: 'HD',
      minPercentage: 80,
      maxPercentage: 100,
      gradePoints: 4.0,
      color: 'text-emerald-600 bg-emerald-50',
      description: 'High Distinction - Exceptional performance',
      passingGrade: true,
    },
    {
      letter: 'D',
      minPercentage: 70,
      maxPercentage: 79,
      gradePoints: 3.0,
      color: 'text-green-600 bg-green-50',
      description: 'Distinction - Superior performance',
      passingGrade: true,
    },
    {
      letter: 'C',
      minPercentage: 60,
      maxPercentage: 69,
      gradePoints: 2.0,
      color: 'text-blue-600 bg-blue-50',
      description: 'Credit - Good performance',
      passingGrade: true,
    },
    {
      letter: 'P',
      minPercentage: 50,
      maxPercentage: 59,
      gradePoints: 1.0,
      color: 'text-yellow-600 bg-yellow-50',
      description: 'Pass - Satisfactory performance',
      passingGrade: true,
    },
    {
      letter: 'N',
      minPercentage: 0,
      maxPercentage: 49,
      gradePoints: 0.0,
      color: 'text-red-600 bg-red-50',
      description: 'Fail - Unsatisfactory performance',
      passingGrade: false,
    },
  ],
}

// Alternative US-style grade scale
const usGradeScale: GradeScale = {
  id: 'us-standard',
  name: 'US Standard Scale',
  description: 'Traditional US letter grading scale',
  isDefault: false,
  entries: [
    {
      letter: 'A+',
      minPercentage: 97,
      maxPercentage: 100,
      gradePoints: 4.0,
      color: 'text-emerald-600 bg-emerald-50',
      description: 'Excellent - Outstanding achievement',
      passingGrade: true,
    },
    {
      letter: 'A',
      minPercentage: 93,
      maxPercentage: 96,
      gradePoints: 4.0,
      color: 'text-emerald-600 bg-emerald-50',
      description: 'Excellent - Superior achievement',
      passingGrade: true,
    },
    {
      letter: 'A-',
      minPercentage: 90,
      maxPercentage: 92,
      gradePoints: 3.7,
      color: 'text-green-600 bg-green-50',
      description: 'Excellent - High achievement',
      passingGrade: true,
    },
    {
      letter: 'B+',
      minPercentage: 87,
      maxPercentage: 89,
      gradePoints: 3.3,
      color: 'text-green-600 bg-green-50',
      description: 'Good - Above average achievement',
      passingGrade: true,
    },
    {
      letter: 'B',
      minPercentage: 83,
      maxPercentage: 86,
      gradePoints: 3.0,
      color: 'text-blue-600 bg-blue-50',
      description: 'Good - Average achievement',
      passingGrade: true,
    },
    {
      letter: 'B-',
      minPercentage: 80,
      maxPercentage: 82,
      gradePoints: 2.7,
      color: 'text-blue-600 bg-blue-50',
      description: 'Good - Below average achievement',
      passingGrade: true,
    },
    {
      letter: 'C+',
      minPercentage: 77,
      maxPercentage: 79,
      gradePoints: 2.3,
      color: 'text-yellow-600 bg-yellow-50',
      description: 'Satisfactory - Above minimum achievement',
      passingGrade: true,
    },
    {
      letter: 'C',
      minPercentage: 73,
      maxPercentage: 76,
      gradePoints: 2.0,
      color: 'text-yellow-600 bg-yellow-50',
      description: 'Satisfactory - Minimum achievement',
      passingGrade: true,
    },
    {
      letter: 'C-',
      minPercentage: 70,
      maxPercentage: 72,
      gradePoints: 1.7,
      color: 'text-orange-600 bg-orange-50',
      description: 'Satisfactory - Below minimum achievement',
      passingGrade: true,
    },
    {
      letter: 'D+',
      minPercentage: 67,
      maxPercentage: 69,
      gradePoints: 1.3,
      color: 'text-orange-600 bg-orange-50',
      description: 'Poor - Marginal achievement',
      passingGrade: true,
    },
    {
      letter: 'D',
      minPercentage: 65,
      maxPercentage: 66,
      gradePoints: 1.0,
      color: 'text-red-600 bg-red-50',
      description: 'Poor - Minimal achievement',
      passingGrade: true,
    },
    {
      letter: 'F',
      minPercentage: 0,
      maxPercentage: 64,
      gradePoints: 0.0,
      color: 'text-red-600 bg-red-50',
      description: 'Fail - Inadequate achievement',
      passingGrade: false,
    },
  ],
}

export function useGradeScale() {
  // Available grade scales
  const availableScales = ref<GradeScale[]>([defaultGradeScale, usGradeScale])
  
  // Current active scale
  const currentScale = ref<GradeScale>(defaultGradeScale)
  
  // Set the active grade scale
  const setGradeScale = (scaleId: string) => {
    const scale = availableScales.value.find(s => s.id === scaleId)
    if (scale) {
      currentScale.value = scale
    }
  }
  
  // Convert percentage to letter grade
  const getLetterGrade = (percentage: number): GradeScaleEntry | null => {
    if (percentage < 0 || percentage > 100) return null
    
    return currentScale.value.entries.find(
      entry => percentage >= entry.minPercentage && percentage <= entry.maxPercentage
    ) || null
  }
  
  // Get grade color classes
  const getGradeColor = (percentage: number): string => {
    const grade = getLetterGrade(percentage)
    return grade?.color || 'text-gray-600 bg-gray-50'
  }
  
  // Get grade letter only
  const getGradeLetter = (percentage: number): string => {
    const grade = getLetterGrade(percentage)
    return grade?.letter || 'N/A'
  }
  
  // Get grade description
  const getGradeDescription = (percentage: number): string => {
    const grade = getLetterGrade(percentage)
    return grade?.description || 'No grade available'
  }
  
  // Check if grade is passing
  const isPassingGrade = (percentage: number): boolean => {
    const grade = getLetterGrade(percentage)
    return grade?.passingGrade || false
  }
  
  // Get grade points
  const getGradePoints = (percentage: number): number => {
    const grade = getLetterGrade(percentage)
    return grade?.gradePoints || 0
  }
  
  // Get grade statistics for a set of percentages
  const getGradeStatistics = (percentages: number[]) => {
    const validPercentages = percentages.filter(p => p >= 0 && p <= 100)
    
    if (validPercentages.length === 0) {
      return {
        totalStudents: 0,
        averagePercentage: 0,
        averageGradePoints: 0,
        distribution: {},
        passingRate: 0,
      }
    }
    
    const distribution: Record<string, number> = {}
    let totalGradePoints = 0
    let passingCount = 0
    
    validPercentages.forEach(percentage => {
      const grade = getLetterGrade(percentage)
      if (grade) {
        distribution[grade.letter] = (distribution[grade.letter] || 0) + 1
        totalGradePoints += grade.gradePoints
        if (grade.passingGrade) passingCount++
      }
    })
    
    return {
      totalStudents: validPercentages.length,
      averagePercentage: validPercentages.reduce((sum, p) => sum + p, 0) / validPercentages.length,
      averageGradePoints: totalGradePoints / validPercentages.length,
      distribution,
      passingRate: (passingCount / validPercentages.length) * 100,
    }
  }
  
  // Computed properties
  const scaleEntries = computed(() => currentScale.value.entries)
  const scaleName = computed(() => currentScale.value.name)
  const scaleDescription = computed(() => currentScale.value.description)
  
  return {
    // State
    availableScales,
    currentScale,
    
    // Computed
    scaleEntries,
    scaleName,
    scaleDescription,
    
    // Methods
    setGradeScale,
    getLetterGrade,
    getGradeColor,
    getGradeLetter,
    getGradeDescription,
    isPassingGrade,
    getGradePoints,
    getGradeStatistics,
  }
}
