import { useBase<PERSON><PERSON> } from '@/shared/composables/useBaseApi'
import type { ApiResponse } from '@/shared/types/api/api'
import type {
  Assessment,
  AssessmentDetail,
  GradeEntry,
  GradeStatistics,
  StudentPerformance,
  GradingSession,
  BulkGradeUpdate,
  BulkOperationResult,
  ExportOptions,
  ExportResult,
  ValidationResult,
  WeightValidation,
  CreateAssessmentRequest,
  UpdateAssessmentRequest,
  UpdateGradeRequest,
  GradeQueryParams,
} from '@/lecturer/types/models/assessment'

export function useAssessmentApi() {
  const baseApi = useBaseApi()

  return {
    // Assessment Management
    assessments: {
      /**
       * Get all assessments for a course offering
       */
      async getAll(courseOfferingId: number): Promise<ApiResponse<Assessment[]>> {
        return baseApi.get<Assessment[]>(`/lecturer/courses/${courseOfferingId}/assessments`)
      },

      /**
       * Get a specific assessment by ID
       */
      async getById(assessmentId: number): Promise<ApiResponse<Assessment>> {
        return baseApi.get<Assessment>(`/lecturer/assessments/${assessmentId}`)
      },

      /**
       * Create a new assessment
       */
      async create(assessmentData: CreateAssessmentRequest): Promise<ApiResponse<Assessment>> {
        return baseApi.post<Assessment>('/lecturer/assessments', assessmentData)
      },

      /**
       * Update an existing assessment
       */
      async update(assessmentData: UpdateAssessmentRequest): Promise<ApiResponse<Assessment>> {
        return baseApi.put<Assessment>(`/lecturer/assessments/${assessmentData.id}`, assessmentData)
      },

      /**
       * Delete an assessment
       */
      async delete(assessmentId: number): Promise<ApiResponse<void>> {
        return baseApi.delete<void>(`/lecturer/assessments/${assessmentId}`)
      },

      /**
       * Duplicate an assessment
       */
      async duplicate(assessmentId: number, newName?: string): Promise<ApiResponse<Assessment>> {
        return baseApi.post<Assessment>(`/lecturer/assessments/${assessmentId}/duplicate`, {
          name: newName,
        })
      },

      /**
       * Validate assessment weights for a course
       */
      async validateWeights(courseOfferingId: number): Promise<ApiResponse<WeightValidation>> {
        return baseApi.get<WeightValidation>(`/lecturer/courses/${courseOfferingId}/assessments/validate-weights`)
      },

      /**
       * Get assessment statistics
       */
      async getStatistics(assessmentId: number): Promise<ApiResponse<GradeStatistics>> {
        return baseApi.get<GradeStatistics>(`/lecturer/assessments/${assessmentId}/statistics`)
      },
    },

    // Assessment Details Management
    assessmentDetails: {
      /**
       * Get all details for an assessment
       */
      async getAll(assessmentId: number): Promise<ApiResponse<AssessmentDetail[]>> {
        return baseApi.get<AssessmentDetail[]>(`/lecturer/assessments/${assessmentId}/details`)
      },

      /**
       * Create a new assessment detail
       */
      async create(
        assessmentId: number,
        detailData: Omit<AssessmentDetail, 'id' | 'assessment_id' | 'created_at' | 'updated_at'>,
      ): Promise<ApiResponse<AssessmentDetail>> {
        return baseApi.post<AssessmentDetail>(`/lecturer/assessments/${assessmentId}/details`, detailData)
      },

      /**
       * Update an assessment detail
       */
      async update(detailId: number, detailData: Partial<AssessmentDetail>): Promise<ApiResponse<AssessmentDetail>> {
        return baseApi.put<AssessmentDetail>(`/lecturer/assessment-details/${detailId}`, detailData)
      },

      /**
       * Delete an assessment detail
       */
      async delete(detailId: number): Promise<ApiResponse<void>> {
        return baseApi.delete<void>(`/lecturer/assessment-details/${detailId}`)
      },

      /**
       * Reorder assessment details
       */
      async reorder(assessmentId: number, detailIds: number[]): Promise<ApiResponse<AssessmentDetail[]>> {
        return baseApi.put<AssessmentDetail[]>(`/lecturer/assessments/${assessmentId}/details/reorder`, {
          detail_ids: detailIds,
        })
      },
    },

    // Grading Management
    grades: {
      /**
       * Get grades with filtering and pagination
       */
      async getAll(params: GradeQueryParams): Promise<ApiResponse<GradeEntry[]>> {
        const queryString = new URLSearchParams()

        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach((v) => queryString.append(`${key}[]`, v.toString()))
            } else {
              queryString.append(key, value.toString())
            }
          }
        })

        return baseApi.get<GradeEntry[]>(`/lecturer/grades?${queryString.toString()}`)
      },

      /**
       * Get grades for a specific student
       */
      async getByStudent(studentId: number, courseOfferingId: number): Promise<ApiResponse<GradeEntry[]>> {
        return baseApi.get<GradeEntry[]>(
          `/lecturer/students/${studentId}/grades?course_offering_id=${courseOfferingId}`,
        )
      },

      /**
       * Get grades for a specific assessment
       */
      async getByAssessment(assessmentId: number): Promise<ApiResponse<GradeEntry[]>> {
        return baseApi.get<GradeEntry[]>(`/lecturer/assessments/${assessmentId}/grades`)
      },

      /**
       * Get grades for a specific assessment detail
       */
      async getByAssessmentDetail(assessmentDetailId: number): Promise<ApiResponse<GradeEntry[]>> {
        return baseApi.get<GradeEntry[]>(`/lecturer/assessment-details/${assessmentDetailId}/grades`)
      },

      /**
       * Update a single grade
       */
      async update(gradeData: UpdateGradeRequest): Promise<ApiResponse<GradeEntry>> {
        return baseApi.put<GradeEntry>(`/lecturer/grades/${gradeData.id}`, gradeData)
      },

      /**
       * Bulk update grades
       */
      async bulkUpdate(bulkData: BulkGradeUpdate): Promise<ApiResponse<BulkOperationResult>> {
        return baseApi.post<BulkOperationResult>('/lecturer/grades/bulk-update', bulkData)
      },

      /**
       * Create or update multiple grades
       */
      async upsertMany(grades: Partial<GradeEntry>[]): Promise<ApiResponse<GradeEntry[]>> {
        return baseApi.post<GradeEntry[]>('/lecturer/grades/upsert-many', { grades })
      },

      /**
       * Delete a grade entry
       */
      async delete(gradeId: number): Promise<ApiResponse<void>> {
        return baseApi.delete<void>(`/lecturer/grades/${gradeId}`)
      },

      /**
       * Validate grade data before saving
       */
      async validate(gradeData: Partial<GradeEntry>): Promise<ApiResponse<ValidationResult>> {
        return baseApi.post<ValidationResult>('/lecturer/grades/validate', gradeData)
      },

      /**
       * Get grade history for audit trail
       */
      async getHistory(gradeId: number): Promise<ApiResponse<any[]>> {
        return baseApi.get<any[]>(`/lecturer/grades/${gradeId}/history`)
      },
    },

    // Student Performance Analysis
    performance: {
      /**
       * Get performance data for all students in a course
       */
      async getByCourse(courseOfferingId: number): Promise<ApiResponse<StudentPerformance[]>> {
        return baseApi.get<StudentPerformance[]>(`/lecturer/courses/${courseOfferingId}/performance`)
      },

      /**
       * Get performance data for a specific student
       */
      async getByStudent(studentId: number, courseOfferingId: number): Promise<ApiResponse<StudentPerformance>> {
        return baseApi.get<StudentPerformance>(
          `/lecturer/students/${studentId}/performance?course_offering_id=${courseOfferingId}`,
        )
      },

      /**
       * Get at-risk students
       */
      async getAtRiskStudents(courseOfferingId: number): Promise<ApiResponse<StudentPerformance[]>> {
        return baseApi.get<StudentPerformance[]>(`/lecturer/courses/${courseOfferingId}/performance/at-risk`)
      },

      /**
       * Get exceptional students
       */
      async getExceptionalStudents(courseOfferingId: number): Promise<ApiResponse<StudentPerformance[]>> {
        return baseApi.get<StudentPerformance[]>(`/lecturer/courses/${courseOfferingId}/performance/exceptional`)
      },

      /**
       * Update student performance alerts
       */
      async updateAlerts(studentId: number, courseOfferingId: number, alerts: any[]): Promise<ApiResponse<void>> {
        return baseApi.put<void>(`/lecturer/students/${studentId}/alerts`, {
          course_offering_id: courseOfferingId,
          alerts,
        })
      },
    },

    // Grading Sessions
    sessions: {
      /**
       * Create a new grading session
       */
      async create(
        sessionData: Omit<GradingSession, 'id' | 'created_at' | 'updated_at'>,
      ): Promise<ApiResponse<GradingSession>> {
        return baseApi.post<GradingSession>('/lecturer/grading-sessions', sessionData)
      },

      /**
       * Get an existing grading session
       */
      async get(sessionId: string): Promise<ApiResponse<GradingSession>> {
        return baseApi.get<GradingSession>(`/lecturer/grading-sessions/${sessionId}`)
      },

      /**
       * Update a grading session
       */
      async update(sessionId: string, sessionData: Partial<GradingSession>): Promise<ApiResponse<GradingSession>> {
        return baseApi.put<GradingSession>(`/lecturer/grading-sessions/${sessionId}`, sessionData)
      },

      /**
       * Delete a grading session
       */
      async delete(sessionId: string): Promise<ApiResponse<void>> {
        return baseApi.delete<void>(`/lecturer/grading-sessions/${sessionId}`)
      },

      /**
       * Save unsaved changes from a session
       */
      async saveChanges(sessionId: string, changes: GradeEntry[]): Promise<ApiResponse<BulkOperationResult>> {
        return baseApi.post<BulkOperationResult>(`/lecturer/grading-sessions/${sessionId}/save`, {
          changes,
        })
      },

      /**
       * Auto-save session state
       */
      async autoSave(sessionId: string, sessionData: Partial<GradingSession>): Promise<ApiResponse<void>> {
        return baseApi.patch<void>(`/lecturer/grading-sessions/${sessionId}/auto-save`, sessionData)
      },
    },

    // Export Functionality
    export: {
      /**
       * Export assessment data
       */
      async exportData(courseOfferingId: number, options: ExportOptions): Promise<ApiResponse<ExportResult>> {
        return baseApi.post<ExportResult>(`/lecturer/courses/${courseOfferingId}/assessments/export`, options)
      },

      /**
       * Export grades for a specific assessment
       */
      async exportAssessmentGrades(assessmentId: number, options: ExportOptions): Promise<ApiResponse<ExportResult>> {
        return baseApi.post<ExportResult>(`/lecturer/assessments/${assessmentId}/export`, options)
      },

      /**
       * Export student performance report
       */
      async exportPerformanceReport(
        courseOfferingId: number,
        options: ExportOptions,
      ): Promise<ApiResponse<ExportResult>> {
        return baseApi.post<ExportResult>(`/lecturer/courses/${courseOfferingId}/performance/export`, options)
      },

      /**
       * Get export status
       */
      async getExportStatus(
        exportId: string,
      ): Promise<ApiResponse<{ status: string; progress: number; file_url?: string }>> {
        return baseApi.get<{ status: string; progress: number; file_url?: string }>(
          `/lecturer/exports/${exportId}/status`,
        )
      },

      /**
       * Download exported file
       */
      async downloadFile(fileUrl: string): Promise<Blob> {
        const response = await baseApi.apiCall(fileUrl, {
          method: 'GET',
          headers: {
            Accept: 'application/octet-stream',
          },
        })

        if (response.success && response.data) {
          return response.data as Blob
        }

        throw new Error('Failed to download file')
      },
    },

    // Analytics and Reporting
    analytics: {
      /**
       * Get course assessment analytics
       */
      async getCourseAnalytics(courseOfferingId: number, period?: string): Promise<ApiResponse<any>> {
        const params = period ? `?period=${period}` : ''
        return baseApi.get<any>(`/lecturer/courses/${courseOfferingId}/analytics${params}`)
      },

      /**
       * Get assessment comparison data
       */
      async getAssessmentComparison(courseOfferingId: number, assessmentIds: number[]): Promise<ApiResponse<any>> {
        const params = assessmentIds.map((id) => `assessment_ids[]=${id}`).join('&')
        return baseApi.get<any>(`/lecturer/courses/${courseOfferingId}/analytics/comparison?${params}`)
      },

      /**
       * Get grade distribution data
       */
      async getGradeDistribution(assessmentId: number): Promise<ApiResponse<any>> {
        return baseApi.get<any>(`/lecturer/assessments/${assessmentId}/analytics/distribution`)
      },

      /**
       * Get performance trends
       */
      async getPerformanceTrends(courseOfferingId: number, timeframe: string = '30d'): Promise<ApiResponse<any>> {
        return baseApi.get<any>(`/lecturer/courses/${courseOfferingId}/analytics/trends?timeframe=${timeframe}`)
      },
    },

    // Utility Methods
    utils: {
      /**
       * Calculate weighted scores
       */
      async calculateWeightedScores(courseOfferingId: number, studentIds?: number[]): Promise<ApiResponse<any>> {
        const body = studentIds ? { student_ids: studentIds } : {}
        return baseApi.post<any>(`/lecturer/courses/${courseOfferingId}/calculate-weighted-scores`, body)
      },

      /**
       * Recalculate all grades for a course
       */
      async recalculateGrades(courseOfferingId: number): Promise<ApiResponse<BulkOperationResult>> {
        return baseApi.post<BulkOperationResult>(`/lecturer/courses/${courseOfferingId}/recalculate-grades`)
      },

      /**
       * Sync grades with external system
       */
      async syncGrades(courseOfferingId: number): Promise<ApiResponse<any>> {
        return baseApi.post<any>(`/lecturer/courses/${courseOfferingId}/sync-grades`)
      },

      /**
       * Get system configuration for assessments
       */
      async getConfig(): Promise<ApiResponse<any>> {
        return baseApi.get<any>('/lecturer/assessments/config')
      },
    },
  }
}
