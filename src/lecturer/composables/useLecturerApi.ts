import { useBase<PERSON><PERSON> } from '@/shared/composables/useBaseApi'
import { useAuthStore } from '@/shared/stores/auth'

export function useLecturerApi() {
  const authStore = useAuthStore()
  const baseApi = useBaseApi()

  // Create lecturer-specific API instance with base URL prefix
  const lecturerApi = {
    ...baseApi,

    // Lecturer Dashboard APIs
    dashboard: {
      getData: () => baseApi.get(`/lecturer/dashboard`),
      getTeachingSummary: () => baseApi.get(`/lecturer/dashboard/teaching-summary`),
      getStudentAlerts: () => baseApi.get(`/lecturer/dashboard/student-alerts`),
      getRecentActivities: () => baseApi.get(`/lecturer/dashboard/activities`),
    },

    // Lecturer Course Management APIs
    courses: {
      getOfferings: () => baseApi.get(`/lecturer/courses`),
      getById: (courseId: string) => baseApi.get(`/lecturer/courses/${courseId}`),
      updateCourse: (courseId: string, data: any) => baseApi.put(`/lecturer/courses/${courseId}`, data),
      uploadMaterial: (courseId: string, file: FormData) =>
        baseApi.post(`/lecturer/courses/${courseId}/materials`, file),
    },

    // Lecturer Attendance Management APIs
    attendance: {
      getSessions: () => baseApi.get(`/lecturer/attendance/sessions`),
      getSessionById: (sessionId: string) => baseApi.get(`/lecturer/attendance/sessions/${sessionId}`),
      markAttendance: (sessionId: string, attendanceData: any) =>
        baseApi.post(`/lecturer/attendance/sessions/${sessionId}/mark`, attendanceData),
      updateAttendance: (sessionId: string, studentId: string, status: string) =>
        baseApi.put(`/lecturer/attendance/sessions/${sessionId}/students/${studentId}`, { status }),
    },

    // Lecturer Student Management APIs
    students: {
      getByCourse: (courseId: string) => baseApi.get(`/lecturer/courses/${courseId}/students`),
      getAlerts: () => baseApi.get(`/lecturer/students/alerts`),
      dismissAlert: (alertId: string) => baseApi.delete(`/lecturer/students/alerts/${alertId}`),
      addNote: (studentId: string, note: string) => baseApi.post(`/lecturer/students/${studentId}/notes`, { note }),
    },

    // Lecturer Timetable APIs
    timetable: {
      get: () => baseApi.get(`/lecturer/timetable`),
      getByWeek: (weekStart: string) => baseApi.get(`/lecturer/timetable/week/${weekStart}`),
      createSession: (sessionData: any) => baseApi.post(`/lecturer/timetable/sessions`, sessionData),
      updateSession: (sessionId: string, sessionData: any) =>
        baseApi.put(`/lecturer/timetable/sessions/${sessionId}`, sessionData),
    },
  }

  return lecturerApi
}
