import { ref, computed, onMounted, onUnmounted, readonly, watch } from 'vue'
import { useAssessmentApi } from './useAssessmentApi'
import { useWeightValidation, type ValidationMessage } from './useWeightValidation'
import type {
  Assessment,
  AssessmentDetail,
  WeightValidation,
  ValidationError,
  ValidationWarning,
  CreateAssessmentRequest,
  UpdateAssessmentRequest,
} from '@/lecturer/types/models/assessment'

export interface AssessmentManagementState {
  assessments: Assessment[]
  loading: boolean
  error: string | null
  validationErrors: ValidationError[]
  validationWarnings: ValidationWarning[]
  weightValidation: WeightValidation | null
  lastUpdated: Date | null
  realTimeValidation: boolean
  pendingWeightChanges: Map<number, number>
}

export function useAssessmentManagement(courseOfferingId: number) {
  // Local state
  const state = ref<AssessmentManagementState>({
    assessments: [],
    loading: false,
    error: null,
    validationErrors: [],
    validationWarnings: [],
    weightValidation: null,
    lastUpdated: null,
    realTimeValidation: true,
    pendingWeightChanges: new Map(),
  })

  // API instance
  const api = useAssessmentApi()

  // Real-time weight validation integration
  const {
    totalWeight,
    isWeightValid,
    weightExceeded,
    weightRemaining,
    weightPercentage,
    validationStatus,
    statusColor,
    statusIcon,
    componentValidations,
    hasComponentErrors,
    validationMessages,
    validateWeightChange,
    validateComponentWeights,
    getWeightSuggestions,
  } = useWeightValidation(computed(() => state.value.assessments))

  // Enhanced computed properties
  const hasValidationErrors = computed(() => {
    return state.value.validationErrors.length > 0 || validationMessages.value.some((msg) => msg.type === 'error')
  })

  const hasValidationWarnings = computed(() => {
    return state.value.validationWarnings.length > 0 || validationMessages.value.some((msg) => msg.type === 'warning')
  })

  const allValidationMessages = computed(() => {
    const messages: ValidationMessage[] = [...validationMessages.value]

    // Convert legacy validation errors to new format
    state.value.validationErrors.forEach((error) => {
      messages.push({
        type: 'error',
        message: error.message,
        code: error.code,
        severity: 'high',
      })
    })

    // Convert legacy validation warnings to new format
    state.value.validationWarnings.forEach((warning) => {
      messages.push({
        type: 'warning',
        message: warning.message,
        code: warning.code,
        severity: warning.severity,
      })
    })

    return messages
  })

  const weightValidationSummary = computed(() => ({
    totalWeight: totalWeight.value,
    isValid: isWeightValid.value,
    status: validationStatus.value,
    statusColor: statusColor.value,
    statusIcon: statusIcon.value,
    weightExceeded: weightExceeded.value,
    weightRemaining: weightRemaining.value,
    weightPercentage: weightPercentage.value,
    hasComponentErrors: hasComponentErrors.value,
    componentValidations: componentValidations.value,
    messages: allValidationMessages.value,
    suggestions: getWeightSuggestions(),
  }))

  const assessmentsByType = computed(() => {
    const grouped = state.value.assessments.reduce(
      (acc, assessment) => {
        if (!acc[assessment.type]) {
          acc[assessment.type] = []
        }
        acc[assessment.type].push(assessment)
        return acc
      },
      {} as Record<string, Assessment[]>,
    )
    return grouped
  })

  const requiredAssessments = computed(() => {
    return state.value.assessments.filter((assessment) => assessment.is_required)
  })

  const optionalAssessments = computed(() => {
    return state.value.assessments.filter((assessment) => !assessment.is_required)
  })

  // Internal helper functions
  const clearErrors = () => {
    state.value.error = null
    state.value.validationErrors = []
    state.value.validationWarnings = []
  }

  const setError = (error: string) => {
    state.value.error = error
    console.error('Assessment Management Error:', error)
  }

  const updateLastModified = () => {
    state.value.lastUpdated = new Date()
  }

  // Real-time weight validation methods
  const validateWeightChangeRealTime = (assessmentId: number, newWeight: number) => {
    if (!state.value.realTimeValidation) return { isValid: true, message: '' }

    const result = validateWeightChange(assessmentId, newWeight)

    // Store pending change for visual feedback
    if (newWeight !== (state.value.assessments.find((a) => a.id === assessmentId)?.weight || 0)) {
      state.value.pendingWeightChanges.set(assessmentId, newWeight)
    } else {
      state.value.pendingWeightChanges.delete(assessmentId)
    }

    return result
  }

  const validateComponentWeightsRealTime = (assessmentId: number, componentWeights: number[]) => {
    if (!state.value.realTimeValidation) return { isValid: true, message: '' }

    return validateComponentWeights(assessmentId, componentWeights)
  }

  const getWeightChangePreview = (assessmentId: number, newWeight: number) => {
    const currentAssessment = state.value.assessments.find((a) => a.id === assessmentId)
    if (!currentAssessment) return null

    const otherAssessmentsWeight = state.value.assessments
      .filter((a) => a.id !== assessmentId)
      .reduce((sum, a) => sum + a.weight, 0)

    const projectedTotal = otherAssessmentsWeight + newWeight
    const weightDifference = newWeight - currentAssessment.weight

    return {
      currentWeight: currentAssessment.weight,
      newWeight,
      weightDifference,
      projectedTotal,
      isValid: projectedTotal <= 100 && newWeight > 0,
      exceedsLimit: projectedTotal > 100,
      remainingAfterChange: Math.max(0, 100 - projectedTotal),
    }
  }

  const clearPendingWeightChanges = () => {
    state.value.pendingWeightChanges.clear()
  }

  const applyPendingWeightChanges = async () => {
    const updates: Promise<Assessment | null>[] = []

    for (const [assessmentId, newWeight] of Array.from(state.value.pendingWeightChanges)) {
      updates.push(updateAssessment({ id: assessmentId, weight: newWeight }))
    }

    const results = await Promise.all(updates)
    clearPendingWeightChanges()

    return results.filter((result) => result !== null)
  }

  const toggleRealTimeValidation = (enabled: boolean) => {
    state.value.realTimeValidation = enabled
    if (!enabled) {
      clearPendingWeightChanges()
    }
  }

  // Enhanced weight validation with API fallback
  const validateWeights = async (): Promise<WeightValidation | null> => {
    try {
      // Use local validation first for immediate feedback
      const localValidation: WeightValidation = {
        total_weight: totalWeight.value,
        is_valid: isWeightValid.value && !hasComponentErrors.value,
        exceeds_limit: totalWeight.value > 100,
        missing_weight: weightRemaining.value,
        component_validations: componentValidations.value,
      }

      // Store local validation
      state.value.weightValidation = localValidation

      // Optionally validate with API for server-side rules
      try {
        const response = await api.assessments.validateWeights(courseOfferingId)
        if (response.success && response.data) {
          state.value.weightValidation = response.data
          return response.data
        }
      } catch (apiError) {
        console.warn('API weight validation failed, using local validation:', apiError)
      }

      return localValidation
    } catch (error) {
      console.error('Weight validation failed:', error)
      return null
    }
  }

  // CRUD operations
  const fetchAssessments = async (): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.assessments.getAll(courseOfferingId)
      if (response.success && response.data) {
        // Handle the actual API response structure with 'components' field
        if (response.data.components && Array.isArray(response.data.components)) {
          state.value.assessments = response.data.components
        } else if (Array.isArray(response.data)) {
          // Fallback for direct array response
          state.value.assessments = response.data
        } else {
          console.warn('Unexpected API response structure:', response.data)
          state.value.assessments = []
        }
        updateLastModified()
        // Validate weights after fetching
        await validateWeights()
      } else {
        setError(response.message || 'Failed to fetch assessments')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch assessments')
    } finally {
      state.value.loading = false
    }
  }

  const createAssessment = async (assessmentData: CreateAssessmentRequest): Promise<Assessment | null> => {
    state.value.loading = true
    clearErrors()

    try {
      // Real-time validation before creating
      const validationResult = validateWeightChange(-1, assessmentData.weight) // Use -1 for new assessment
      if (!validationResult.isValid) {
        const error: ValidationError = {
          field: 'weight',
          message: validationResult.message,
          code: 'WEIGHT_VALIDATION_FAILED',
          value: assessmentData.weight,
        }
        state.value.validationErrors = [error]
        return null
      }

      // Additional validation for weight exceeding limit
      const newTotalWeight = totalWeight.value + assessmentData.weight
      if (newTotalWeight > 100) {
        const error: ValidationError = {
          field: 'weight',
          message: `Total weight would exceed 100% (current: ${totalWeight.value.toFixed(1)}%, adding: ${assessmentData.weight}%, total: ${newTotalWeight.toFixed(1)}%)`,
          code: 'WEIGHT_EXCEEDS_LIMIT',
          value: newTotalWeight,
        }
        state.value.validationErrors = [error]
        return null
      }

      const response = await api.assessments.create(assessmentData)
      if (response.success && response.data) {
        state.value.assessments.push(response.data)
        updateLastModified()
        await validateWeights()
        return response.data
      } else {
        setError(response.message || 'Failed to create assessment')
        return null
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create assessment')
      return null
    } finally {
      state.value.loading = false
    }
  }

  const updateAssessment = async (assessmentData: UpdateAssessmentRequest): Promise<Assessment | null> => {
    state.value.loading = true
    clearErrors()

    try {
      // Find the existing assessment to calculate weight difference
      const existingAssessment = state.value.assessments.find((a) => a.id === assessmentData.id)
      if (!existingAssessment) {
        setError('Assessment not found')
        return null
      }

      // Real-time validation for weight changes
      if (assessmentData.weight !== undefined) {
        const validationResult = validateWeightChange(assessmentData.id, assessmentData.weight)
        if (!validationResult.isValid) {
          const error: ValidationError = {
            field: 'weight',
            message: validationResult.message,
            code: 'WEIGHT_VALIDATION_FAILED',
            value: assessmentData.weight,
          }
          state.value.validationErrors = [error]
          return null
        }

        // Additional detailed validation
        const weightDifference = assessmentData.weight - existingAssessment.weight
        const newTotalWeight = totalWeight.value + weightDifference

        if (newTotalWeight > 100) {
          const error: ValidationError = {
            field: 'weight',
            message: `Total weight would exceed 100% (current: ${totalWeight.value.toFixed(1)}%, change: ${weightDifference > 0 ? '+' : ''}${weightDifference.toFixed(1)}%, new total: ${newTotalWeight.toFixed(1)}%)`,
            code: 'WEIGHT_EXCEEDS_LIMIT',
            value: newTotalWeight,
          }
          state.value.validationErrors = [error]
          return null
        }

        // Provide positive feedback for valid changes
        if (validationResult.severity === 'low' && newTotalWeight === 100) {
          const warning: ValidationWarning = {
            field: 'weight',
            message: 'This change will complete your weight allocation to 100%',
            code: 'WEIGHT_ALLOCATION_COMPLETE',
            severity: 'low',
          }
          state.value.validationWarnings = [warning]
        }
      }

      const response = await api.assessments.update(assessmentData)
      if (response.success && response.data) {
        const index = state.value.assessments.findIndex((a) => a.id === assessmentData.id)
        if (index !== -1) {
          state.value.assessments[index] = response.data
        }

        // Clear pending changes for this assessment
        state.value.pendingWeightChanges.delete(assessmentData.id)

        updateLastModified()
        await validateWeights()
        return response.data
      } else {
        setError(response.message || 'Failed to update assessment')
        return null
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update assessment')
      return null
    } finally {
      state.value.loading = false
    }
  }

  const deleteAssessment = async (assessmentId: number): Promise<boolean> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.assessments.delete(assessmentId)
      if (response.success) {
        state.value.assessments = state.value.assessments.filter((a) => a.id !== assessmentId)
        updateLastModified()
        await validateWeights()
        return true
      } else {
        setError(response.message || 'Failed to delete assessment')
        return false
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete assessment')
      return false
    } finally {
      state.value.loading = false
    }
  }

  const duplicateAssessment = async (assessmentId: number, newName?: string): Promise<Assessment | null> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.assessments.duplicate(assessmentId, newName)
      if (response.success && response.data) {
        // Check if adding this assessment would exceed weight limit
        const newTotalWeight = totalWeight.value + response.data.weight
        if (newTotalWeight > 100) {
          const warning: ValidationWarning = {
            field: 'weight',
            message: `Duplicated assessment causes total weight to exceed 100% (${newTotalWeight}%). Please adjust weights.`,
            code: 'WEIGHT_EXCEEDS_LIMIT_AFTER_DUPLICATE',
            severity: 'high',
          }
          state.value.validationWarnings = [warning]
        }

        state.value.assessments.push(response.data)
        updateLastModified()
        await validateWeights()
        return response.data
      } else {
        setError(response.message || 'Failed to duplicate assessment')
        return null
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to duplicate assessment')
      return null
    } finally {
      state.value.loading = false
    }
  }

  // Assessment detail operations
  const addAssessmentDetail = async (
    assessmentId: number,
    detailData: Omit<AssessmentDetail, 'id' | 'assessment_id' | 'created_at' | 'updated_at'>,
  ): Promise<AssessmentDetail | null> => {
    try {
      const response = await api.assessmentDetails.create(assessmentId, detailData)
      if (response.success && response.data) {
        // Update the assessment in local state
        const assessmentIndex = state.value.assessments.findIndex((a) => a.id === assessmentId)
        if (assessmentIndex !== -1) {
          state.value.assessments[assessmentIndex].details.push(response.data)
          updateLastModified()
        }
        return response.data
      } else {
        setError(response.message || 'Failed to add assessment detail')
        return null
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add assessment detail')
      return null
    }
  }

  const updateAssessmentDetail = async (
    detailId: number,
    detailData: Partial<AssessmentDetail>,
  ): Promise<AssessmentDetail | null> => {
    try {
      const response = await api.assessmentDetails.update(detailId, detailData)
      if (response.success && response.data) {
        // Update the detail in local state
        for (const assessment of state.value.assessments) {
          const detailIndex = assessment.details.findIndex((d) => d.id === detailId)
          if (detailIndex !== -1) {
            assessment.details[detailIndex] = response.data
            updateLastModified()
            break
          }
        }
        return response.data
      } else {
        setError(response.message || 'Failed to update assessment detail')
        return null
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update assessment detail')
      return null
    }
  }

  const deleteAssessmentDetail = async (detailId: number): Promise<boolean> => {
    try {
      const response = await api.assessmentDetails.delete(detailId)
      if (response.success) {
        // Remove the detail from local state
        for (const assessment of state.value.assessments) {
          const detailIndex = assessment.details.findIndex((d) => d.id === detailId)
          if (detailIndex !== -1) {
            assessment.details.splice(detailIndex, 1)
            updateLastModified()
            break
          }
        }
        return true
      } else {
        setError(response.message || 'Failed to delete assessment detail')
        return false
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete assessment detail')
      return false
    }
  }

  const reorderAssessmentDetails = async (assessmentId: number, detailIds: number[]): Promise<boolean> => {
    try {
      const response = await api.assessmentDetails.reorder(assessmentId, detailIds)
      if (response.success && response.data) {
        // Update the order in local state
        const assessmentIndex = state.value.assessments.findIndex((a) => a.id === assessmentId)
        if (assessmentIndex !== -1) {
          state.value.assessments[assessmentIndex].details = response.data
          updateLastModified()
        }
        return true
      } else {
        setError(response.message || 'Failed to reorder assessment details')
        return false
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to reorder assessment details')
      return false
    }
  }

  // Utility functions
  const refreshData = async (): Promise<void> => {
    await fetchAssessments()
  }

  const clearValidationErrors = (): void => {
    state.value.validationErrors = []
    state.value.validationWarnings = []
  }

  const getAssessmentById = (id: number): Assessment | undefined => {
    return state.value.assessments.find((assessment) => assessment.id === id)
  }

  const getAssessmentsByType = (type: string): Assessment[] => {
    return state.value.assessments.filter((assessment) => assessment.type === type)
  }

  // Cleanup function
  const cleanup = (): void => {
    state.value.assessments = []
    state.value.loading = false
    state.value.error = null
    state.value.validationErrors = []
    state.value.validationWarnings = []
    state.value.weightValidation = null
    state.value.lastUpdated = null
    state.value.pendingWeightChanges.clear()
  }

  // Real-time validation watchers
  watch(
    () => state.value.assessments,
    () => {
      if (state.value.realTimeValidation) {
        // Clear old validation errors when assessments change
        state.value.validationErrors = state.value.validationErrors.filter(
          (error) => error.code !== 'WEIGHT_VALIDATION_FAILED' && error.code !== 'WEIGHT_EXCEEDS_LIMIT',
        )

        // Validate weights automatically
        validateWeights()
      }
    },
    { deep: true },
  )

  // Watch for pending weight changes and provide immediate feedback
  watch(
    () => state.value.pendingWeightChanges,
    (pendingChanges) => {
      if (state.value.realTimeValidation && pendingChanges.size > 0) {
        // Clear previous validation errors for pending changes
        state.value.validationErrors = state.value.validationErrors.filter(
          (error) => error.code !== 'PENDING_WEIGHT_VALIDATION',
        )

        // Validate each pending change
        for (const [assessmentId, newWeight] of Array.from(pendingChanges)) {
          const validationResult = validateWeightChange(assessmentId, newWeight)
          if (!validationResult.isValid) {
            const error: ValidationError = {
              field: 'weight',
              message: `Pending change: ${validationResult.message}`,
              code: 'PENDING_WEIGHT_VALIDATION',
              value: newWeight,
            }
            state.value.validationErrors.push(error)
          }
        }
      }
    },
    { deep: true },
  )

  // Lifecycle management
  onMounted(() => {
    fetchAssessments()
  })

  onUnmounted(() => {
    cleanup()
  })

  // Return readonly state and actions
  return {
    // State (readonly)
    assessments: readonly(computed(() => state.value.assessments)),
    loading: readonly(computed(() => state.value.loading)),
    error: readonly(computed(() => state.value.error)),
    validationErrors: readonly(computed(() => state.value.validationErrors)),
    validationWarnings: readonly(computed(() => state.value.validationWarnings)),
    weightValidation: readonly(computed(() => state.value.weightValidation)),
    lastUpdated: readonly(computed(() => state.value.lastUpdated)),
    realTimeValidation: readonly(computed(() => state.value.realTimeValidation)),
    pendingWeightChanges: readonly(computed(() => state.value.pendingWeightChanges)),

    // Enhanced computed properties with real-time validation
    totalWeight,
    isWeightValid,
    weightExceeded,
    weightRemaining,
    weightPercentage,
    validationStatus,
    statusColor,
    statusIcon,
    hasValidationErrors,
    hasValidationWarnings,
    hasComponentErrors,
    componentValidations,
    allValidationMessages,
    weightValidationSummary,
    assessmentsByType,
    requiredAssessments,
    optionalAssessments,

    // Actions
    fetchAssessments,
    createAssessment,
    updateAssessment,
    deleteAssessment,
    duplicateAssessment,
    addAssessmentDetail,
    updateAssessmentDetail,
    deleteAssessmentDetail,
    reorderAssessmentDetails,
    validateWeights,
    refreshData,
    clearValidationErrors,
    getAssessmentById,
    getAssessmentsByType,
    cleanup,

    // Real-time validation actions
    validateWeightChangeRealTime,
    validateComponentWeightsRealTime,
    getWeightChangePreview,
    clearPendingWeightChanges,
    applyPendingWeightChanges,
    toggleRealTimeValidation,
    getWeightSuggestions,
  }
}
