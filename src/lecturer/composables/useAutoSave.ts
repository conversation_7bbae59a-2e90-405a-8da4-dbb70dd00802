import { ref, computed, onUnmounted } from 'vue'
import { useDebounceFn, useLocalStorage } from '@vueuse/core'

export interface AutoSaveOptions<T> {
  debounceMs?: number
  maxRetries?: number
  retryDelayMs?: number
  enableOfflineSupport?: boolean
  localStorageKey?: string
  onSaveSuccess?: (data: T) => void
  onSaveError?: (error: Error, data: T) => void
  onOfflineQueue?: (data: T) => void
  onOnlineRestore?: (data: T[]) => void
}

export interface AutoSaveState {
  isSaving: boolean
  lastSaved: Date | null
  saveCount: number
  errorCount: number
  retryCount: number
  isOnline: boolean
  queueSize: number
}

export interface SaveQueueItem<T> {
  id: string
  data: T
  timestamp: Date
  retryCount: number
  priority: 'low' | 'normal' | 'high'
}

/**
 * Generic auto-save composable with debouncing, retry logic, and offline support
 * Can be used for any type of data that needs to be auto-saved
 */
export function useAutoSave<T>(saveFunction: (data: T) => Promise<void>, options: AutoSaveOptions<T> = {}) {
  const {
    debounceMs = 2000,
    maxRetries = 3,
    retryDelayMs = 1000,
    enableOfflineSupport = true,
    localStorageKey = 'autoSave_queue',
    onSaveSuccess,
    onSaveError,
    onOfflineQueue,
    onOnlineRestore,
  } = options

  // State
  const state = ref<AutoSaveState>({
    isSaving: false,
    lastSaved: null,
    saveCount: 0,
    errorCount: 0,
    retryCount: 0,
    isOnline: navigator.onLine,
    queueSize: 0,
  })

  // Save queue for offline support and retry logic
  const saveQueue = ref<SaveQueueItem<T>[]>([])

  // Offline storage (only if enabled)
  const offlineStorage = enableOfflineSupport
    ? useLocalStorage<SaveQueueItem<T>[]>(localStorageKey, [])
    : ref<SaveQueueItem<T>[]>([])

  // Computed properties
  const isSaving = computed(() => state.value.isSaving)
  const lastSaved = computed(() => state.value.lastSaved)
  const hasErrors = computed(() => state.value.errorCount > 0)
  const isOnline = computed(() => state.value.isOnline)
  const queueSize = computed(() => saveQueue.value.length)
  const hasQueuedItems = computed(() => saveQueue.value.length > 0)

  // Internal helper functions
  const generateId = (): string => {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  const addToQueue = (data: T, priority: SaveQueueItem<T>['priority'] = 'normal'): string => {
    const id = generateId()
    const item: SaveQueueItem<T> = {
      id,
      data,
      timestamp: new Date(),
      retryCount: 0,
      priority,
    }

    // Insert based on priority
    if (priority === 'high') {
      saveQueue.value.unshift(item)
    } else {
      saveQueue.value.push(item)
    }

    state.value.queueSize = saveQueue.value.length

    // Save to offline storage if enabled
    if (enableOfflineSupport) {
      offlineStorage.value = [...saveQueue.value] as any
    }

    return id
  }

  const removeFromQueue = (id: string): void => {
    saveQueue.value = saveQueue.value.filter((item) => item.id !== id)
    state.value.queueSize = saveQueue.value.length

    if (enableOfflineSupport) {
      offlineStorage.value = [...saveQueue.value] as any
    }
  }

  const getNextQueueItem = (): SaveQueueItem<T> | null => {
    // Sort by priority and timestamp
    const sorted = [...saveQueue.value].sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 }
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
      if (priorityDiff !== 0) return priorityDiff
      return a.timestamp.getTime() - b.timestamp.getTime()
    })

    return sorted[0] || null
  }

  // Core save functionality
  const executeSave = async (data: T): Promise<boolean> => {
    try {
      await saveFunction(data)
      state.value.lastSaved = new Date()
      state.value.saveCount++
      state.value.retryCount = 0

      onSaveSuccess?.(data)
      return true
    } catch (error) {
      state.value.errorCount++
      const saveError = error instanceof Error ? error : new Error('Save failed')

      onSaveError?.(saveError, data)
      throw saveError
    }
  }

  const processQueue = async (): Promise<void> => {
    if (state.value.isSaving || !state.value.isOnline) return

    const item = getNextQueueItem()
    if (!item) return

    state.value.isSaving = true

    try {
      await executeSave(item.data)
      removeFromQueue(item.id)

      // Process next item if queue is not empty
      if (saveQueue.value.length > 0) {
        setTimeout(processQueue, 100) // Small delay between queue items
      }
    } catch (error) {
      item.retryCount++

      if (item.retryCount >= maxRetries) {
        // Remove from queue after max retries
        removeFromQueue(item.id)
        console.error(`Auto-save failed after ${maxRetries} retries:`, error)
      } else {
        // Schedule retry with exponential backoff
        const delay = retryDelayMs * Math.pow(2, item.retryCount - 1)
        setTimeout(() => {
          state.value.retryCount = item.retryCount
          processQueue()
        }, delay)
      }
    } finally {
      state.value.isSaving = false
    }
  }

  // Debounced save function
  const debouncedSave = useDebounceFn(async (data: T, priority?: SaveQueueItem<T>['priority']) => {
    if (!state.value.isOnline && enableOfflineSupport) {
      // Add to offline queue
      addToQueue(data, priority)
      onOfflineQueue?.(data)
      return
    }

    if (state.value.isOnline) {
      // Add to queue and process
      addToQueue(data, priority)
      await processQueue()
    }
  }, debounceMs)

  const cancelDebouncedSave = () => {
    // VueUse useDebounceFn doesn't have cancel method, so we'll implement our own
    // For now, we'll just clear the queue as a workaround
    saveQueue.value = []
  }

  // Public API functions
  const save = (data: T, priority?: SaveQueueItem<T>['priority']): void => {
    debouncedSave(data, priority)
  }

  const saveImmediately = async (data: T): Promise<boolean> => {
    // Cancel any pending debounced saves
    cancelDebouncedSave()

    if (!state.value.isOnline) {
      addToQueue(data, 'high')
      return false
    }

    state.value.isSaving = true
    try {
      await executeSave(data)
      return true
    } catch (error) {
      // Add to queue for retry
      addToQueue(data, 'high')
      return false
    } finally {
      state.value.isSaving = false
    }
  }

  const clearQueue = (): void => {
    saveQueue.value = []
    state.value.queueSize = 0

    if (enableOfflineSupport) {
      offlineStorage.value = []
    }
  }

  const pauseAutoSave = (): void => {
    cancelDebouncedSave()
  }

  const resumeAutoSave = (): void => {
    if (saveQueue.value.length > 0) {
      processQueue()
    }
  }

  const getQueueStatus = () => {
    return {
      total: saveQueue.value.length,
      byPriority: {
        high: saveQueue.value.filter((item) => item.priority === 'high').length,
        normal: saveQueue.value.filter((item) => item.priority === 'normal').length,
        low: saveQueue.value.filter((item) => item.priority === 'low').length,
      },
      oldestItem:
        saveQueue.value.length > 0 ? Math.min(...saveQueue.value.map((item) => item.timestamp.getTime())) : null,
    }
  }

  // Network status handling
  const handleOnline = (): void => {
    console.log('Network connection restored')
    state.value.isOnline = true

    // Restore items from offline storage
    if (enableOfflineSupport && offlineStorage.value.length > 0) {
      const offlineItems = [...offlineStorage.value]
      saveQueue.value = offlineItems
      state.value.queueSize = saveQueue.value.length

      onOnlineRestore?.(offlineItems.map((item) => item.data) as T[])

      // Start processing queue
      processQueue()
    }
  }

  const handleOffline = (): void => {
    console.log('Network connection lost')
    state.value.isOnline = false

    // Cancel any pending saves
    cancelDebouncedSave()
  }

  // Conflict resolution helpers
  const detectConflict = (localData: T, serverData: T): boolean => {
    // This is a generic implementation - should be customized based on data type
    const localStr = JSON.stringify(localData)
    const serverStr = JSON.stringify(serverData)
    return localStr !== serverStr
  }

  const resolveConflict = async (
    localData: T,
    serverData: T,
    resolver: (local: T, server: T) => T | Promise<T>,
  ): Promise<T> => {
    return await resolver(localData, serverData)
  }

  // Statistics and monitoring
  const getStatistics = () => {
    return {
      totalSaves: state.value.saveCount,
      totalErrors: state.value.errorCount,
      successRate:
        state.value.saveCount > 0
          ? ((state.value.saveCount - state.value.errorCount) / state.value.saveCount) * 100
          : 0,
      lastSaved: state.value.lastSaved,
      currentRetryCount: state.value.retryCount,
      queueSize: state.value.queueSize,
      isOnline: state.value.isOnline,
    }
  }

  const resetStatistics = (): void => {
    state.value.saveCount = 0
    state.value.errorCount = 0
    state.value.retryCount = 0
  }

  // Setup network listeners
  if (typeof window !== 'undefined') {
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
  }

  // Cleanup function
  const cleanup = (): void => {
    cancelDebouncedSave()
    clearQueue()
    resetStatistics()

    if (typeof window !== 'undefined') {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }

  // Auto-cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  // Initialize from offline storage if available
  if (enableOfflineSupport && offlineStorage.value.length > 0) {
    saveQueue.value = [...offlineStorage.value] as SaveQueueItem<T>[]
    state.value.queueSize = saveQueue.value.length
  }

  return {
    // State (readonly)
    isSaving,
    lastSaved,
    hasErrors,
    isOnline,
    queueSize,
    hasQueuedItems,

    // Actions
    save,
    saveImmediately,
    clearQueue,
    pauseAutoSave,
    resumeAutoSave,

    // Utilities
    getQueueStatus,
    getStatistics,
    resetStatistics,
    detectConflict,
    resolveConflict,
    cleanup,
  }
}
