import { computed, ref, watch, type Ref } from 'vue'
import type { Assessment, WeightValidation, ComponentWeightValidation } from '@/lecturer/types/models/assessment'

export interface WeightValidationOptions {
  maxTotalWeight?: number
  allowExceedance?: boolean
  warningThreshold?: number
}

export interface ValidationMessage {
  type: 'error' | 'warning' | 'info' | 'success'
  message: string
  code: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export const useWeightValidation = (assessments: Ref<Assessment[]>, options: WeightValidationOptions = {}) => {
  const { maxTotalWeight = 100, allowExceedance = false, warningThreshold = 95 } = options

  // Reactive validation state
  const validationMessages = ref<ValidationMessage[]>([])
  const isValidating = ref(false)

  // Computed validation results
  const totalWeight = computed(() => {
    if (!Array.isArray(assessments.value)) return 0
    return assessments.value.reduce((sum, assessment) => {
      const weight = typeof assessment.weight === 'string' ? parseFloat(assessment.weight) : assessment.weight
      return sum + (isNaN(weight) ? 0 : weight)
    }, 0)
  })

  const isWeightValid = computed(() => {
    if (allowExceedance) return true
    return totalWeight.value <= maxTotalWeight
  })

  const weightExceeded = computed(() => Math.max(0, totalWeight.value - maxTotalWeight))

  const weightRemaining = computed(() => Math.max(0, maxTotalWeight - totalWeight.value))

  const weightPercentage = computed(() => (totalWeight.value / maxTotalWeight) * 100)

  const validationStatus = computed(() => {
    if (totalWeight.value === 0) return 'empty'
    if (totalWeight.value > maxTotalWeight) return 'exceeded'
    if (totalWeight.value === maxTotalWeight) return 'complete'
    if (totalWeight.value >= warningThreshold) return 'warning'
    return 'incomplete'
  })

  const statusColor = computed(() => {
    switch (validationStatus.value) {
      case 'exceeded':
        return 'text-red-600'
      case 'complete':
        return 'text-green-600'
      case 'warning':
        return 'text-yellow-600'
      case 'incomplete':
        return 'text-blue-600'
      case 'empty':
        return 'text-gray-400'
      default:
        return 'text-gray-600'
    }
  })

  const statusIcon = computed(() => {
    switch (validationStatus.value) {
      case 'exceeded':
        return 'AlertTriangle'
      case 'complete':
        return 'CheckCircle'
      case 'warning':
        return 'Clock'
      case 'incomplete':
        return 'Info'
      case 'empty':
        return 'Circle'
      default:
        return 'Circle'
    }
  })

  const progressBarColor = computed(() => {
    switch (validationStatus.value) {
      case 'exceeded':
        return 'bg-red-500'
      case 'complete':
        return 'bg-green-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'incomplete':
        return 'bg-blue-500'
      case 'empty':
        return 'bg-gray-300'
      default:
        return 'bg-gray-300'
    }
  })

  // Component-level validation
  const componentValidations = computed((): ComponentWeightValidation[] => {
    if (!Array.isArray(assessments.value)) return []
    
    return assessments.value.map((assessment) => {
      const assessmentWeight = typeof assessment.weight === 'string' ? parseFloat(assessment.weight) : assessment.weight
      const detailWeightsSum = assessment.details?.reduce((sum, detail) => {
        const detailWeight = typeof detail.weight === 'string' ? parseFloat(detail.weight) : detail.weight
        return sum + (isNaN(detailWeight) ? 0 : detailWeight)
      }, 0) || 0
      
      const hasDetails = assessment.details?.length > 0
      const errors: string[] = []

      if (hasDetails && detailWeightsSum !== 100) {
        if (detailWeightsSum > 100) {
          errors.push(`Component weights exceed 100% (${detailWeightsSum}%)`)
        } else if (detailWeightsSum < 100) {
          errors.push(`Component weights total ${detailWeightsSum}%, should be 100%`)
        }
      }

      if (isNaN(assessmentWeight) || assessmentWeight <= 0) {
        errors.push('Assessment weight must be greater than 0')
      }

      if (!isNaN(assessmentWeight) && assessmentWeight > maxTotalWeight) {
        errors.push(`Assessment weight (${assessmentWeight}%) exceeds maximum (${maxTotalWeight}%)`)
      }

      return {
        assessment_id: assessment.id,
        assessment_name: assessment.name,
        current_weight: assessmentWeight,
        detail_weights_sum: detailWeightsSum,
        is_valid: errors.length === 0,
        errors,
      }
    })
  })

  const hasComponentErrors = computed(() => componentValidations.value.some((validation) => !validation.is_valid))

  // Overall validation result
  const weightValidation = computed(
    (): WeightValidation => ({
      total_weight: totalWeight.value,
      is_valid: isWeightValid.value && !hasComponentErrors.value,
      exceeds_limit: totalWeight.value > maxTotalWeight,
      missing_weight: weightRemaining.value,
      component_validations: componentValidations.value,
    }),
  )

  // Validation messages generation
  const generateValidationMessages = () => {
    const messages: ValidationMessage[] = []

    // Total weight validation
    if (totalWeight.value > maxTotalWeight) {
      messages.push({
        type: 'error',
        message: `Total assessment weight (${totalWeight.value}%) exceeds the maximum allowed (${maxTotalWeight}%)`,
        code: 'WEIGHT_EXCEEDED',
        severity: 'high',
      })
    } else if (totalWeight.value === 0) {
      messages.push({
        type: 'info',
        message: 'No assessments have been added yet',
        code: 'NO_ASSESSMENTS',
        severity: 'low',
      })
    } else if (totalWeight.value < maxTotalWeight) {
      const remaining = maxTotalWeight - totalWeight.value
      if (remaining <= 5) {
        messages.push({
          type: 'warning',
          message: `Only ${remaining}% weight remaining. Consider reviewing assessment distribution.`,
          code: 'LOW_REMAINING_WEIGHT',
          severity: 'medium',
        })
      } else {
        messages.push({
          type: 'info',
          message: `${remaining}% weight remaining to reach ${maxTotalWeight}%`,
          code: 'WEIGHT_REMAINING',
          severity: 'low',
        })
      }
    } else {
      messages.push({
        type: 'success',
        message: 'Assessment weight allocation is complete',
        code: 'WEIGHT_COMPLETE',
        severity: 'low',
      })
    }

    // Component-level validation messages
    componentValidations.value.forEach((validation) => {
      if (!validation.is_valid) {
        validation.errors.forEach((error, errorIndex) => {
          messages.push({
            type: 'error',
            message: `${validation.assessment_name}: ${error}`,
            code: `COMPONENT_WEIGHT_ERROR_${validation.assessment_id}_${errorIndex}`,
            severity: 'medium',
          })
        })
      }
    })

    return messages
  }

  // Validation for a specific assessment weight change
  const validateWeightChange = (assessmentId: number, newWeight: number) => {
    const currentAssessment = assessments.value.find((a) => a.id === assessmentId)
    if (!currentAssessment) return { isValid: false, message: 'Assessment not found' }

    const otherAssessmentsWeight = assessments.value
      .filter((a) => a.id !== assessmentId)
      .reduce((sum, a) => {
        const weight = typeof a.weight === 'string' ? parseFloat(a.weight) : a.weight
        return sum + (isNaN(weight) ? 0 : weight)
      }, 0)

    const projectedTotal = otherAssessmentsWeight + newWeight

    if (newWeight <= 0) {
      return {
        isValid: false,
        message: 'Weight must be greater than 0',
        severity: 'high' as const,
      }
    }

    if (newWeight > maxTotalWeight) {
      return {
        isValid: false,
        message: `Weight cannot exceed ${maxTotalWeight}%`,
        severity: 'high' as const,
      }
    }

    if (projectedTotal > maxTotalWeight) {
      const excess = projectedTotal - maxTotalWeight
      return {
        isValid: false,
        message: `This would exceed total weight by ${excess.toFixed(1)}%`,
        severity: 'high' as const,
      }
    }

    if (projectedTotal === maxTotalWeight) {
      return {
        isValid: true,
        message: 'This will complete the weight allocation',
        severity: 'low' as const,
      }
    }

    const remaining = maxTotalWeight - projectedTotal
    return {
      isValid: true,
      message: `${remaining.toFixed(1)}% will remain after this change`,
      severity: 'low' as const,
    }
  }

  // Validation for component weight changes
  const validateComponentWeights = (assessmentId: number, componentWeights: number[]) => {
    const total = componentWeights.reduce((sum, weight) => sum + weight, 0)

    if (componentWeights.length === 0) {
      return { isValid: true, message: 'No components defined' }
    }

    if (total !== 100) {
      return {
        isValid: false,
        message: `Component weights must total 100% (currently ${total}%)`,
        severity: 'medium' as const,
      }
    }

    if (componentWeights.some((weight) => weight <= 0)) {
      return {
        isValid: false,
        message: 'All component weights must be greater than 0',
        severity: 'medium' as const,
      }
    }

    return {
      isValid: true,
      message: 'Component weights are valid',
      severity: 'low' as const,
    }
  }

  // Suggestions for weight distribution
  const getWeightSuggestions = () => {
    const suggestions: string[] = []
    const assessmentCount = assessments.value.length

    if (assessmentCount === 0) {
      return ['Add your first assessment to begin weight allocation']
    }

    if (totalWeight.value === 0) {
      suggestions.push('Start by assigning weights to your assessments')
    } else if (totalWeight.value < maxTotalWeight) {
      const remaining = maxTotalWeight - totalWeight.value
      if (assessmentCount === 1) {
        suggestions.push(`Consider adding more assessments or increase the current weight by ${remaining}%`)
      } else {
        const avgIncrease = remaining / assessmentCount
        suggestions.push(
          `Distribute the remaining ${remaining}% across assessments (avg. ${avgIncrease.toFixed(1)}% each)`,
        )
      }
    } else if (totalWeight.value > maxTotalWeight) {
      const excess = totalWeight.value - maxTotalWeight
      const avgDecrease = excess / assessmentCount
      suggestions.push(`Reduce total weight by ${excess}% (avg. ${avgDecrease.toFixed(1)}% per assessment)`)
    }

    // Check for uneven distribution
    if (assessmentCount > 1) {
      const weights = assessments.value.map((a) => {
        const weight = typeof a.weight === 'string' ? parseFloat(a.weight) : a.weight
        return isNaN(weight) ? 0 : weight
      }).filter((w) => w > 0)
      const avgWeight = weights.reduce((sum, w) => sum + w, 0) / weights.length
      const hasUnevenDistribution = weights.some((w) => Math.abs(w - avgWeight) > avgWeight * 0.5)

      if (hasUnevenDistribution) {
        suggestions.push('Consider balancing assessment weights for more even distribution')
      }
    }

    return suggestions
  }

  // Watch for changes and update validation messages
  watch(
    [assessments, totalWeight],
    () => {
      isValidating.value = true
      validationMessages.value = generateValidationMessages()
      isValidating.value = false
    },
    { deep: true, immediate: true },
  )

  return {
    // Computed properties
    totalWeight,
    isWeightValid,
    weightExceeded,
    weightRemaining,
    weightPercentage,
    validationStatus,
    statusColor,
    statusIcon,
    progressBarColor,
    componentValidations,
    hasComponentErrors,
    weightValidation,
    validationMessages,
    isValidating,

    // Methods
    validateWeightChange,
    validateComponentWeights,
    getWeightSuggestions,
    generateValidationMessages,
  }
}
