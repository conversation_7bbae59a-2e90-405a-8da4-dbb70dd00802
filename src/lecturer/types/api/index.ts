// Lecturer-specific API types
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface LecturerDashboardApiResponse {
  // Dashboard API response types
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface CourseManagementApiResponse {
  // Course management API response types
}

// Session Attendance API Response Types
export interface SessionAttendanceApiResponse {
  session: SessionInfo
  students: StudentAttendanceRecord[]
  summary: AttendanceSummary
  actions: SessionActions
  recommendations: SessionRecommendation[]
}

export interface SessionInfo {
  id: number
  title: string
  date: string
  start_time: string
  end_time: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed'
  session_type:
    | 'lecture'
    | 'tutorial'
    | 'practical'
    | 'laboratory'
    | 'seminar'
    | 'workshop'
    | 'exam'
    | 'assessment'
    | 'review'
    | 'presentation'
  delivery_mode: 'in_person' | 'online' | 'hybrid' | 'blended'
  attendance_required: boolean
  attendance_marked: boolean | null
  expected_attendees: number | null
  actual_attendees: number | null
  course: {
    id: number
    unit_code: string | null
    unit_name: string | null
    section_code: string | null
    semester: string | null
  }
}

export interface StudentAttendanceRecord {
  student_id: number
  student_number: string
  full_name: string
  email: string
  phone?: string
  attendance: AttendanceDetails
  student_info: StudentInfo
}

export interface AttendanceDetails {
  id?: number
  status: 'present' | 'absent' | 'late' | 'excused' | 'not_marked'
  status_label?: string
  status_color?: string
  check_in_time?: string
  check_out_time?: string
  minutes_late: number
  minutes_present?: number
  participation_level?: 'excellent' | 'good' | 'average' | 'poor' | 'none'
  participation_score?: number
  notes?: string
  excuse_reason?: string
  recording_method: 'manual' | 'automatic' | 'qr_code' | 'biometric'
  is_verified: boolean
  recorded_by?: string
  recorded_at?: string
  can_edit?: boolean
  can_verify?: boolean
}

export interface StudentInfo {
  program?: string
  specialization?: string
  academic_status?: string
  status?: string
  campus?: string
  admission_date?: string
  expected_graduation_date?: string
}

export interface AttendanceSummary {
  total_enrolled: number
  attendance_counts: AttendanceCounts
  attendance_rate: number
  completion_rate: number
  needs_attention: boolean
  quick_stats: QuickStats
}

export interface QuickStats {
  present_percentage: number
  absent_percentage: number
  late_percentage: number
  excused_percentage: number
  not_marked_percentage: number
}

export interface AttendanceCounts {
  present: number
  absent: number
  late: number
  excused: number
  not_marked: number
}

// Session Actions
export interface SessionActions {
  can_mark_all_present: boolean
  can_mark_all_absent: boolean
  can_export: boolean
  can_send_notifications: boolean
  can_save_draft: boolean
  can_generate_report: boolean
}

// Session Recommendations
export interface SessionRecommendation {
  type: 'alert' | 'action' | 'info' | 'warning'
  priority: 'low' | 'medium' | 'high' | 'critical'
  message: string
  action: string
}

// Course Students API Response Types
export interface CourseStudentApiResponse {
  student_id: number
  student_number: string | null
  full_name: string
  email: string
  registration_date: string
  registration: {
    status: 'confirmed' | 'pending' | 'cancelled'
    attempt_number: number
    is_retake: boolean
  }
  academics: {
    final_score?: number | null
    final_grade?: string | null
    grade_status: 'provisional' | 'final' | 'pending'
    grade_status_label: string
  }
  attendance: {
    percentage: number
    sessions_attended: number
    total_sessions: number
    last_attendance?: string
    meets_requirement: boolean
    status: 'excellent' | 'good' | 'concerning' | 'poor'
    status_label: string
    status_color: string
  }
  academic_standing: {
    status: 'good' | 'probation' | 'at_risk' | 'warning'
    label: string
    color: string
  }
  risk_assessment: {
    level: 'low' | 'medium' | 'high' | 'critical'
    factors: string[]
    recommendations: string[]
  }
  actions: {
    can_contact: boolean
    can_add_note: boolean
    needs_attention: boolean
    can_view_details: boolean
  }
}
