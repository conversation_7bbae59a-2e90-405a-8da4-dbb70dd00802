<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import AssessmentLayout from '@/lecturer/components/assessment/AssessmentLayout.vue'
import GradeMatrix from '@/lecturer/components/assessment/GradeMatrix.vue'
import GradeScaleLegend from '@/lecturer/components/assessment/GradeScaleLegend.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { useAssessmentApi } from '@/lecturer/composables/useAssessmentApi'
import { Table, Download, Calculator, Info, TrendingUp, Users, FileSpreadsheet, Eye, Settings } from 'lucide-vue-next'

interface Props {
  courseId: string
}

const props = defineProps<Props>()

// State
const loading = ref(false)
const error = ref<string | null>(null)
const activeTab = ref('matrix')
const showLegend = ref(true)

// API
const api = useAssessmentApi()

// Grade scale configuration (this could come from API/config)
const gradeScale = [
  { letter: 'A', range: '90-100%', color: 'text-green-600', description: 'Excellent' },
  { letter: 'B', range: '80-89%', color: 'text-blue-600', description: 'Good' },
  { letter: 'C', range: '70-79%', color: 'text-yellow-600', description: 'Satisfactory' },
  { letter: 'D', range: '60-69%', color: 'text-orange-600', description: 'Pass' },
  { letter: 'F', range: '0-59%', color: 'text-red-600', description: 'Fail' },
]

// Methods
const exportGrades = async (format: 'excel' | 'pdf') => {
  try {
    loading.value = true
    const response = await api.export.exportGrades({
      course_offering_id: parseInt(props.courseId),
      format,
      include_all_assessments: true,
      include_weighted_totals: true,
      include_letter_grades: true,
    })

    if (response.success && response.data) {
      // Handle download
      const blob = new Blob([response.data.file_content], {
        type:
          format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'application/pdf',
      })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = response.data.filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Export failed'
  } finally {
    loading.value = false
  }
}

// Computed
const pageTitle = computed(() => `Grade Matrix - Course ${props.courseId}`)
</script>

<template>
  <AssessmentLayout :course-id="courseId">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-semibold flex items-center gap-2">
            <Table class="h-6 w-6" />
            Grade Matrix & Overview
          </h2>
          <p class="text-muted-foreground">Comprehensive view of all student grades across all assessments</p>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" size="sm" @click="showLegend = !showLegend">
            <Info class="h-4 w-4 mr-2" />
            {{ showLegend ? 'Hide' : 'Show' }} Legend
          </Button>
          <Button variant="outline" size="sm" @click="exportGrades('excel')" :disabled="loading">
            <FileSpreadsheet class="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button variant="outline" size="sm" @click="exportGrades('pdf')" :disabled="loading">
            <Download class="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      <!-- Grade Scale Legend -->
      <GradeScaleLegend v-if="showLegend" :show-scale-selector="true" :show-statistics="false" :compact="false" />

      <!-- Main Content Tabs -->
      <Tabs v-model="activeTab" class="space-y-4">
        <TabsList>
          <TabsTrigger value="matrix" class="flex items-center gap-2">
            <Table class="h-4 w-4" />
            Grade Matrix
          </TabsTrigger>
          <TabsTrigger value="summary" class="flex items-center gap-2">
            <TrendingUp class="h-4 w-4" />
            Class Summary
          </TabsTrigger>
          <TabsTrigger value="settings" class="flex items-center gap-2">
            <Settings class="h-4 w-4" />
            Display Settings
          </TabsTrigger>
        </TabsList>

        <!-- Grade Matrix Tab -->
        <TabsContent value="matrix" class="space-y-4">
          <GradeMatrix :course-id="courseId" />
        </TabsContent>

        <!-- Class Summary Tab -->
        <TabsContent value="summary" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Summary cards would go here -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <Users class="h-5 w-5" />
                  Class Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Total Students:</span>
                    <span class="font-medium">--</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Average Grade:</span>
                    <span class="font-medium">--%</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Completion Rate:</span>
                    <span class="font-medium">--%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Grade Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-2">
                  <div v-for="grade in gradeScale" :key="grade.letter" class="flex justify-between">
                    <span :class="['text-sm font-medium', grade.color]">{{ grade.letter }}</span>
                    <span class="text-sm text-muted-foreground">-- students</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Assessment Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Fully Graded:</span>
                    <span class="font-medium">-- assessments</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Partially Graded:</span>
                    <span class="font-medium">-- assessments</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Not Started:</span>
                    <span class="font-medium">-- assessments</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <!-- Display Settings Tab -->
        <TabsContent value="settings" class="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Display Preferences</CardTitle>
              <CardDescription>Customize how the grade matrix is displayed</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <Alert>
                <Info class="h-4 w-4" />
                <AlertDescription>
                  Display settings and customization options will be implemented in future updates. Currently, the
                  matrix automatically adapts to screen size and provides filtering options.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  </AssessmentLayout>
</template>

<style scoped>
/* Additional styles if needed */
</style>
