<script setup lang="ts">
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Input } from '@/shared/components/ui/input'
import { Progress } from '@/shared/components/ui/progress'
import { BarChart3, BookOpen, Calendar, MapPin, Search, Users } from 'lucide-vue-next'
import { storeToRefs } from 'pinia'
import { computed, onMounted, ref } from 'vue'
import { useLecturerCoursesStore } from '../stores'
import type { CourseOffering } from '../types/models/lecturer'

const coursesStore = useLecturerCoursesStore()
const searchQuery = ref('')
const selectedDeliveryMode = ref('all')

// Computed properties for easy access
const { courses, loading, error, activeCourses, totalEnrollment, averageEnrollmentRate } = storeToRefs(coursesStore)
// Filtered courses based on search and filters
const filteredCourses = computed(() => {
  let filtered = activeCourses.value
  // console.log('activeCourses', activeCourses)
  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (course: CourseOffering) =>
        course.curriculum_unit.code.toLowerCase().includes(query) ||
        course.curriculum_unit.name.toLowerCase().includes(query) ||
        course.section_code.toLowerCase().includes(query),
    )
  }

  // Delivery mode filter
  if (selectedDeliveryMode.value !== 'all') {
    filtered = filtered.filter((course: CourseOffering) => course.delivery_mode === selectedDeliveryMode.value)
  }

  return filtered
})

// Load courses data on component mount
onMounted(async () => {
  await coursesStore.fetchCourses()
})

// Helper functions
const getEnrollmentPercentage = (current: number, max: number) => {
  return Math.round((current / max) * 100)
}

const getEnrollmentColor = (percentage: number) => {
  if (percentage >= 90) return 'text-red-600'
  if (percentage >= 75) return 'text-yellow-600'
  return 'text-green-600'
}

const getDeliveryModeColor = (mode: string) => {
  switch (mode) {
    case 'in_person':
      return 'default'
    case 'online':
      return 'secondary'
    case 'hybrid':
      return 'outline'
    case 'blended':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatSchedule = (days: string[] | null, startTime: string | null, endTime: string | null) => {
  if (!days || !startTime || !endTime) {
    return 'Schedule TBD'
  }
  const formattedDays = days.join(', ')
  const start = new Date(`2000-01-01T${startTime}`).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  })
  const end = new Date(`2000-01-01T${endTime}`).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  })
  return `${formattedDays} ${start} - ${end}`
}

const selectCourse = (course: CourseOffering) => {
  coursesStore.selectCourse(course)
}

const refreshCourses = async () => {
  await coursesStore.fetchCourses()
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">My Courses</h1>
        <p class="text-muted-foreground">Manage your teaching assignments and course materials</p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="refreshCourses" :disabled="loading" variant="outline">
          <BarChart3 class="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <!-- <Button variant="default">
          <Plus class="mr-2 h-4 w-4" />
          Add Material
        </Button> -->
      </div>
    </div>

    <!-- Error State -->
    <Alert v-if="error" variant="destructive">
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Course Overview Stats -->
    <div class="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Active Courses</CardTitle>
          <BookOpen class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ activeCourses?.length || 0 }}</div>
          <p class="text-xs text-muted-foreground">This semester</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Total Enrollment</CardTitle>
          <Users class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ totalEnrollment || 0 }}</div>
          <p class="text-xs text-muted-foreground">Students across all courses</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Avg. Enrollment Rate</CardTitle>
          <BarChart3 class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ Math.round(averageEnrollmentRate || 0) }}%</div>
          <p class="text-xs text-muted-foreground">Of maximum capacity</p>
        </CardContent>
      </Card>
    </div>

    <!-- Search and Filters -->
    <div class="flex items-center gap-4">
      <div class="relative flex-1 max-w-sm">
        <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input v-model="searchQuery" placeholder="Search courses..." class="pl-8" />
      </div>
      <select v-model="selectedDeliveryMode" class="px-3 py-2 border border-input bg-background rounded-md text-sm">
        <option value="all">All Delivery Modes</option>
        <option value="in_person">In Person</option>
        <option value="online">Online</option>
        <option value="hybrid">Hybrid</option>
        <option value="blended">Blended</option>
      </select>
    </div>
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading courses...</p>
      </div>
    </div>
    <!-- Courses Grid -->
    <div v-if="!loading && filteredCourses?.length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <Card
        v-for="course in filteredCourses"
        :key="course.id"
        class="cursor-pointer hover:shadow-md transition-shadow"
        @click="selectCourse(course)"
      >
        <CardHeader>
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <CardTitle class="text-lg">{{ course.curriculum_unit.code }}</CardTitle>
              <CardDescription class="mt-1">
                {{ course.curriculum_unit.name }}
              </CardDescription>
            </div>
            <Badge :variant="getDeliveryModeColor(course.delivery_mode)">
              {{ course.delivery_mode.replace('_', ' ') }}
            </Badge>
          </div>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- Enrollment Progress -->
          <div>
            <div class="flex items-center justify-between text-sm mb-2">
              <span>Enrollment</span>
              <span
                :class="getEnrollmentColor(getEnrollmentPercentage(course.current_enrollment, course.max_capacity))"
              >
                {{ course.current_enrollment }}/{{ course.max_capacity }}
              </span>
            </div>
            <Progress :value="getEnrollmentPercentage(course.current_enrollment, course.max_capacity)" class="h-2" />
          </div>

          <!-- Schedule -->
          <div class="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar class="h-4 w-4" />
            <span>
              {{ formatSchedule(course.schedule_days, course.schedule_time_start, course.schedule_time_end) }}
            </span>
          </div>

          <!-- Location -->
          <div v-if="course.location" class="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPin class="h-4 w-4" />
            <span>{{ course.location }}</span>
          </div>

          <!-- Section -->
          <div class="flex items-center justify-between">
            <span class="text-sm text-muted-foreground">Section {{ course.section_code }}</span>
            <div class="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                @click.stop="$router.push(`/lecturer/teaching/courses/${course.id}/students`)"
              >
                <Users class="h-4 w-4 mr-1" />
                Students
              </Button>
              <Button
                size="sm"
                variant="outline"
                @click.stop="$router.push(`/lecturer/teaching/courses/${course.id}/assessments`)"
              >
                <BarChart3 class="h-4 w-4 mr-1" />
                Assessments
              </Button>
              <Button
                size="sm"
                variant="outline"
                @click.stop="$router.push(`/lecturer/teaching/courses/${course.id}/sessions`)"
              >
                <Calendar class="h-4 w-4 mr-1" />
                Sessions
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading && courses.length === 0" class="text-center py-12">
      <BookOpen class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
      <h3 class="text-lg font-semibold mb-2">No courses found</h3>
      <p class="text-muted-foreground mb-4">You don't have any active courses this semester.</p>
    </div>

    <!-- No Results State -->
    <div v-else-if="!loading && filteredCourses.length === 0" class="text-center py-12">
      <Search class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
      <h3 class="text-lg font-semibold mb-2">No courses match your search</h3>
      <p class="text-muted-foreground mb-4">Try adjusting your search terms or filters.</p>
      <Button
        variant="outline"
        @click="
          () => {
            searchQuery = ''
            selectedDeliveryMode = 'all'
          }
        "
      >
        Clear Filters
      </Button>
    </div>
  </div>
</template>
