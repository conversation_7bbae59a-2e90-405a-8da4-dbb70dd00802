<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { ColumnDef } from '@tanstack/vue-table'
import {
  FlexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  useVueTable,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/vue-table'
// Value updater utility
const valueUpdater = <T>(updaterOrValue: T | ((old: T) => T), ref: { value: T }) => {
  ref.value = typeof updaterOrValue === 'function'
    ? (updaterOrValue as (old: T) => T)(ref.value)
    : updaterOrValue
}
import { useLecturerApi } from '@/lecturer/composables/useLecturerApi'
import type { CourseStudentApiResponse } from '@/lecturer/types/api'

// UI Components
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table'
import { Input } from '@/shared/components/ui/input'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'

// Icons
import { MoreHorizontal, ArrowUpDown, Mail, MessageSquare, Eye, Flag, ArrowLeft } from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()
const lecturerApi = useLecturerApi()

// Reactive state
const students = ref<CourseStudentApiResponse[]>([])
const loading = ref(true)
const error = ref<string | null>(null)
const sorting = ref<SortingState>([])
const columnFilters = ref<ColumnFiltersState>([])

// Get course ID from route params
const courseId = computed(() => route.params.courseId as string)

// Registration status color mapping
const getRegistrationStatusColor = (status: string) => {
  const colors = {
    confirmed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

// Table columns definition
const columns: ColumnDef<CourseStudentApiResponse>[] = [
  {
    accessorKey: 'full_name',
    header: ({ column }) => {
      return h(Button, {
        variant: 'ghost',
        onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
      }, () => [
        'Student Name',
        h(ArrowUpDown, { class: 'ml-2 h-4 w-4' })
      ])
    },
    cell: ({ row }) => {
      const student = row.original
      return h('div', { class: 'flex flex-col' }, [
        h('div', { class: 'font-medium' }, student.full_name),
        h('div', { class: 'text-sm text-muted-foreground' }, student.student_number || 'N/A')
      ])
    },
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      return h('div', { class: 'text-sm' }, row.getValue('email'))
    },
  },
  {
    accessorKey: 'registration.status',
    header: 'Registration Status',
    cell: ({ row }) => {
      const student = row.original
      const status = student.registration.status
      return h(Badge, {
        class: getRegistrationStatusColor(status)
      }, () => status.charAt(0).toUpperCase() + status.slice(1))
    },
  },
  {
    accessorKey: 'academics.final_score',
    header: ({ column }) => {
      return h(Button, {
        variant: 'ghost',
        onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
      }, () => [
        'Final Score',
        h(ArrowUpDown, { class: 'ml-2 h-4 w-4' })
      ])
    },
    cell: ({ row }) => {
      const student = row.original
      const score = student.academics.final_score
      return h('div', { class: 'text-center' }, [
        score !== null && score !== undefined ? `${score}%` : 'N/A'
      ])
    },
  },
  {
    accessorKey: 'attendance.percentage',
    header: ({ column }) => {
      return h(Button, {
        variant: 'ghost',
        onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
      }, () => [
        'Attendance',
        h(ArrowUpDown, { class: 'ml-2 h-4 w-4' })
      ])
    },
    cell: ({ row }) => {
      const student = row.original
      const percentage = student.attendance.percentage
      const status = student.attendance.status

      return h('div', { class: 'flex flex-col items-center' }, [
        h('div', { class: 'font-medium' }, `${percentage}%`),
        h(Badge, {
          class: `bg-${student.attendance.status_color}-100 text-${student.attendance.status_color}-800 text-xs`
        }, () => student.attendance.status_label)
      ])
    },
  },
  {
    accessorKey: 'academic_standing.status',
    header: 'Academic Standing',
    cell: ({ row }) => {
      const student = row.original
      const standing = student.academic_standing

      return h('div', { class: 'text-center' }, [
        h(Badge, {
          class: `bg-${standing.color}-100 text-${standing.color}-800`
        }, () => standing.label)
      ])
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const student = row.original
      const actions = student.actions

      return h('div', { class: 'flex justify-center' }, [
        h(DropdownMenu, {}, {
          default: () => [
            h(DropdownMenuTrigger, { asChild: true }, {
              default: () => h(Button, {
                variant: 'ghost',
                class: 'h-8 w-8 p-0'
              }, {
                default: () => [
                  h('span', { class: 'sr-only' }, 'Open menu'),
                  h(MoreHorizontal, { class: 'h-4 w-4' })
                ]
              })
            }),
            h(DropdownMenuContent, { align: 'end' }, {
              default: () => [
                h(DropdownMenuLabel, {}, 'Actions'),
                ...(actions.can_contact ? [
                  h(DropdownMenuItem, {
                    onClick: () => contactStudent(student)
                  }, {
                    default: () => [
                      h(Mail, { class: 'mr-2 h-4 w-4' }),
                      'Contact Student'
                    ]
                  })
                ] : []),
                ...(actions.can_add_note ? [
                  h(DropdownMenuItem, {
                    onClick: () => addNote(student)
                  }, {
                    default: () => [
                      h(MessageSquare, { class: 'mr-2 h-4 w-4' }),
                      'Add Note'
                    ]
                  })
                ] : []),
                ...(actions.can_view_details ? [
                  h(DropdownMenuSeparator),
                  h(DropdownMenuItem, {
                    onClick: () => viewDetails(student)
                  }, {
                    default: () => [
                      h(Eye, { class: 'mr-2 h-4 w-4' }),
                      'View Details'
                    ]
                  })
                ] : []),
                ...(actions.needs_attention ? [
                  h(DropdownMenuItem, {
                    onClick: () => toggleAttentionFlag(student)
                  }, {
                    default: () => [
                      h(Flag, { class: 'mr-2 h-4 w-4' }),
                      'Mark as Needs Attention'
                    ]
                  })
                ] : [])
              ]
            })
          ]
        })
      ])
    },
  },
]

// Table configuration
const table = useVueTable({
  get data() { return students.value },
  get columns() { return columns },
  getCoreRowModel: getCoreRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  onSortingChange: updaterOrValue => valueUpdater(updaterOrValue, sorting),
  onColumnFiltersChange: updaterOrValue => valueUpdater(updaterOrValue, columnFilters),
  state: {
    get sorting() { return sorting.value },
    get columnFilters() { return columnFilters.value },
  },
})

// Action handlers
const contactStudent = (student: CourseStudentApiResponse) => {
  // TODO: Implement contact functionality
  console.log('Contact student:', student.full_name)
}

const addNote = (student: CourseStudentApiResponse) => {
  // TODO: Implement add note functionality
  console.log('Add note for student:', student.full_name)
}

const viewDetails = (student: CourseStudentApiResponse) => {
  // TODO: Implement view details functionality
  console.log('View details for student:', student.full_name)
}

const toggleAttentionFlag = (student: CourseStudentApiResponse) => {
  // TODO: Implement attention flag toggle
  console.log('Toggle attention flag for student:', student.full_name)
}

// Fetch data
const fetchStudents = async () => {
  try {
    loading.value = true
    error.value = null
    const response = await lecturerApi.students.getByCourse(courseId.value)
    students.value = Array.isArray(response.data) ? response.data : (response as any) || []
  } catch (err) {
    error.value = 'Failed to load students'
    console.error('Error fetching students:', err)
  } finally {
    loading.value = false
  }
}

// Summary statistics
const summaryStats = computed(() => {
  const totalStudents = students.value.length
  const confirmedStudents = students.value.filter(s => s.registration.status === 'confirmed').length
  const averageAttendance = students.value.length > 0
    ? Math.round(students.value.reduce((acc, s) => acc + s.attendance.percentage, 0) / students.value.length)
    : 0
  const atRiskStudents = students.value.filter(s =>
    s.academic_standing.status === 'at_risk' || s.academic_standing.status === 'warning' || s.risk_assessment.level === 'high' || s.risk_assessment.level === 'critical'
  ).length

  return {
    totalStudents,
    confirmedStudents,
    averageAttendance,
    atRiskStudents
  }
})

// Lifecycle
onMounted(() => {
  fetchStudents()
})

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="flex flex-col space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4 mb-2">
        <Button variant="ghost" size="sm" @click="goBack">
          <ArrowLeft class="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 class="text-2xl font-bold tracking-tight">Course Students</h1>
        <p class="text-muted-foreground">
          Manage and monitor students enrolled in this course
        </p>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Total Students</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ summaryStats.totalStudents }}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Confirmed Registrations</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ summaryStats.confirmedStudents }}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Average Attendance</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ summaryStats.averageAttendance }}%</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">At Risk Students</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ summaryStats.atRiskStudents }}</div>
        </CardContent>
      </Card>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-8">
      <LoadingSpinner size="lg" />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex justify-center py-8">
      <div class="text-center">
        <p class="text-red-600 mb-4">{{ error }}</p>
        <Button @click="fetchStudents" variant="outline">
          Retry
        </Button>
      </div>
    </div>

    <!-- Data Table -->
    <div v-else class="space-y-4">
      <!-- Search and Filters -->
      <div class="flex items-center space-x-2">
        <Input class="max-w-sm" placeholder="Filter by name"
          :model-value="table.getColumn('full_name')?.getFilterValue() as string"
          @update:model-value="table.getColumn('full_name')?.setFilterValue($event)" />
      </div>

      <!-- Table -->
      <div class="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id">
              <TableHead v-for="header in headerGroup.headers" :key="header.id">
                <FlexRender v-if="!header.isPlaceholder" :render="header.column.columnDef.header"
                  :props="header.getContext()" />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <template v-if="table.getRowModel().rows?.length">
              <TableRow v-for="row in table.getRowModel().rows" :key="row.id"
                :data-state="row.getIsSelected() ? 'selected' : undefined">
                <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id">
                  <FlexRender :render="cell.column.columnDef.cell" :props="cell.getContext()" />
                </TableCell>
              </TableRow>
            </template>
            <template v-else>
              <TableRow>
                <TableCell :colspan="columns.length" class="h-24 text-center">
                  No students found.
                </TableCell>
              </TableRow>
            </template>
          </TableBody>
        </Table>
      </div>

      <!-- Pagination -->
      <div class="flex items-center justify-between space-x-2 py-4">
        <div class="text-sm text-muted-foreground">
          Showing {{ table.getRowModel().rows.length }} of {{ students.length }} students
        </div>
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" :disabled="!table.getCanPreviousPage()" @click="table.previousPage()">
            Previous
          </Button>
          <Button variant="outline" size="sm" :disabled="!table.getCanNextPage()" @click="table.nextPage()">
            Next
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
