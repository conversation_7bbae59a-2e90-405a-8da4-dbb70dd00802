<script setup lang="ts">
import AssessmentLayout from '@/lecturer/components/assessment/AssessmentLayout.vue'
import WeightValidationIndicator from '@/lecturer/components/assessment/WeightValidationIndicator.vue'
import { useAssessmentManagement } from '@/lecturer/composables/useAssessmentManagement'
import { useSharedAssessmentStore } from '@/lecturer/stores/sharedAssessment'
import { AssessmentType } from '@/lecturer/types/models/assessment'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Badge } from '@/shared/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import { AlertTriangle, CheckCircle, Clock, FileText } from 'lucide-vue-next'
import { computed, onMounted } from 'vue'

interface Props {
  courseId: string
}

const props = defineProps<Props>()

// Use the composable for local state management
const {
  assessments,
  loading,
  error,
  validationErrors,
  validationWarnings,
  hasValidationErrors,
  hasValidationWarnings,
  requiredAssessments,
  optionalAssessments,
} = useAssessmentManagement(parseInt(props.courseId))

// Use shared store for navigation state only
const sharedStore = useSharedAssessmentStore()

// Set current course and tab in shared store
onMounted(() => {
  sharedStore.setCurrentCourse(props.courseId)
  sharedStore.setLastVisitedTab('management')
})

// Computed properties for UI
const assessmentTypeLabels = computed(() => ({
  [AssessmentType.QUIZ]: 'Quiz',
  [AssessmentType.ASSIGNMENT]: 'Assignment',
  [AssessmentType.PROJECT]: 'Project',
  [AssessmentType.EXAM]: 'Exam',
  [AssessmentType.ONLINE_ACTIVITY]: 'Online Activity',
  [AssessmentType.PRESENTATION]: 'Presentation',
  [AssessmentType.PARTICIPATION]: 'Participation',
  [AssessmentType.OTHER]: 'Other',
}))

const assessmentTypeColors = computed(() => ({
  [AssessmentType.QUIZ]: 'bg-blue-100 text-blue-800',
  [AssessmentType.ASSIGNMENT]: 'bg-green-100 text-green-800',
  [AssessmentType.PROJECT]: 'bg-purple-100 text-purple-800',
  [AssessmentType.EXAM]: 'bg-red-100 text-red-800',
  [AssessmentType.ONLINE_ACTIVITY]: 'bg-yellow-100 text-yellow-800',
  [AssessmentType.PRESENTATION]: 'bg-indigo-100 text-indigo-800',
  [AssessmentType.PARTICIPATION]: 'bg-pink-100 text-pink-800',
  [AssessmentType.OTHER]: 'bg-gray-100 text-gray-800',
}))

// Grading progress calculation (mock data for now)
const gradingProgress = computed(() => {
  // This would typically come from the grading composable or API
  // For now, return mock data
  return {
    percentage: 0,
    graded: 0,
    total: 0,
  }
})

const formatDueDate = (dateString?: string) => {
  if (!dateString) return 'No due date'
  return new Date(dateString).toLocaleDateString('en-AU', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  })
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return 'bg-green-500'
  if (percentage >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}
</script>

<template>
  <AssessmentLayout :course-id="courseId">
    <div class="space-y-6">
      <!-- Header Section -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">Assessment Management</h1>
          <p class="text-muted-foreground">Manage assessment components and structure for this course</p>
        </div>
      </div>

      <!-- Weight Validation Indicator -->
      <WeightValidationIndicator
        :assessments="Array.isArray(assessments) ? assessments.map((a) => ({ ...a })) : []"
        :show-details="true"
        :show-suggestions="true"
      />

      <!-- Validation Errors -->
      <div v-if="hasValidationErrors" class="space-y-2">
        <Alert v-for="validationError in validationErrors" :key="validationError.code" variant="destructive">
          <AlertTriangle class="h-4 w-4" />
          <AlertDescription>
            {{ validationError.message }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Validation Warnings -->
      <div v-if="hasValidationWarnings" class="space-y-2">
        <Alert v-for="validationWarning in validationWarnings" :key="validationWarning.code" variant="default">
          <AlertTriangle class="h-4 w-4" />
          <AlertDescription>
            {{ validationWarning.message }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Error State -->
      <Alert v-if="error" variant="destructive">
        <AlertTriangle class="h-4 w-4" />
        <AlertDescription>
          {{ error }}
        </AlertDescription>
      </Alert>

      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" text="Loading assessments..." />
      </div>

      <!-- Assessment Content -->
      <div v-else-if="!error" class="space-y-6">
        <!-- Required Assessments Section -->
        <div v-if="requiredAssessments.length > 0">
          <h2 class="text-lg font-semibold mb-4 flex items-center gap-2">
            <CheckCircle class="h-5 w-5 text-red-600" />
            Required Assessments
          </h2>
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card v-for="assessment in requiredAssessments" :key="assessment.id" class="relative">
              <CardHeader class="pb-3">
                <div class="flex items-start justify-between">
                  <div class="space-y-1">
                    <CardTitle class="text-base">{{ assessment.name }}</CardTitle>
                    <div class="flex items-center gap-2">
                      <Badge :class="assessmentTypeColors[assessment.type]">
                        {{ assessmentTypeLabels[assessment.type] }}
                      </Badge>
                      <Badge variant="outline" class="text-red-600 border-red-200">Required</Badge>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-semibold">{{ assessment.weight }}%</div>
                    <div class="text-sm text-muted-foreground">{{ assessment.max_points }} pts</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent class="pt-0">
                <div class="space-y-3">
                  <!-- Due Date -->
                  <div class="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock class="h-4 w-4" />
                    {{ formatDueDate(assessment.due_date) }}
                  </div>

                  <!-- Sub-components -->
                  <div v-if="assessment.details.length > 0" class="space-y-2">
                    <div class="text-sm font-medium">Components:</div>
                    <div class="space-y-1">
                      <div
                        v-for="detail in assessment.details"
                        :key="detail.id"
                        class="flex items-center justify-between text-sm bg-gray-50 rounded px-2 py-1"
                      >
                        <span>{{ detail.name }}</span>
                        <span class="font-medium">{{ detail.weight }}%</span>
                      </div>
                    </div>
                  </div>

                  <!-- Grading Progress -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                      <span>Grading Progress</span>
                      <span>{{ gradingProgress.percentage }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        class="h-1.5 rounded-full transition-all duration-300"
                        :class="getProgressColor(gradingProgress.percentage)"
                        :style="{ width: `${gradingProgress.percentage}%` }"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Optional Assessments Section -->
        <div v-if="optionalAssessments.length > 0">
          <h2 class="text-lg font-semibold mb-4 flex items-center gap-2">
            <FileText class="h-5 w-5 text-blue-600" />
            Optional Assessments
          </h2>
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card v-for="assessment in optionalAssessments" :key="assessment.id" class="relative">
              <CardHeader class="pb-3">
                <div class="flex items-start justify-between">
                  <div class="space-y-1">
                    <CardTitle class="text-base">{{ assessment.name }}</CardTitle>
                    <div class="flex items-center gap-2">
                      <Badge :class="assessmentTypeColors[assessment.type]">
                        {{ assessmentTypeLabels[assessment.type] }}
                      </Badge>
                      <Badge variant="outline" class="text-blue-600 border-blue-200">Optional</Badge>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-semibold">{{ assessment.weight }}%</div>
                    <div class="text-sm text-muted-foreground">{{ assessment.max_points }} pts</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent class="pt-0">
                <div class="space-y-3">
                  <!-- Due Date -->
                  <div class="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock class="h-4 w-4" />
                    {{ formatDueDate(assessment.due_date) }}
                  </div>

                  <!-- Sub-components -->
                  <div v-if="assessment.details.length > 0" class="space-y-2">
                    <div class="text-sm font-medium">Components:</div>
                    <div class="space-y-1">
                      <div
                        v-for="detail in assessment.details"
                        :key="detail.id"
                        class="flex items-center justify-between text-sm bg-gray-50 rounded px-2 py-1"
                      >
                        <span>{{ detail.name }}</span>
                        <span class="font-medium">{{ detail.weight }}%</span>
                      </div>
                    </div>
                  </div>

                  <!-- Grading Progress -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                      <span>Grading Progress</span>
                      <span>{{ gradingProgress.percentage }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        class="h-1.5 rounded-full transition-all duration-300"
                        :class="getProgressColor(gradingProgress.percentage)"
                        :style="{ width: `${gradingProgress.percentage}%` }"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="Array.isArray(assessments) && assessments.length === 0" class="text-center py-12">
          <FileText class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-semibold mb-2">No assessments found</h3>
          <p class="text-muted-foreground mb-4">Get started by creating your first assessment component</p>
        </div>
      </div>
    </div>
  </AssessmentLayout>
</template>
