<script setup lang="ts">
import { onMounted, computed } from 'vue'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Progress } from '@/shared/components/ui/progress'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import {
  BookOpen,
  Users,
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  UserCheck,
  MessageSquare,
} from 'lucide-vue-next'
import { useLecturerDashboardStore } from '../stores'

const dashboardStore = useLecturerDashboardStore()

// Computed properties for easy access
const {
  dashboard,
  loading,
  error,
  teachingSummary,
  attendanceSummary,
  studentAlerts,
  recentActivities,
  totalCourses,
  totalStudents,
  todaySessions,
  upcomingSessions,
  criticalAlerts,
  averageAttendance,
  attendanceTrend,
} = dashboardStore

// Load dashboard data on component mount
onMounted(async () => {
  await dashboardStore.fetchDashboard()
})

// Helper functions
const getAttendanceTrendIcon = (trend: string) => {
  switch (trend) {
    case 'improving':
      return TrendingUp
    case 'declining':
      return TrendingDown
    default:
      return Clock
  }
}

const getAttendanceTrendColor = (trend: string) => {
  switch (trend) {
    case 'improving':
      return 'text-green-600'
    case 'declining':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'critical':
      return 'destructive'
    case 'high':
      return 'destructive'
    case 'medium':
      return 'default'
    case 'low':
      return 'secondary'
    default:
      return 'secondary'
  }
}

const formatTime = (timeString: string) => {
  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  })
}

const refreshDashboard = async () => {
  await dashboardStore.refreshDashboard()
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p class="text-muted-foreground">
          Welcome back! Here's an overview of your teaching activities.
        </p>
      </div>
      <Button @click="refreshDashboard" :disabled="loading" variant="outline">
        <Clock class="mr-2 h-4 w-4" />
        Refresh
      </Button>
    </div>

    <!-- Loading State -->
    <div v-if="loading && !dashboard" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading dashboard...</p>
      </div>
    </div>

    <!-- Error State -->
    <Alert v-if="error" variant="destructive">
      <AlertTriangle class="h-4 w-4" />
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Dashboard Content -->
    <div v-if="dashboard && !loading" class="space-y-6">
      <!-- Key Metrics Cards -->
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <!-- Total Courses -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Total Courses</CardTitle>
            <BookOpen class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ totalCourses }}</div>
            <p class="text-xs text-muted-foreground">Active this semester</p>
          </CardContent>
        </Card>

        <!-- Total Students -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Total Students</CardTitle>
            <Users class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ totalStudents }}</div>
            <p class="text-xs text-muted-foreground">Across all courses</p>
          </CardContent>
        </Card>

        <!-- Average Attendance -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Average Attendance</CardTitle>
            <component
              :is="getAttendanceTrendIcon(attendanceTrend)"
              :class="['h-4 w-4', getAttendanceTrendColor(attendanceTrend)]"
            />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ Math.round(averageAttendance) }}%</div>
            <p class="text-xs text-muted-foreground">{{ attendanceTrend }} trend</p>
          </CardContent>
        </Card>

        <!-- Critical Alerts -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Critical Alerts</CardTitle>
            <AlertTriangle class="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold text-red-600">{{ criticalAlerts.length }}</div>
            <p class="text-xs text-muted-foreground">Require attention</p>
          </CardContent>
        </Card>
      </div>

      <!-- Today's Sessions and Upcoming Sessions -->
      <div class="grid gap-4 md:grid-cols-2">
        <!-- Today's Sessions -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Calendar class="h-5 w-5" />
              Today's Sessions
            </CardTitle>
            <CardDescription>
              {{ todaySessions.length }} session{{
                todaySessions.length !== 1 ? 's' : ''
              }}
              scheduled for today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div v-if="todaySessions.length === 0" class="text-center py-4 text-muted-foreground">
                No sessions scheduled for today
              </div>
              <div
                v-for="session in todaySessions.slice(0, 3)"
                :key="session.id"
                class="flex items-center justify-between p-3 border rounded-lg"
              >
                <div class="flex-1">
                  <p class="font-medium">{{ session.session_title }}</p>
                  <p class="text-sm text-muted-foreground">
                    {{ formatTime(session.start_time) }} - {{ formatTime(session.end_time) }}
                  </p>
                  <p class="text-xs text-muted-foreground">{{ session.location }}</p>
                </div>
                <Badge :variant="session.status === 'completed' ? 'default' : 'secondary'">
                  {{ session.status }}
                </Badge>
              </div>
              <Button v-if="todaySessions.length > 3" variant="ghost" size="sm" class="w-full">
                View all {{ todaySessions.length }} sessions
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Upcoming Sessions -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Clock class="h-5 w-5" />
              Upcoming Sessions
            </CardTitle>
            <CardDescription>
              Next {{ upcomingSessions.length }} session{{
                upcomingSessions.length !== 1 ? 's' : ''
              }}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div
                v-if="upcomingSessions.length === 0"
                class="text-center py-4 text-muted-foreground"
              >
                No upcoming sessions
              </div>
              <div
                v-for="session in upcomingSessions.slice(0, 3)"
                :key="session.id"
                class="flex items-center justify-between p-3 border rounded-lg"
              >
                <div class="flex-1">
                  <p class="font-medium">{{ session.session_title }}</p>
                  <p class="text-sm text-muted-foreground">
                    {{ new Date(session.session_date).toLocaleDateString() }}
                  </p>
                  <p class="text-xs text-muted-foreground">
                    {{ formatTime(session.start_time) }} - {{ formatTime(session.end_time) }}
                  </p>
                </div>
                <Badge variant="outline">{{ session.session_type }}</Badge>
              </div>
              <Button v-if="upcomingSessions.length > 3" variant="ghost" size="sm" class="w-full">
                View all upcoming sessions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Student Alerts -->
      <Card
        v-if="
          studentAlerts &&
          (studentAlerts.students_needing_attention.length > 0 ||
            studentAlerts.low_attendance_students.length > 0)
        "
      >
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <AlertTriangle class="h-5 w-5 text-orange-500" />
            Student Alerts
          </CardTitle>
          <CardDescription> Students requiring your attention </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <!-- Critical Alerts -->
            <div
              v-for="alert in criticalAlerts.slice(0, 5)"
              :key="alert.id"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div class="flex-1">
                <p class="font-medium">{{ alert.student_name }}</p>
                <p class="text-sm text-muted-foreground">
                  {{ alert.course_code }} - {{ alert.title }}
                </p>
                <p class="text-xs text-muted-foreground">{{ alert.description }}</p>
              </div>
              <div class="flex items-center gap-2">
                <Badge :variant="getSeverityColor(alert.severity)">{{ alert.severity }}</Badge>
                <Button size="sm" variant="outline" @click="dashboardStore.dismissAlert(alert.id)">
                  Dismiss
                </Button>
              </div>
            </div>

            <Button v-if="criticalAlerts.length > 5" variant="ghost" size="sm" class="w-full">
              View all {{ criticalAlerts.length }} alerts
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Recent Activities -->
      <Card v-if="recentActivities.length > 0">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <MessageSquare class="h-5 w-5" />
            Recent Activities
          </CardTitle>
          <CardDescription> Your latest teaching activities </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="activity in recentActivities.slice(0, 5)"
              :key="activity.id"
              class="flex items-start gap-3 p-3 border rounded-lg"
            >
              <div class="flex-shrink-0 mt-1">
                <CheckCircle
                  v-if="activity.type === 'attendance_marked'"
                  class="h-4 w-4 text-green-500"
                />
                <BookOpen
                  v-else-if="activity.type === 'material_uploaded'"
                  class="h-4 w-4 text-blue-500"
                />
                <UserCheck
                  v-else-if="activity.type === 'session_completed'"
                  class="h-4 w-4 text-purple-500"
                />
                <MessageSquare v-else class="h-4 w-4 text-gray-500" />
              </div>
              <div class="flex-1">
                <p class="font-medium">{{ activity.title }}</p>
                <p class="text-sm text-muted-foreground">{{ activity.description }}</p>
                <p class="text-xs text-muted-foreground">
                  {{ new Date(activity.timestamp).toLocaleString() }}
                </p>
              </div>
            </div>

            <Button v-if="recentActivities.length > 5" variant="ghost" size="sm" class="w-full">
              View all activities
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
