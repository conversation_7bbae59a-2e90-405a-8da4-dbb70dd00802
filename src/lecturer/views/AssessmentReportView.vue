<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAssessmentReports } from '@/lecturer/composables/useAssessmentReports'
import AssessmentLayout from '@/lecturer/components/assessment/AssessmentLayout.vue'
import ChartContainer from '@/lecturer/components/assessment/ChartContainer.vue'
import { registerChartComponents } from '@/lecturer/utils/chartSetup'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Badge } from '@/shared/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { RefreshCw, Download, TrendingUp, TrendingDown, Minus } from 'lucide-vue-next'

// Register Chart.js components
registerChartComponents()

interface Props {
  courseId: string
}

const props = defineProps<Props>()

// Use composable
const {
  chartData,
  statistics, 
  loading, 
  error, 
  refreshData,
  updateFilters,
  filters,
  exportReport
} = useAssessmentReports(parseInt(props.courseId))

// Chart.js configuration for grade distribution (bar chart)
const gradeDistributionConfig = computed(() => ({
  type: 'bar',
  data: chartData.value.gradeDistribution || { labels: [], datasets: [] },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false // Title is handled by ChartContainer
      },
      legend: {
        display: true
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Number of Students'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Grade'
        }
      }
    }
  }
}))

// Chart.js configuration for performance trends (line chart)
const performanceTrendsConfig = computed(() => ({
  type: 'line',
  data: chartData.value.performanceTrends || { labels: [], datasets: [] },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false
      },
      legend: {
        display: true
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Score / Percentage'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Time Period'
        }
      }
    }
  }
}))

// Chart.js configuration for assessment comparison (bar chart)
const assessmentComparisonConfig = computed(() => ({
  type: 'bar',
  data: chartData.value.assessmentComparison || { labels: [], datasets: [] },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false
      },
      legend: {
        display: true
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Score / Percentage'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Assessment'
        }
      }
    }
  }
}))

// Chart.js configuration for completion rates (bar chart)
const completionRatesConfig = computed(() => ({
  type: 'bar',
  data: chartData.value.completionRates || { labels: [], datasets: [] },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false
      },
      legend: {
        display: true
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Completion Rate (%)'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Assessment'
        }
      }
    }
  }
}))

// Handle period filter change
const handlePeriodChange = (period: string) => {
  updateFilters({ period })
}

// Handle export
const handleExport = async (format: 'excel' | 'pdf') => {
  await exportReport(format)
}
</script>

<template>
  <AssessmentLayout :course-id="courseId">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-semibold">Assessment Reports & Analytics</h2>
          <p class="text-muted-foreground">Course {{ courseId }}</p>
        </div>
        <div class="flex items-center gap-2">
          <!-- Period Filter -->
          <Select :value="filters.period" @update:value="handlePeriodChange">
            <SelectTrigger class="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="semester">This Semester</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          
          <!-- Action Buttons -->
          <Button variant="outline" size="sm" @click="refreshData" :disabled="loading">
            <RefreshCw :class="{ 'animate-spin': loading }" class="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button variant="outline" size="sm" @click="handleExport('excel')">
            <Download class="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <!-- Statistics Summary Cards -->
      <div v-if="statistics" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Total Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ statistics.totalStudents }}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Average Grade</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ statistics.averageGrade.toFixed(1) }}%</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Completion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ (statistics.completionRate * 100).toFixed(1) }}%</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">At-Risk Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold text-red-600">{{ statistics.atRiskStudents }}</div>
          </CardContent>
        </Card>
      </div>

      <!-- Charts Section -->
      <Tabs defaultValue="distribution" class="space-y-4">
        <TabsList>
          <TabsTrigger value="distribution">Grade Distribution</TabsTrigger>
          <TabsTrigger value="trends">Performance Trends</TabsTrigger>
          <TabsTrigger value="comparison">Assessment Comparison</TabsTrigger>
          <TabsTrigger value="completion">Completion Rates</TabsTrigger>
        </TabsList>

        <TabsContent value="distribution" class="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Grade Distribution</CardTitle>
              <CardDescription>Distribution of grades across all assessments</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                v-if="chartData.gradeDistribution"
                :config="gradeDistributionConfig"
                :loading="loading"
                :error="error"
                :height="400"
                @onRetry="refreshData"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" class="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>Performance trends over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                v-if="chartData.performanceTrends"
                :config="performanceTrendsConfig"
                :loading="loading"
                :error="error"
                :height="400"
                @onRetry="refreshData"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" class="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assessment Comparison</CardTitle>
              <CardDescription>Compare performance across different assessments</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                v-if="chartData.assessmentComparison"
                :config="assessmentComparisonConfig"
                :loading="loading"
                :error="error"
                :height="400"
                @onRetry="refreshData"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completion" class="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completion Rates</CardTitle>
              <CardDescription>Completion rates for each assessment</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                v-if="chartData.completionRates"
                :config="completionRatesConfig"
                :loading="loading"
                :error="error"
                :height="400"
                @onRetry="refreshData"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  </AssessmentLayout>
</template>
