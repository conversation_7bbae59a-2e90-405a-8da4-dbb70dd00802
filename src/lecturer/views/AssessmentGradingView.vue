<script setup lang="ts">
import AssessmentLayout from '@/lecturer/components/assessment/AssessmentLayout.vue'

interface Props {
  courseId: string
  assessmentId?: string
}

const props = defineProps<Props>()
</script>

<template>
  <AssessmentLayout :course-id="courseId">
    <div class="space-y-6">
      <div class="text-center py-12">
        <h2 class="text-xl font-semibold mb-2">Assessment Grading</h2>
        <p class="text-muted-foreground">
          Grade student submissions for Course {{ courseId }}
          <span v-if="assessmentId">- Assessment {{ assessmentId }}</span>
        </p>
        <p class="text-sm text-muted-foreground mt-4">This view will be implemented in subsequent tasks.</p>
      </div>
    </div>
  </AssessmentLayout>
</template>
