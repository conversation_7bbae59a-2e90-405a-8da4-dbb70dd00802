<script setup lang="ts">
// Props
interface Props {
  sessionId?: string
}

const props = withDefaults(defineProps<Props>(), {
  sessionId: undefined
})

import type {
  AttendanceSummary,
  SessionAttendanceApiResponse,
  SessionInfo,
  SessionRecommendation,
  StudentAttendanceRecord
} from '@/lecturer/types/api';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Checkbox } from '@/shared/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/shared/components/ui/dialog';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Progress } from '@/shared/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select';
import { Textarea } from '@/shared/components/ui/textarea';
import { useBaseApi } from '@/shared/composables/useBaseApi';
import {
  AlertTriangle,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Edit,
  Eye,
  Filter,
  MapPin,
  RefreshCw,
  Save,
  Search,
  Send,
  UserCheck,
  UserMinus,
  UserX,
  Users,
  XCircle
} from 'lucide-vue-next';
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { toast } from 'vue-sonner';

const route = useRoute()
const router = useRouter()
const api = useBaseApi()
const sessionId = ref(props.sessionId || route.params.sessionId as string)

// Local state
const sessionData = ref<SessionAttendanceApiResponse | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)
const searchQuery = ref('')
const statusFilter = ref('all')
const selectedStudents = ref<number[]>([])
const showEditDialog = ref(false)
const showViewDialog = ref(false)
const selectedStudent = ref<StudentAttendanceRecord | null>(null)
const editForm = ref({
  status: '',
  notes: '',
  checkInTime: '',
  minutesLate: 0,
  participationScore: 0
})

// Computed values
const session = computed((): SessionInfo | undefined => sessionData.value?.session)
const students = computed((): StudentAttendanceRecord[] => sessionData.value?.students || [])
const summary = computed((): AttendanceSummary | undefined => sessionData.value?.summary)
// const actions = computed((): SessionActions | undefined => sessionData.value?.actions)
const recommendations = computed((): SessionRecommendation[] => sessionData.value?.recommendations || [])

const filteredStudents = computed((): StudentAttendanceRecord[] => {
  let filtered = students.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((student: StudentAttendanceRecord) =>
      student.full_name.toLowerCase().includes(query) ||
      student.student_number.toLowerCase().includes(query) ||
      student.email.toLowerCase().includes(query)
    )
  }

  // Apply status filter
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter((student: StudentAttendanceRecord) =>
      student.attendance.status === statusFilter.value
    )
  }

  return filtered
})

// Selection management
const isAllSelected = computed(() => {
  if (filteredStudents.value.length === 0) return false
  return filteredStudents.value.every((student: StudentAttendanceRecord) =>
    selectedStudents.value.includes(student.student_id)
  )
})

const isIndeterminate = computed(() => {
  if (selectedStudents.value.length === 0) return false
  if (isAllSelected.value) return false
  return filteredStudents.value.some((student: StudentAttendanceRecord) =>
    selectedStudents.value.includes(student.student_id)
  )
})

// Helper functions
const getAttendanceStatusColor = (status: string) => {
  switch (status) {
    case 'present':
      return 'bg-green-100 text-green-800'
    case 'absent':
      return 'bg-red-100 text-red-800'
    case 'late':
      return 'bg-yellow-100 text-yellow-800'
    case 'excused':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getAttendanceStatusIcon = (status: string) => {
  switch (status) {
    case 'present':
      return UserCheck
    case 'absent':
      return UserX
    case 'late':
      return Clock
    case 'excused':
      return UserMinus
    default:
      return Users
  }
}

const formatTime = (time: string) => {
  if (!time) return 'N/A'
  return new Date(`2000-01-01T${time}`).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

const goBack = () => {
  router.back()
}

// Selection functions
const toggleAllStudents = (value: boolean | 'indeterminate') => {
  const filteredIds = filteredStudents.value.map((s: StudentAttendanceRecord) => s.student_id)

  if (value === true) {
    // Add all filtered students to selection
    selectedStudents.value = [...new Set([...selectedStudents.value, ...filteredIds])]
  } else {
    // Remove all filtered students from selection
    selectedStudents.value = selectedStudents.value.filter(id => !filteredIds.includes(id))
  }
}

const toggleStudent = (studentId: number, checked: boolean) => {
  if (checked) {
    if (!selectedStudents.value.includes(studentId)) {
      selectedStudents.value.push(studentId)
    }
  } else {
    selectedStudents.value = selectedStudents.value.filter(id => id !== studentId)
  }
}

const clearSelection = () => {
  selectedStudents.value = []
}

// Bulk operations
const bulkUpdateAttendance = async (status: string) => {
  try {
    loading.value = true
    const attendanceData = selectedStudents.value.map(studentId => ({
      student_id: studentId,
      status
    }))
    await markAttendance(attendanceData)
    clearSelection()
  } catch (err) {
    console.error('Failed to bulk update attendance:', err)
  } finally {
    loading.value = false
  }
}

// Dialog management
const openEditDialog = (student: StudentAttendanceRecord) => {
  selectedStudent.value = student
  editForm.value = {
    status: student.attendance.status,
    notes: student.attendance.notes || '',
    checkInTime: student.attendance.check_in_time || '',
    minutesLate: student.attendance.minutes_late || 0,
    participationScore: student.attendance.participation_score || 0
  }
  showEditDialog.value = true
}

const openViewDialog = (student: StudentAttendanceRecord) => {
  selectedStudent.value = student
  showViewDialog.value = true
}

const closeEditDialog = () => {
  showEditDialog.value = false
  selectedStudent.value = null
  editForm.value = {
    status: '',
    notes: '',
    checkInTime: '',
    minutesLate: 0,
    participationScore: 0
  }
}

const closeViewDialog = () => {
  showViewDialog.value = false
  selectedStudent.value = null
}

// Refresh functionality
const refreshData = async () => {
  loading.value = true
  try {
    await fetchSessionAttendance()
  } finally {
    loading.value = false
  }
}

const saveAttendanceChanges = async () => {
  if (!selectedStudent.value) return

  try {

    const attendanceData = [{
      student_id: selectedStudent.value.student_id,
      status: editForm.value.status,
      ...(editForm.value.notes && { notes: editForm.value.notes }),
      ...(editForm.value.checkInTime && { check_in_time: editForm.value.checkInTime }),
      ...(editForm.value.minutesLate !== undefined && { minutes_late: editForm.value.minutesLate }),
      ...(editForm.value.participationScore !== undefined && { participation_score: editForm.value.participationScore })
    }]

    await markAttendance(attendanceData)
    closeEditDialog()
    toast.success('Attendance updated successfully')
  } catch (err) {
    console.error('Failed to update attendance:', err)
    toast.error('Failed to update attendance')
  } finally {
  }
}

const exportAttendance = async () => {
  try {
    const response = await api.get(`/lecturer/attendance/${sessionId.value}/export`)
    if (response.success) {
      // Handle file download
      const blob = new Blob([response.data as string], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `attendance-${session.value?.title || sessionId.value}.csv`
      a.click()
      window.URL.revokeObjectURL(url)
    }
  } catch (err) {
    console.error('Failed to export attendance:', err)
  }
}

const markAttendance = async (attendanceData: Array<{
  student_id: number
  status: string
  check_in_time?: string
  minutes_late?: number
  participation_score?: number
  notes?: string
}>) => {
  try {
    const response = await api.post(`/attendance/sessions/${sessionId.value}/mark`, {
      attendance_data: attendanceData
    })

    if (response.success) {
      await fetchSessionAttendance()
    } else {
      throw new Error(response.message || 'Failed to mark attendance')
    }
  } catch (err) {
    console.error('Failed to mark attendance:', err)
    throw err
  }
}

const fetchSessionAttendance = async () => {
  try {
    const response = await api.get(`/lecturer/attendance/sessions/${sessionId.value}`)
    if (response.success) {
      sessionData.value = response.data as SessionAttendanceApiResponse
    } else {
      throw new Error(response.message || 'Failed to load session attendance')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load session attendance'
    throw err
  }
}

// Load session data on component mount
onMounted(async () => {
  loading.value = true
  error.value = null

  try {
    await fetchSessionAttendance()
  } catch (err) {
    console.error('Failed to load session details:', err)
  } finally {
    loading.value = false
  }
})
</script>

<template>

  <!-- Header -->
  <div class="bg-white rounded-lg shadow-sm border p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <div>
        <div class="flex items-center gap-4 mb-2">
          <Button variant="ghost" size="sm" @click="goBack">
            <ArrowLeft class="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 class="text-2xl font-bold text-gray-900">
            {{ session?.title || 'Session Attendance' }}
          </h1>
        </div>
        <div v-if="session" class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
          <div class="flex items-center gap-1">
            <Calendar class="w-4 h-4" />
            {{ formatDate(session.date) }}
          </div>
          <div class="flex items-center gap-1">
            <Clock class="w-4 h-4" />
            {{ formatTime(session.start_time) }} - {{ formatTime(session.end_time) }}
          </div>
          <div class="flex items-center gap-1">
            <MapPin class="w-4 h-4" />
            {{ session.delivery_mode === 'in_person' ? 'In Person' : 'Online' }}
          </div>
          <div class="flex items-center gap-1">
            <Users class="w-4 h-4" />
            {{ session.course?.unit_code }} - {{ session.course?.unit_name }}
          </div>
        </div>
      </div>
      <div class="flex gap-2">
        <Button variant="outline" size="sm" @click="refreshData" :disabled="loading">
          <RefreshCw class="w-4 h-4" :class="{ 'animate-spin': loading }" />
          Refresh
        </Button>
        <Button variant="outline" size="sm" @click="exportAttendance">
          <Download class="w-4 h-4 mr-2" />
          Export
        </Button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div v-if="loading && !sessionData" class="flex items-center justify-center py-12">
    <div class="text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p class="text-muted-foreground">Loading attendance data...</p>
    </div>
  </div>

  <!-- Error State -->
  <Alert v-if="error" variant="destructive">
    <AlertTriangle class="h-4 w-4" />
    <AlertDescription>{{ error }}</AlertDescription>
  </Alert>

  <!-- Summary Cards -->
  <div v-if="sessionData" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Students</p>
            <p class="text-2xl font-bold">{{ summary?.total_enrolled || 0 }}</p>
          </div>
          <Users class="w-8 h-8 text-gray-400" />
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Present</p>
            <p class="text-2xl font-bold text-green-600">{{ summary?.attendance_counts.present || 0 }}</p>
          </div>
          <CheckCircle class="w-8 h-8 text-green-400" />
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Absent</p>
            <p class="text-2xl font-bold text-red-600">{{ summary?.attendance_counts.absent || 0 }}</p>
          </div>
          <XCircle class="w-8 h-8 text-red-400" />
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Late</p>
            <p class="text-2xl font-bold text-yellow-600">{{ summary?.attendance_counts.late || 0 }}</p>
          </div>
          <Clock class="w-8 h-8 text-yellow-400" />
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Attendance Rate</p>
            <p class="text-2xl font-bold">{{ summary?.attendance_rate || 0 }}%</p>
          </div>
          <div class="w-8 h-8 flex items-center justify-center">
            <div class="w-6 h-6 rounded-full bg-gray-200">
              <Progress :value="summary?.attendance_rate || 0" class="w-6 h-6" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>

  <!-- Recommendations -->
  <Alert v-if="recommendations.length > 0" class="items-center">
    <AlertTriangle class="h-4 w-4" />
    <AlertDescription>
      <div v-for="rec in recommendations" :key="rec.message">
        {{ rec.message }}
      </div>
    </AlertDescription>
  </Alert>

  <!-- Controls -->
  <Card v-if="sessionData">
    <CardContent class="p-6">
      <div class="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div class="flex flex-col sm:flex-row gap-4 flex-1">
          <div class="relative flex-1 max-w-sm">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input v-model="searchQuery" placeholder="Search students..." class="pl-10" />
          </div>
          <Select v-model="statusFilter">
            <SelectTrigger class="w-full sm:w-48">
              <Filter class="w-4 h-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Students</SelectItem>
              <SelectItem value="present">Present</SelectItem>
              <SelectItem value="absent">Absent</SelectItem>
              <SelectItem value="late">Late</SelectItem>
              <SelectItem value="excused">Excused</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div v-if="selectedStudents.length > 0" class="flex gap-2">
          <Button size="sm" @click="bulkUpdateAttendance('present')">
            Mark Present ({{ selectedStudents.length }})
          </Button>
          <Button size="sm" variant="outline" @click="bulkUpdateAttendance('absent')">
            Mark Absent
          </Button>
          <Button size="sm" variant="outline" @click="clearSelection">
            Clear Selection
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- Student List -->
  <Card v-if="sessionData">
    <CardHeader>
      <CardTitle>Student Attendance</CardTitle>
      <CardDescription>
        Showing {{ filteredStudents.length }} of {{ students.length }} students
      </CardDescription>
    </CardHeader>
    <CardContent class="p-0">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 border-b">
            <tr>
              <th class="text-left p-4 font-medium text-gray-900 w-5">
                <Checkbox :model-value="isAllSelected ? true : isIndeterminate ? 'indeterminate' : false"
                  @update:model-value="toggleAllStudents" />
              </th>
              <th class="text-left p-4 font-medium text-gray-900 w-5">No</th>
              <th class="text-left p-4 font-medium text-gray-900">Student</th>
              <th class="text-left p-4 font-medium text-gray-900">Status</th>
              <th class="text-left p-4 font-medium text-gray-900 hidden md:table-cell">Check-in</th>
              <th class="text-left p-4 font-medium text-gray-900 hidden lg:table-cell">Notes</th>
              <th class="text-left p-4 font-medium text-gray-900">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-if="filteredStudents.length === 0">
              <td colspan="6" class="p-8 text-center text-gray-500">
                {{ searchQuery || statusFilter !== 'all'
                  ? 'No students match the current filters' : 'No students enrolled' }}
              </td>
            </tr>
            <tr v-for="(student, index) in filteredStudents" :key="student.student_id" class="hover:bg-gray-50">
              <td class="p-4">
                <Checkbox :model-value="selectedStudents.includes(student.student_id)"
                  @update:model-value="(checked: boolean | 'indeterminate') => toggleStudent(student.student_id, checked === true)" />
              </td>
              <td class="p-4 ">{{ index + 1 }}</td>

              <td class="p-4">
                <div>
                  <div class="font-medium text-gray-900">{{ student.full_name }}</div>
                  <div class="text-sm text-gray-500">{{ student.student_number }}</div>
                  <div class="text-sm text-gray-500 md:hidden">{{ student.student_info.program }}</div>
                </div>
              </td>
              <td class="p-4">
                <div class="flex items-center gap-2">
                  <component :is="getAttendanceStatusIcon(student.attendance.status)" class="w-4 h-4" :class="{
                    'text-green-600': student.attendance.status === 'present',
                    'text-red-600': student.attendance.status === 'absent',
                    'text-yellow-600': student.attendance.status === 'late',
                    'text-blue-600': student.attendance.status === 'excused',
                    'text-gray-600': !['present', 'absent', 'late', 'excused'].includes(student.attendance.status)
                  }" />
                  <Badge :class="getAttendanceStatusColor(student.attendance.status)">
                    {{ student.attendance.status_label }}
                  </Badge>
                  <span v-if="student.attendance.minutes_late > 0" class="text-xs text-gray-500">
                    (+{{ student.attendance.minutes_late }}min)
                  </span>
                </div>
              </td>
              <td class="p-4 hidden md:table-cell">
                <div class="text-sm">{{ student.attendance.check_in_time ?
                  formatTime(student.attendance.check_in_time) : '-' }}</div>
              </td>
              <td class="p-4 hidden lg:table-cell">
                <div class="text-sm text-gray-600 max-w-xs truncate">
                  {{ student.attendance.notes || student.attendance.excuse_reason || '-' }}
                </div>
              </td>
              <td class="p-4">
                <div class="flex items-center gap-2">
                  <Button variant="ghost" size="sm" @click="openEditDialog(student)">
                    <Edit class="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" @click="openViewDialog(student)">
                    <Eye class="w-4 h-4" />
                  </Button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </CardContent>
  </Card>

  <!-- Quick Actions -->
  <Card v-if="sessionData">
    <CardHeader>
      <CardTitle>Quick Actions</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="flex flex-wrap gap-4">
        <Button @click="bulkUpdateAttendance('present')" :disabled="selectedStudents.length === 0">
          <UserCheck class="w-4 h-4 mr-2" />
          Mark Selected Present
        </Button>
        <Button variant="outline">
          <Send class="w-4 h-4 mr-2" />
          Send Notifications
        </Button>
        <Button variant="outline">
          <Download class="w-4 h-4 mr-2" />
          Generate Report
        </Button>
      </div>
    </CardContent>
  </Card>

  <!-- Edit Attendance Dialog -->
  <Dialog :open="showEditDialog" @update:open="closeEditDialog">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Edit Attendance</DialogTitle>
        <DialogDescription v-if="selectedStudent">
          Update attendance for {{ selectedStudent.full_name }}
        </DialogDescription>
      </DialogHeader>
      <div class="space-y-4">
        <div>
          <Label>Attendance Status</Label>
          <Select v-model="editForm.status">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="present">Present</SelectItem>
              <SelectItem value="absent">Absent</SelectItem>
              <SelectItem value="late">Late</SelectItem>
              <SelectItem value="excused">Excused</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Check-in Time</Label>
          <Input v-model="editForm.checkInTime" type="time" placeholder="HH:MM" />
        </div>
        <div>
          <Label>Minutes Late</Label>
          <Input v-model="editForm.minutesLate" type="number" min="0" placeholder="0" />
        </div>
        <div>
          <Label>Participation Score</Label>
          <Input v-model="editForm.participationScore" type="number" min="0" max="10" step="0.1" placeholder="0.0" />
        </div>
        <div>
          <Label>Notes</Label>
          <Textarea v-model="editForm.notes" placeholder="Add notes or excuse reason..." />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeEditDialog">
          Cancel
        </Button>
        <Button @click="saveAttendanceChanges">
          <Save class="w-4 h-4 mr-2" />
          Save Changes
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- View Student Details Dialog -->
  <Dialog :open="showViewDialog" @update:open="closeViewDialog">
    <DialogContent class="sm:max-w-lg">
      <DialogHeader>
        <DialogTitle>Student Details</DialogTitle>
        <DialogDescription v-if="selectedStudent">
          Detailed information for {{ selectedStudent.full_name }}
        </DialogDescription>
      </DialogHeader>
      <div v-if="selectedStudent" class="space-y-4">
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <Label class="font-medium">Student Number</Label>
            <p>{{ selectedStudent.student_number }}</p>
          </div>
          <div>
            <Label class="font-medium">Email</Label>
            <p>{{ selectedStudent.email }}</p>
          </div>
          <div>
            <Label class="font-medium">Program</Label>
            <p>{{ selectedStudent.student_info.program }}</p>
          </div>
          <div>
            <Label class="font-medium">Specialization</Label>
            <p>{{ selectedStudent.student_info.specialization }}</p>
          </div>
          <div>
            <Label class="font-medium">Attendance Status</Label>
            <Badge :class="getAttendanceStatusColor(selectedStudent.attendance.status)">
              {{ selectedStudent.attendance.status_label }}
            </Badge>
          </div>
          <div>
            <Label class="font-medium">Check-in Time</Label>
            <p>{{
              selectedStudent.attendance.check_in_time ?
                formatTime(selectedStudent.attendance.check_in_time)
                : 'Not checked in'
            }}</p>
          </div>
          <div>
            <Label class="font-medium">Minutes Late</Label>
            <p>{{ selectedStudent.attendance.minutes_late || 0 }} minutes</p>
          </div>
          <div>
            <Label class="font-medium">Participation Score</Label>
            <p>{{ selectedStudent.attendance.participation_score || 'Not scored' }}</p>
          </div>
        </div>
        <div v-if="selectedStudent.attendance.notes">
          <Label class="font-medium">Notes</Label>
          <p class="text-sm text-gray-600">{{ selectedStudent.attendance.notes }}</p>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
