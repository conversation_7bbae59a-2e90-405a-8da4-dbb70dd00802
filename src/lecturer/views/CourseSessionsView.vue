<script setup lang="ts">
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Input } from '@/shared/components/ui/input'
import { useBase<PERSON>pi } from '@/shared/composables/useBaseApi'
import type { Unit } from '@/shared/types/models/student'
import {
  AlertTriangle,
  ArrowLeft,
  BookOpen,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  Monitor,
  Pause,
  Play,
  Plus,
  Search,
  Users,
  Video,
  XCircle,
} from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { ClassSession } from '../types/models/lecturer'

const route = useRoute()
const router = useRouter()
const api = useBaseApi()

const courseId = ref(route.params.courseId as string)
const searchQuery = ref('')
const selectedStatus = ref('all')
const selectedType = ref('all')

// Local state
const courseUnit = ref<Unit>()
const sessions = ref<ClassSession[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// Filtered sessions
const filteredSessions = computed(() => {
  let filtered = sessions.value
  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (session: ClassSession) =>
        session.session_title.toLowerCase().includes(query) ||
        session.session_description?.toLowerCase().includes(query) ||
        session.session_type.toLowerCase().includes(query),
    )
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(
      (session: ClassSession) => session.status === selectedStatus.value,
    )
  }

  // Type filter
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(
      (session: ClassSession) => session.session_type === selectedType.value,
    )
  }

  // Sort by date
  return filtered.sort(
    (a, b) => new Date(a.session_date).getTime() - new Date(b.session_date).getTime(),
  )
})

// Statistics
const sessionStats = computed(() => {
  const total = sessions.value.length
  const completed = sessions.value.filter((s) => s.status === 'completed').length
  const scheduled = sessions.value.filter((s) => s.status === 'scheduled').length
  const cancelled = sessions.value.filter((s) => s.status === 'cancelled').length

  return { total, completed, scheduled, cancelled }
})

// Helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'scheduled':
      return 'default'
    case 'in_progress':
      return 'secondary'
    case 'completed':
      return 'outline'
    case 'cancelled':
      return 'destructive'
    case 'postponed':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'scheduled':
      return Calendar
    case 'in_progress':
      return Play
    case 'completed':
      return CheckCircle
    case 'cancelled':
      return XCircle
    case 'postponed':
      return Pause
    default:
      return Calendar
  }
}

const getSessionTypeColor = (sessionType: string) => {
  switch (sessionType) {
    case 'lecture':
      return 'default'
    case 'tutorial':
      return 'secondary'
    case 'practical':
      return 'outline'
    case 'laboratory':
      return 'outline'
    case 'exam':
      return 'destructive'
    case 'assessment':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const getDeliveryModeIcon = (mode: string) => {
  switch (mode) {
    case 'online':
      return Monitor
    case 'in_person':
      return MapPin
    case 'hybrid':
      return Video
    case 'blended':
      return Video
    default:
      return MapPin
  }
}


const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}

const viewSessionAttendance = (session: ClassSession) => {
  router.push(`/lecturer/teaching/courses/sessions/${session.id}/attendance`)
}

const goBack = () => {
  router.push('/lecturer/teaching/courses')
}

// API functions
const fetchCourse = async () => {
  try {
    console.log('fetching course', courseId.value)
    const response = await api.lecturer.courses.getUnitById(courseId.value)
    console.log('response', response.data)
    if (response.success) {
      courseUnit.value = response.data
    } else {
      throw new Error(response.message || 'Failed to load course')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load course'
    throw err
  }
}

const fetchSessions = async () => {
  try {
    const response = await api.lecturer.courses.getSessions(courseId.value)
    if (response.success) {
      sessions.value = response.data || []
    } else {
      throw new Error(response.message || 'Failed to load sessions')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load sessions'
    throw err
  }
}

// Load data on component mount
onMounted(async () => {
  loading.value = true
  error.value = null

  try {
    await Promise.all([
      fetchCourse(),
      fetchSessions()
    ])
  } catch (err) {
    console.error('Failed to load course sessions:', err)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center gap-4">
      <Button variant="ghost" size="sm" @click="goBack">
        <ArrowLeft class="h-4 w-4 mr-2" />
        Back to Courses
      </Button>
      <div class="flex-1">
        <h1 class="text-3xl font-bold tracking-tight">Course Sessions</h1>
        <p class="text-muted-foreground">
          {{ courseUnit?.code }} - {{ courseUnit?.name }}
        </p>
      </div>
      <!-- <Button variant="default">
        <Plus class="mr-2 h-4 w-4" />
        Add Session
      </Button> -->
    </div>

    <!-- Loading State -->
    <div v-if="loading && sessions.length === 0" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading sessions...</p>
      </div>
    </div>

    <!-- Error State -->
    <Alert v-if="error" variant="destructive">
      <AlertTriangle class="h-4 w-4" />
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Session Overview Stats -->
    <div v-if="sessions.length > 0" class="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Total Sessions</CardTitle>
          <BookOpen class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ sessionStats.total }}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Completed</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ sessionStats.completed }}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Scheduled</CardTitle>
          <Calendar class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ sessionStats.scheduled }}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Cancelled</CardTitle>
          <XCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ sessionStats.cancelled }}</div>
        </CardContent>
      </Card>
    </div>

    <!-- Search and Filters -->
    <div class="flex items-center gap-4">
      <div class="relative flex-1 max-w-sm">
        <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input v-model="searchQuery" placeholder="Search sessions..." class="pl-8" />
      </div>
      <select v-model="selectedStatus" class="px-3 py-2 border border-input bg-background rounded-md text-sm">
        <option value="all">All Status</option>
        <option value="scheduled">Scheduled</option>
        <option value="in_progress">In Progress</option>
        <option value="completed">Completed</option>
        <option value="cancelled">Cancelled</option>
        <option value="postponed">Postponed</option>
      </select>
      <select v-model="selectedType" class="px-3 py-2 border border-input bg-background rounded-md text-sm">
        <option value="all">All Types</option>
        <option value="lecture">Lecture</option>
        <option value="tutorial">Tutorial</option>
        <option value="practical">Practical</option>
        <option value="laboratory">Laboratory</option>
        <option value="seminar">Seminar</option>
        <option value="workshop">Workshop</option>
        <option value="exam">Exam</option>
        <option value="assessment">Assessment</option>
      </select>
    </div>

    <!-- Sessions List -->
    <div v-if="filteredSessions.length > 0" class="space-y-4">
      <Card v-for="session in filteredSessions" :key="session.id"
        class="cursor-pointer hover:shadow-md transition-shadow" @click="viewSessionAttendance(session)">
        <CardHeader>
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <CardTitle class="text-lg">{{ session.session_title }}</CardTitle>
              <CardDescription class="mt-1">
                {{ session.session_description || 'No description provided' }}
              </CardDescription>
            </div>
            <div class="flex items-center gap-2">
              <Badge :variant="getSessionTypeColor(session.session_type)">
                {{ session.session_type.replace('_', ' ').toUpperCase() }}
              </Badge>
              <Badge :variant="getStatusColor(session.status)">
                <component :is="getStatusIcon(session.status)" class="w-3 h-3 mr-1" />
                {{ session.status.replace('_', ' ').toUpperCase() }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid gap-4 md:grid-cols-4">
            <!-- Date and Time -->
            <div class="flex items-center gap-2 text-sm">
              <Calendar class="h-4 w-4 text-muted-foreground" />
              <span>{{ formatDate(session.session_date) }}</span>
            </div>

            <div class="flex items-center gap-2 text-sm">
              <Clock class="h-4 w-4 text-muted-foreground" />
              <span>{{ session.start_time }} - {{ session.end_time }}</span>
            </div>

            <!-- Delivery Mode -->
            <div class="flex items-center gap-2 text-sm">
              <component :is="getDeliveryModeIcon(session.delivery_mode)" class="h-4 w-4 text-muted-foreground" />
              <span>{{ session.delivery_mode.replace('_', ' ').toUpperCase() }}</span>
            </div>

            <!-- Attendance -->
            <div class="flex items-center gap-2 text-sm">
              <Users class="h-4 w-4 text-muted-foreground" />
              <span>{{ session.actual_attendees }}/{{ session.expected_attendees }}</span>
              <span class="text-muted-foreground">({{ Math.round(session.attendance_percentage) }}%)</span>
            </div>
          </div>

          <!-- Additional Info -->
          <div class="mt-4 flex items-center justify-between">
            <div class="flex items-center gap-4 text-sm text-muted-foreground">
              <span v-if="session.location">{{ session.location }}</span>
              <span v-if="session.is_assessment" class="text-orange-600 font-medium">Assessment</span>
              <span v-if="session.attendance_required" class="text-blue-600 font-medium">Attendance Required</span>
            </div>

            <div class="flex items-center gap-2">
              <Badge v-if="!session.attendance_marked && session.attendance_required" variant="secondary">
                Attendance Pending
              </Badge>
              <Badge v-if="session.status === 'cancelled'" variant="destructive">
                {{ session.cancellation_reason || 'Cancelled' }}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading && sessions.length === 0" class="text-center py-12">
      <BookOpen class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
      <h3 class="text-lg font-semibold mb-2">No sessions found</h3>
      <p class="text-muted-foreground mb-4">This course doesn't have any sessions yet.</p>
      <Button variant="default">
        <Plus class="mr-2 h-4 w-4" />
        Add First Session
      </Button>
    </div>

    <!-- No Results State -->
    <div v-else-if="!loading && filteredSessions.length === 0" class="text-center py-12">
      <Search class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
      <h3 class="text-lg font-semibold mb-2">No sessions match your search</h3>
      <p class="text-muted-foreground mb-4">Try adjusting your search terms or filters.</p>
      <Button variant="outline" @click="
        () => {
          searchQuery = ''
          selectedStatus = 'all'
          selectedType = 'all'
        }
      ">
        Clear Filters
      </Button>
    </div>
  </div>
</template>
