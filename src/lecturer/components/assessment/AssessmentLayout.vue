<script setup lang="ts">
import { Button } from '@/shared/components/ui/button'
import { Separator } from '@/shared/components/ui/separator'
import { Tabs, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { ClipboardList, GraduationCap, BarChart3, ArrowLeft } from 'lucide-vue-next'
import { computed } from 'vue'
import { RouterView, useRoute, useRouter } from 'vue-router'

interface Props {
  courseId: string
}

const props = defineProps<Props>()
const route = useRoute()
const router = useRouter()

// Compute current tab based on route
const currentTab = computed(() => {
  const routeName = route.name as string
  if (routeName?.includes('assessment-management')) return 'management'
  if (routeName?.includes('assessment-grading')) return 'grading'
  if (routeName?.includes('assessment-report')) return 'report'
  return 'management'
})

// Navigation tabs for assessment module
const assessmentTabs = [
  {
    value: 'management',
    label: 'Assessment Management',
    icon: ClipboardList,
    route: 'lecturer-assessment-management',
  },
  {
    value: 'grading',
    label: 'Grading',
    icon: GraduationCap,
    route: 'lecturer-assessment-grading',
  },
  {
    value: 'report',
    label: 'Reports & Analytics',
    icon: BarChart3,
    route: 'lecturer-assessment-report',
  },
]

// Handle tab navigation
const handleTabChange = (tabValue: string | number) => {
  const tab = assessmentTabs.find((t) => t.value === String(tabValue))
  if (tab) {
    router.push({
      name: tab.route,
      params: { courseId: props.courseId },
    })
  }
}

// Navigate back to courses
const navigateBack = () => {
  router.push({ name: 'lecturer-courses' })
}
</script>

<template>
  <div class="flex flex-col space-y-6">
    <!-- Header with back navigation and title -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <Button variant="ghost" size="sm" @click="navigateBack" class="gap-2" aria-label="Back to courses">
          <ArrowLeft class="h-4 w-4" />
          <span class="hidden sm:inline">Back to Courses</span>
        </Button>
        <Separator orientation="vertical" class="h-6" />
        <div>
          <h1 class="text-2xl font-bold tracking-tight">Course Assessments</h1>
          <p class="text-muted-foreground">Manage assessments, grading, and reports for Course {{ courseId }}</p>
        </div>
      </div>
    </div>

    <!-- Assessment navigation tabs -->
    <Tabs :model-value="currentTab" @update:model-value="handleTabChange" class="w-full">
      <TabsList class="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-none lg:inline-flex">
        <TabsTrigger
          v-for="tab in assessmentTabs"
          :key="tab.value"
          :value="tab.value"
          class="gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          :aria-label="`Navigate to ${tab.label}`"
        >
          <component :is="tab.icon" class="h-4 w-4" />
          <span class="hidden sm:inline">{{ tab.label }}</span>
          <span class="sm:hidden">{{ tab.label.split(' ')[0] }}</span>
        </TabsTrigger>
      </TabsList>
    </Tabs>

    <!-- Main content area -->
    <div class="flex-1">
      <slot />
    </div>
  </div>
</template>
