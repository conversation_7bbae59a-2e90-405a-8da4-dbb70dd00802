# Assessment Weight Validation System

## Overview

The real-time weight validation system provides immediate feedback for assessment weight changes, ensuring that lecturers can maintain proper weight allocation without exceeding 100% total weight.

## Key Features

### 1. Real-time Validation

- **Immediate Feedback**: Weight changes are validated as soon as they are entered
- **Visual Indicators**: Clear color-coded feedback (red for errors, yellow for warnings, green for success)
- **Contextual Messages**: Specific error messages explaining what went wrong and how to fix it

### 2. Weight Change Preview

- **Projection Calculations**: Shows what the total weight would be after a change
- **Impact Analysis**: Displays how much weight remains or exceeds the limit
- **Validation Status**: Indicates whether the change is valid before applying it

### 3. Pending Changes Management

- **Change Tracking**: Keeps track of unsaved weight modifications
- **Batch Operations**: Apply multiple weight changes at once
- **Rollback Support**: Clear pending changes without applying them

## Components

### useAssessmentManagement Composable

The main composable that integrates weight validation:

```typescript
const {
  // Weight validation state
  totalWeight,
  isWeightValid,
  weightValidationSummary,
  allValidationMessages,

  // Real-time validation methods
  validateWeightChangeRealTime,
  getWeightChangePreview,
  toggleRealTimeValidation,

  // Pending changes management
  pendingWeightChanges,
  clearPendingWeightChanges,
  applyPendingWeightChanges,
} = useAssessmentManagement(courseOfferingId)
```

### WeightValidationIndicator Component

Visual component that displays weight validation status:

```vue
<WeightValidationIndicator :assessments="assessments" :show-details="true" :show-suggestions="true" />
```

### WeightValidationDemo Component

Demo component showcasing the validation system functionality.

## Usage Examples

### Basic Weight Validation

```typescript
// Validate a weight change before applying
const validation = validateWeightChangeRealTime(assessmentId, newWeight)
if (validation.isValid) {
  // Apply the change
  await updateAssessment({ id: assessmentId, weight: newWeight })
} else {
  // Show error message
  console.error(validation.message)
}
```

### Weight Change Preview

```typescript
// Get preview of weight change impact
const preview = getWeightChangePreview(assessmentId, newWeight)
if (preview) {
  console.log(`Current: ${preview.currentWeight}%`)
  console.log(`New: ${preview.newWeight}%`)
  console.log(`Total after change: ${preview.projectedTotal}%`)
  console.log(`Valid: ${preview.isValid}`)
}
```

### Managing Pending Changes

```typescript
// Check for pending changes
if (pendingWeightChanges.value.size > 0) {
  // Apply all pending changes
  const results = await applyPendingWeightChanges()
  console.log(`Applied ${results.length} changes`)
}

// Or clear without applying
clearPendingWeightChanges()
```

## Validation Rules

### Weight Constraints

- **Maximum Total**: 100% total weight across all assessments
- **Minimum Weight**: Each assessment must have weight > 0
- **Component Weights**: Sub-components must total 100% within each assessment

### Validation Messages

- **Error**: Critical issues that prevent saving (e.g., exceeding 100%)
- **Warning**: Issues that should be addressed but don't prevent saving
- **Info**: Helpful information about the current state
- **Success**: Confirmation of valid states

## Visual Indicators

### Status Colors

- **Red**: Errors (exceeds limit, invalid values)
- **Yellow**: Warnings (approaching limit, uneven distribution)
- **Green**: Success (valid allocation, complete)
- **Blue**: Information (remaining weight, suggestions)
- **Gray**: Empty or neutral states

### Progress Indicators

- **Progress Bar**: Visual representation of weight allocation (0-100%)
- **Badges**: Status indicators (EXCEEDED, COMPLETE, WARNING, etc.)
- **Icons**: Visual cues (AlertTriangle, CheckCircle, Clock, Info)

## Integration with Forms

The validation system integrates seamlessly with form inputs:

```vue
<template>
  <div class="space-y-2">
    <Input v-model.number="assessmentWeight" type="number" min="0" max="100" @input="handleWeightChange" />

    <!-- Real-time validation feedback -->
    <div v-if="validationResult" :class="validationResult.isValid ? 'text-green-600' : 'text-red-600'">
      {{ validationResult.message }}
    </div>
  </div>
</template>

<script setup>
const handleWeightChange = (newWeight) => {
  validationResult.value = validateWeightChangeRealTime(assessmentId, newWeight)
}
</script>
```

## Performance Considerations

- **Debounced Validation**: Validation is debounced to prevent excessive API calls
- **Local Validation**: Primary validation happens client-side for immediate feedback
- **API Fallback**: Server-side validation as backup for complex business rules
- **Efficient Updates**: Only changed assessments trigger re-validation

## Accessibility Features

- **ARIA Labels**: Screen reader support for validation messages
- **Color Independence**: Information conveyed through icons and text, not just color
- **Keyboard Navigation**: Full keyboard support for all validation interactions
- **Focus Management**: Proper focus handling during validation state changes

## Testing

The validation system includes comprehensive unit tests covering:

- Real-time validation logic
- Weight change previews
- Pending changes management
- Error and warning states
- Integration with the assessment management system

Run tests with:

```bash
pnpm test:unit tests/unit/composables/useAssessmentManagement.test.ts
```
