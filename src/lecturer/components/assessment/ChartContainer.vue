<template>
  <div class="chart-container" :class="containerClasses">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center h-full min-h-[200px]">
      <div class="flex flex-col items-center space-y-2">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="text-sm text-muted-foreground">{{ loadingText }}</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center h-full min-h-[200px]">
      <div class="flex flex-col items-center space-y-4 text-center">
        <div class="rounded-full bg-destructive/10 p-3">
          <AlertCircle class="h-6 w-6 text-destructive" />
        </div>
        <div class="space-y-1">
          <h3 class="font-medium text-sm">Failed to load chart</h3>
          <p class="text-sm text-muted-foreground">{{ error }}</p>
        </div>
        <Button v-if="onRetry" variant="outline" size="sm" @click="onRetry">
          <RefreshCw class="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    </div>

    <!-- Chart Content -->
    <div v-else-if="hasData" class="relative h-full w-full">
      <!-- Chart Title -->
      <div v-if="title || description" class="mb-4">
        <h3 v-if="title" class="text-lg font-semibold">{{ title }}</h3>
        <p v-if="description" class="text-sm text-muted-foreground mt-1">{{ description }}</p>
      </div>

      <!-- Chart Canvas Container -->
      <div 
        ref="chartContainerRef"
        class="relative w-full"
        :style="{ height: chartHeight + 'px' }"
        role="img"
        :aria-label="ariaLabel"
      >
        <canvas
          ref="chartCanvasRef"
          class="w-full h-full"
          :width="canvasWidth"
          :height="canvasHeight"
        ></canvas>
      </div>

      <!-- Chart Legend (if external) -->
      <div v-if="showExternalLegend && legendItems.length > 0" class="mt-4">
        <div class="flex flex-wrap gap-4">
          <div
            v-for="(item, index) in legendItems"
            :key="index"
            class="flex items-center space-x-2 text-sm"
          >
            <div
              class="w-3 h-3 rounded-sm"
              :style="{ backgroundColor: item.color }"
            ></div>
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>

      <!-- Accessibility Description -->
      <div v-if="accessibilityDescription" class="sr-only">
        {{ accessibilityDescription }}
      </div>
    </div>

    <!-- No Data State -->
    <div v-else class="flex items-center justify-center h-full min-h-[200px]">
      <div class="flex flex-col items-center space-y-2 text-center">
        <div class="rounded-full bg-muted p-3">
          <BarChart3 class="h-6 w-6 text-muted-foreground" />
        </div>
        <div class="space-y-1">
          <h3 class="font-medium text-sm">No data available</h3>
          <p class="text-sm text-muted-foreground">{{ noDataMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart,
  type ChartConfiguration,
  type ChartData,
  type ChartOptions,
  type LegendItem,
} from 'chart.js'
import { AlertCircle, RefreshCw, BarChart3 } from 'lucide-vue-next'
import { Button } from '@/shared/components/ui/button'

interface Props {
  // Chart configuration
  config: ChartConfiguration
  // Data for the chart
  data?: ChartData<any>
  // Chart options override
  options?: Partial<ChartOptions>
  // Display props
  title?: string
  description?: string
  loading?: boolean
  loadingText?: string
  error?: string | null
  noDataMessage?: string
  // Responsive props
  responsive?: boolean
  maintainAspectRatio?: boolean
  aspectRatio?: number
  height?: number | string
  width?: number | string
  // Accessibility props
  ariaLabel?: string
  accessibilityDescription?: string
  // Legend props
  showExternalLegend?: boolean
  // CSS classes
  class?: string
  // Callbacks
  onRetry?: () => void
  onChartClick?: (event: any, elements: any[]) => void
  onChartHover?: (event: any, elements: any[]) => void
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: 'Loading chart...',
  noDataMessage: 'No data to display',
  responsive: true,
  maintainAspectRatio: true,
  aspectRatio: 2,
  height: 'auto',
  width: '100%',
  showExternalLegend: false,
})

const emit = defineEmits<{
  chartReady: [chart: Chart]
  chartDestroyed: []
  chartUpdated: [chart: Chart]
  dataPointClick: [dataPoint: any, event: any]
  dataPointHover: [dataPoint: any, event: any]
}>()

// Template refs
const chartContainerRef = ref<HTMLDivElement>()
const chartCanvasRef = ref<HTMLCanvasElement>()

// Chart instance
const chartInstance = ref<Chart | null>(null)

// Responsive dimensions
const canvasWidth = ref(400)
const canvasHeight = ref(200)
const resizeObserver = ref<ResizeObserver>()

// Computed properties
const containerClasses = computed(() => {
  const classes = ['relative', 'w-full']
  
  if (props.class) {
    classes.push(props.class)
  }
  
  return classes.join(' ')
})

const chartHeight = computed(() => {
  if (typeof props.height === 'number') {
    return props.height
  }
  
  if (props.height === 'auto' && props.maintainAspectRatio && props.aspectRatio) {
    return canvasWidth.value / props.aspectRatio
  }
  
  return 400 // Default height
})

const hasData = computed(() => {
  if (props.data) {
    const datasets = props.data.datasets
    return datasets && datasets.length > 0 && datasets.some(dataset => 
      dataset.data && Array.isArray(dataset.data) && dataset.data.length > 0
    )
  }
  
  return props.config.data && 
    props.config.data.datasets && 
    props.config.data.datasets.length > 0 &&
    props.config.data.datasets.some(dataset => 
      dataset.data && Array.isArray(dataset.data) && dataset.data.length > 0
    )
})

const legendItems = computed<LegendItem[]>(() => {
  if (!chartInstance.value) return []
  
  const legend = chartInstance.value.legend
  return legend?.legendItems || []
})

const ariaLabel = computed(() => {
  if (props.ariaLabel) return props.ariaLabel
  if (props.title) return `Chart: ${props.title}`
  return 'Data visualization chart'
})

// Chart management functions
const createChart = async (): Promise<void> => {
  if (!chartCanvasRef.value || props.loading || props.error || !hasData.value) {
    return
  }

  destroyChart()

  try {
    const ctx = chartCanvasRef.value.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get canvas context')
    }

    // Merge configuration with props
    const chartConfig: ChartConfiguration = {
      ...props.config,
      data: props.data || props.config.data,
      options: {
        responsive: props.responsive,
        maintainAspectRatio: props.maintainAspectRatio,
        aspectRatio: props.aspectRatio,
        ...props.config.options,
        ...props.options,
        // Add click and hover handlers
        onClick: (event, elements) => {
          props.onChartClick?.(event, elements)
          if (elements.length > 0) {
            const dataPoint = getDataPointFromElement(elements[0])
            emit('dataPointClick', dataPoint, event)
          }
        },
        onHover: (event, elements) => {
          props.onChartHover?.(event, elements)
          if (elements.length > 0) {
            const dataPoint = getDataPointFromElement(elements[0])
            emit('dataPointHover', dataPoint, event)
          }
        },
        // Hide legend if external legend is enabled
        plugins: {
          ...props.config.options?.plugins,
          legend: {
            ...props.config.options?.plugins?.legend,
            display: props.showExternalLegend ? false : props.config.options?.plugins?.legend?.display !== false,
          },
        },
      },
    }

    chartInstance.value = new Chart(ctx, chartConfig)
    emit('chartReady', chartInstance.value)
  } catch (error) {
    console.error('Failed to create chart:', error)
    emit('chartDestroyed')
  }
}

const destroyChart = (): void => {
  if (chartInstance.value) {
    chartInstance.value.destroy()
    chartInstance.value = null
    emit('chartDestroyed')
  }
}

const updateChart = async (): Promise<void> => {
  if (!chartInstance.value) {
    await createChart()
    return
  }

  try {
    // Update data
    if (props.data) {
      chartInstance.value.data = props.data
    }

    // Update options
    if (props.options) {
      Object.assign(chartInstance.value.options, props.options)
    }

    chartInstance.value.update('active')
    emit('chartUpdated', chartInstance.value)
  } catch (error) {
    console.error('Failed to update chart:', error)
    // Recreate chart if update fails
    await createChart()
  }
}

const getDataPointFromElement = (element: any): any => {
  if (!chartInstance.value || !element) return null

  const { datasetIndex, index } = element
  const dataset = chartInstance.value.data.datasets[datasetIndex]
  const data = dataset?.data[index]
  const label = chartInstance.value.data.labels?.[index]

  return {
    value: data,
    label,
    datasetLabel: dataset?.label,
    datasetIndex,
    index,
  }
}

// Responsive handling
const updateDimensions = (): void => {
  if (!chartContainerRef.value) return

  const rect = chartContainerRef.value.getBoundingClientRect()
  canvasWidth.value = rect.width
  canvasHeight.value = chartHeight.value

  if (chartInstance.value && props.responsive) {
    chartInstance.value.resize()
  }
}

const setupResizeObserver = (): void => {
  if (!chartContainerRef.value || !props.responsive) return

  resizeObserver.value = new ResizeObserver(() => {
    updateDimensions()
  })

  resizeObserver.value.observe(chartContainerRef.value)
}

const cleanupResizeObserver = (): void => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = undefined
  }
}

// Public methods
const refresh = async (): Promise<void> => {
  await updateChart()
}

const resize = (): void => {
  updateDimensions()
}

const exportChart = (format: 'png' | 'jpeg' = 'png'): string | null => {
  if (!chartInstance.value) return null

  return chartInstance.value.toBase64Image(`image/${format}`, 1.0)
}

// Lifecycle
onMounted(async () => {
  await nextTick()
  updateDimensions()
  setupResizeObserver()
  
  if (hasData.value && !props.loading && !props.error) {
    await createChart()
  }
})

onUnmounted(() => {
  destroyChart()
  cleanupResizeObserver()
})

// Watchers
watch(
  () => [props.data, props.config],
  async () => {
    if (hasData.value && !props.loading && !props.error) {
      await updateChart()
    } else {
      destroyChart()
    }
  },
  { deep: true }
)

watch(
  () => [props.loading, props.error],
  async () => {
    if (!props.loading && !props.error && hasData.value) {
      await createChart()
    } else {
      destroyChart()
    }
  }
)

watch(
  () => props.options,
  async () => {
    if (chartInstance.value) {
      await updateChart()
    }
  },
  { deep: true }
)

// Expose public methods
defineExpose({
  refresh,
  resize,
  exportChart,
  chartInstance: computed(() => chartInstance.value),
})
</script>

<style scoped>
.chart-container {
  /* Ensure proper aspect ratio calculation */
  container-type: inline-size;
}

/* Responsive adjustments */
@container (max-width: 640px) {
  .chart-container {
    /* Mobile-specific adjustments */
  }
}

@container (max-width: 480px) {
  .chart-container {
    /* Small mobile adjustments */
  }
}
</style>
