<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/shared/components/ui/badge'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/shared/components/ui/tooltip'
import { useGradeScale } from '@/lecturer/composables/useGradeScale'
import { CheckCircle, AlertTriangle, Clock, Minus, Eye, EyeOff } from 'lucide-vue-next'
import { GradeStatus } from '@/lecturer/types/models/assessment'

interface Props {
  percentage?: number | null
  status?: GradeStatus
  isExcluded?: boolean
  isLate?: boolean
  showTooltip?: boolean
  showStatus?: boolean
  showPercentage?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'outline' | 'secondary'
}

const props = withDefaults(defineProps<Props>(), {
  percentage: null,
  status: GradeStatus.MISSING,
  isExcluded: false,
  isLate: false,
  showTooltip: true,
  showStatus: true,
  showPercentage: true,
  size: 'md',
  variant: 'default',
})

const { getLetterGrade, getGradeLetter, getGradeColor, getGradeDescription, isPassingGrade } = useGradeScale()

// Computed properties
const gradeInfo = computed(() => {
  if (props.percentage === null || props.percentage === undefined) {
    return {
      letter: 'N/A',
      color: 'text-gray-400 bg-gray-50',
      description: 'No grade recorded',
      isPassing: false,
    }
  }

  const grade = getLetterGrade(props.percentage)
  return {
    letter: grade?.letter || 'N/A',
    color: grade?.color || 'text-gray-400 bg-gray-50',
    description: grade?.description || 'No grade available',
    isPassing: grade?.passingGrade || false,
  }
})

const statusIcon = computed(() => {
  switch (props.status) {
    case GradeStatus.FINAL:
      return CheckCircle
    case GradeStatus.PROVISIONAL:
      return Clock
    case GradeStatus.DRAFT:
      return AlertTriangle
    case GradeStatus.PENDING:
      return Clock
    case GradeStatus.MISSING:
      return Minus
    default:
      return Minus
  }
})

const statusColor = computed(() => {
  switch (props.status) {
    case GradeStatus.FINAL:
      return 'text-green-600'
    case GradeStatus.PROVISIONAL:
      return 'text-blue-600'
    case GradeStatus.DRAFT:
      return 'text-yellow-600'
    case GradeStatus.PENDING:
      return 'text-orange-600'
    case GradeStatus.MISSING:
      return 'text-gray-400'
    default:
      return 'text-gray-400'
  }
})

const displayText = computed(() => {
  if (props.isExcluded) {
    return 'EXC'
  }
  
  if (props.percentage === null || props.percentage === undefined) {
    return 'N/A'
  }

  let text = gradeInfo.value.letter
  if (props.showPercentage && props.percentage !== null) {
    text += ` (${props.percentage.toFixed(1)}%)`
  }
  
  return text
})

const badgeClasses = computed(() => {
  const baseClasses = []
  
  // Size classes
  switch (props.size) {
    case 'sm':
      baseClasses.push('text-xs px-2 py-1')
      break
    case 'lg':
      baseClasses.push('text-base px-3 py-2')
      break
    default:
      baseClasses.push('text-sm px-2.5 py-1.5')
  }
  
  // Color classes
  if (props.isExcluded) {
    baseClasses.push('text-gray-500 bg-gray-100 border-gray-300')
  } else if (props.percentage !== null) {
    const colorClasses = gradeInfo.value.color.split(' ')
    baseClasses.push(...colorClasses)
  } else {
    baseClasses.push('text-gray-400 bg-gray-50')
  }
  
  // Additional styling for excluded or late grades
  if (props.isExcluded) {
    baseClasses.push('line-through opacity-75')
  }
  
  if (props.isLate && !props.isExcluded) {
    baseClasses.push('border-orange-300 border-2')
  }
  
  return baseClasses.join(' ')
})

const tooltipContent = computed(() => {
  const content = []
  
  if (props.isExcluded) {
    content.push('Grade: Excluded from calculations')
  } else if (props.percentage !== null) {
    content.push(`Grade: ${gradeInfo.value.letter} (${props.percentage.toFixed(1)}%)`)
    content.push(`Description: ${gradeInfo.value.description}`)
    content.push(`Status: ${gradeInfo.value.isPassing ? 'Passing' : 'Failing'}`)
  } else {
    content.push('Grade: Not recorded')
  }
  
  content.push(`Status: ${props.status}`)
  
  if (props.isLate) {
    content.push('⚠️ Late submission')
  }
  
  if (props.isExcluded) {
    content.push('👁️‍🗨️ Excluded from final calculation')
  }
  
  return content
})
</script>

<template>
  <div class="inline-flex items-center gap-1">
    <!-- Grade Badge -->
    <TooltipProvider v-if="showTooltip">
      <Tooltip>
        <TooltipTrigger as-child>
          <Badge :class="badgeClasses" :variant="variant">
            {{ displayText }}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div class="space-y-1">
            <p v-for="line in tooltipContent" :key="line" class="text-sm">
              {{ line }}
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
    
    <!-- Without tooltip -->
    <Badge v-else :class="badgeClasses" :variant="variant">
      {{ displayText }}
    </Badge>
    
    <!-- Status Indicator -->
    <component
      v-if="showStatus"
      :is="statusIcon"
      class="h-3 w-3"
      :class="statusColor"
      :title="`Status: ${status}`"
    />
    
    <!-- Exclusion Indicator -->
    <EyeOff
      v-if="isExcluded"
      class="h-3 w-3 text-gray-500"
      title="Excluded from calculations"
    />
    
    <!-- Late Indicator -->
    <AlertTriangle
      v-if="isLate && !isExcluded"
      class="h-3 w-3 text-orange-500"
      title="Late submission"
    />
  </div>
</template>

<style scoped>
.line-through {
  text-decoration: line-through;
}
</style>
