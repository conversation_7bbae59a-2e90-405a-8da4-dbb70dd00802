<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Separator } from '@/shared/components/ui/separator'
import { useGradeScale } from '@/lecturer/composables/useGradeScale'
import { Calculator, Info, Award, AlertTriangle } from 'lucide-vue-next'

interface Props {
  showScaleSelector?: boolean
  showStatistics?: boolean
  studentPercentages?: number[]
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showScaleSelector: true,
  showStatistics: false,
  studentPercentages: () => [],
  compact: false,
})

const {
  availableScales,
  currentScale,
  scaleEntries,
  scaleName,
  scaleDescription,
  setGradeScale,
  getGradeStatistics,
} = useGradeScale()

// Computed properties
const statistics = computed(() => {
  if (!props.showStatistics || props.studentPercentages.length === 0) {
    return null
  }
  return getGradeStatistics(props.studentPercentages)
})

const handleScaleChange = (scaleId: string) => {
  setGradeScale(scaleId)
}
</script>

<template>
  <Card>
    <CardHeader>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Calculator class="h-5 w-5" />
          <CardTitle>{{ compact ? 'Grade Scale' : 'Grade Scale & Legend' }}</CardTitle>
        </div>
        
        <!-- Scale Selector -->
        <Select 
          v-if="showScaleSelector" 
          :value="currentScale.id" 
          @update:value="handleScaleChange"
        >
          <SelectTrigger class="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem 
              v-for="scale in availableScales" 
              :key="scale.id" 
              :value="scale.id"
            >
              {{ scale.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <CardDescription v-if="!compact">
        {{ scaleDescription }}
      </CardDescription>
    </CardHeader>
    
    <CardContent class="space-y-4">
      <!-- Grade Scale Entries -->
      <div>
        <h4 v-if="!compact" class="font-medium mb-3">Grade Ranges</h4>
        <div :class="compact ? 'grid grid-cols-3 md:grid-cols-5 gap-2' : 'space-y-2'">
          <div
            v-for="entry in scaleEntries"
            :key="entry.letter"
            :class="compact 
              ? 'flex flex-col items-center p-2 border rounded-lg text-center' 
              : 'flex items-center justify-between p-3 border rounded-lg'"
          >
            <div :class="compact ? 'space-y-1' : 'flex items-center gap-3'">
              <Badge :class="entry.color" variant="outline">
                {{ entry.letter }}
              </Badge>
              <div :class="compact ? 'text-xs' : ''">
                <div class="font-medium">
                  {{ entry.minPercentage }}% - {{ entry.maxPercentage }}%
                </div>
                <div v-if="!compact" class="text-sm text-muted-foreground">
                  {{ entry.description }}
                </div>
              </div>
            </div>
            
            <div v-if="!compact" class="flex items-center gap-2">
              <Badge variant="secondary" class="text-xs">
                {{ entry.gradePoints }} GP
              </Badge>
              <Award 
                v-if="entry.passingGrade" 
                class="h-4 w-4 text-green-600" 
                title="Passing grade"
              />
              <AlertTriangle 
                v-else 
                class="h-4 w-4 text-red-600" 
                title="Failing grade"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Statistics -->
      <div v-if="statistics && !compact">
        <Separator class="my-4" />
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Info class="h-4 w-4" />
          Class Statistics
        </h4>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center p-3 border rounded-lg">
            <div class="text-2xl font-bold">{{ statistics.totalStudents }}</div>
            <div class="text-sm text-muted-foreground">Total Students</div>
          </div>
          
          <div class="text-center p-3 border rounded-lg">
            <div class="text-2xl font-bold">{{ statistics.averagePercentage.toFixed(1) }}%</div>
            <div class="text-sm text-muted-foreground">Average Score</div>
          </div>
          
          <div class="text-center p-3 border rounded-lg">
            <div class="text-2xl font-bold">{{ statistics.averageGradePoints.toFixed(2) }}</div>
            <div class="text-sm text-muted-foreground">Average GPA</div>
          </div>
          
          <div class="text-center p-3 border rounded-lg">
            <div class="text-2xl font-bold">{{ statistics.passingRate.toFixed(1) }}%</div>
            <div class="text-sm text-muted-foreground">Passing Rate</div>
          </div>
        </div>
        
        <!-- Grade Distribution -->
        <div class="mt-4">
          <h5 class="font-medium mb-2">Grade Distribution</h5>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
            <div
              v-for="entry in scaleEntries"
              :key="entry.letter"
              class="flex items-center justify-between p-2 border rounded"
            >
              <Badge :class="entry.color" variant="outline" class="text-xs">
                {{ entry.letter }}
              </Badge>
              <span class="text-sm font-medium">
                {{ statistics.distribution[entry.letter] || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Compact Statistics -->
      <div v-else-if="statistics && compact" class="text-center">
        <div class="flex justify-center gap-4 text-sm">
          <span>Avg: {{ statistics.averagePercentage.toFixed(1) }}%</span>
          <span>Pass: {{ statistics.passingRate.toFixed(1) }}%</span>
        </div>
      </div>
      
      <!-- Legend for Status Indicators -->
      <div v-if="!compact">
        <Separator class="my-4" />
        <h4 class="font-medium mb-3">Status Indicators</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Final - Grade is finalized</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Provisional - Grade is provisional</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span>Draft - Grade is in draft</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Pending - Grade is pending</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span>Missing - No grade recorded</span>
          </div>
          <div class="flex items-center gap-2">
            <AlertTriangle class="w-3 h-3 text-orange-500" />
            <span>Late submission</span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
/* Additional styles if needed */
</style>
