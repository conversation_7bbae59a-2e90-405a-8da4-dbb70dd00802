<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Textarea } from '@/shared/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Separator } from '@/shared/components/ui/separator'
import { Checkbox } from '@/shared/components/ui/checkbox'
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  Calculator,
  FileText,
  User,
  MessageSquare,
  Shield,
} from 'lucide-vue-next'
import type { GradeEntry } from '@/lecturer/types/models/assessment'

interface Props {
  gradeEntry: GradeEntry
  assessmentDueDate?: string
  latePenaltyPerDay?: number
  maxLateDays?: number
}

interface LateExcuse {
  id?: number
  reason: string
  documentation?: string
  status: 'pending' | 'approved' | 'denied'
  reviewedBy?: string
  reviewedAt?: string
  reviewNotes?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:grade': [grade: Partial<GradeEntry>]
  'approve-excuse': [excuse: LateExcuse]
  'deny-excuse': [excuse: LateExcuse, reason: string]
}>()

// Local state
const showExcuseDialog = ref(false)
const showPenaltyCalculator = ref(false)
const customPenalty = ref<number>(props.gradeEntry.late_penalty || 0)
const excuseReason = ref('')
const excuseDocumentation = ref('')
const denyReason = ref('')
const showDenyDialog = ref(false)

// Mock excuse data - in real app this would come from props or API
const currentExcuse = ref<LateExcuse | null>(null)

// Computed properties
const isLateSubmission = computed(() => props.gradeEntry.is_late)

const daysLate = computed(() => {
  if (!props.gradeEntry.submission_date || !props.assessmentDueDate) return 0

  const submissionDate = new Date(props.gradeEntry.submission_date)
  const dueDate = new Date(props.assessmentDueDate)
  const diffTime = submissionDate.getTime() - dueDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return Math.max(0, diffDays)
})

const calculatedPenalty = computed(() => {
  if (!isLateSubmission.value || !props.latePenaltyPerDay) return 0

  const effectiveDaysLate = Math.min(daysLate.value, props.maxLateDays || daysLate.value)
  return Math.min(effectiveDaysLate * props.latePenaltyPerDay, 100)
})

const adjustedScore = computed(() => {
  const originalScore = props.gradeEntry.percentage_score || 0
  const penalty = customPenalty.value || calculatedPenalty.value
  return Math.max(0, originalScore - penalty)
})

const penaltyStatusColor = computed(() => {
  if (!isLateSubmission.value) return 'text-green-600'
  if (currentExcuse.value?.status === 'approved') return 'text-blue-600'
  if (props.gradeEntry.late_penalty > 0) return 'text-red-600'
  return 'text-yellow-600'
})

const penaltyStatusIcon = computed(() => {
  if (!isLateSubmission.value) return CheckCircle
  if (currentExcuse.value?.status === 'approved') return Shield
  if (props.gradeEntry.late_penalty > 0) return AlertTriangle
  return Clock
})

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Methods
const applyCalculatedPenalty = () => {
  customPenalty.value = calculatedPenalty.value
  updateGrade()
}

const applyCustomPenalty = () => {
  updateGrade()
}

const removePenalty = () => {
  customPenalty.value = 0
  updateGrade()
}

const updateGrade = () => {
  emit('update:grade', {
    late_penalty: customPenalty.value,
    percentage_score: adjustedScore.value,
  })
}

const submitExcuse = () => {
  if (!excuseReason.value.trim()) return

  const excuse: LateExcuse = {
    reason: excuseReason.value,
    documentation: excuseDocumentation.value,
    status: 'pending',
  }

  currentExcuse.value = excuse
  showExcuseDialog.value = false

  // Reset form
  excuseReason.value = ''
  excuseDocumentation.value = ''
}

const approveExcuse = () => {
  if (!currentExcuse.value) return

  const approvedExcuse = {
    ...currentExcuse.value,
    status: 'approved' as const,
    reviewedAt: new Date().toISOString(),
  }

  currentExcuse.value = approvedExcuse

  // Remove penalty when excuse is approved
  customPenalty.value = 0
  updateGrade()

  emit('approve-excuse', approvedExcuse)
}

const denyExcuse = () => {
  if (!currentExcuse.value || !denyReason.value.trim()) return

  const deniedExcuse = {
    ...currentExcuse.value,
    status: 'denied' as const,
    reviewedAt: new Date().toISOString(),
    reviewNotes: denyReason.value,
  }

  currentExcuse.value = deniedExcuse
  showDenyDialog.value = false
  denyReason.value = ''

  emit('deny-excuse', deniedExcuse, denyReason.value)
}

// Watch for changes in grade entry
watch(
  () => props.gradeEntry.late_penalty,
  (newPenalty) => {
    customPenalty.value = newPenalty || 0
  },
  { immediate: true },
)
</script>

<template>
  <Card
    class="border-l-4"
    :class="{
      'border-l-green-500': !isLateSubmission,
      'border-l-blue-500': currentExcuse?.status === 'approved',
      'border-l-red-500': isLateSubmission && gradeEntry.late_penalty > 0,
      'border-l-yellow-500': isLateSubmission && gradeEntry.late_penalty === 0,
    }"
  >
    <CardHeader class="pb-3">
      <div class="flex items-center justify-between">
        <CardTitle class="text-lg flex items-center gap-2">
          <component :is="penaltyStatusIcon" class="h-5 w-5" :class="penaltyStatusColor" />
          Late Submission Management
        </CardTitle>

        <Badge :variant="isLateSubmission ? 'destructive' : 'default'" class="gap-1">
          <Clock class="h-3 w-3" />
          {{ isLateSubmission ? `${daysLate} days late` : 'On time' }}
        </Badge>
      </div>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Submission Timeline -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
        <div class="space-y-2">
          <Label class="text-sm font-medium flex items-center gap-2">
            <Calendar class="h-4 w-4" />
            Due Date
          </Label>
          <p class="text-sm">
            {{ assessmentDueDate ? formatDate(assessmentDueDate) : 'Not set' }}
          </p>
        </div>

        <div class="space-y-2">
          <Label class="text-sm font-medium flex items-center gap-2">
            <FileText class="h-4 w-4" />
            Submitted
          </Label>
          <p
            class="text-sm"
            :class="{
              'text-red-600': isLateSubmission,
              'text-green-600': !isLateSubmission,
            }"
          >
            {{ gradeEntry.submission_date ? formatDate(gradeEntry.submission_date) : 'Not submitted' }}
          </p>
        </div>
      </div>

      <!-- Late Penalty Calculation -->
      <div v-if="isLateSubmission" class="space-y-4">
        <Separator />

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <h4 class="font-medium flex items-center gap-2">
              <Calculator class="h-4 w-4" />
              Penalty Calculation
            </h4>

            <Button variant="outline" size="sm" @click="showPenaltyCalculator = !showPenaltyCalculator">
              {{ showPenaltyCalculator ? 'Hide' : 'Show' }} Calculator
            </Button>
          </div>

          <!-- Penalty Calculator -->
          <div v-if="showPenaltyCalculator" class="p-4 border rounded-lg bg-background space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <Label class="text-xs text-muted-foreground">Days Late</Label>
                <p class="font-medium">{{ daysLate }}</p>
              </div>
              <div>
                <Label class="text-xs text-muted-foreground">Penalty per Day</Label>
                <p class="font-medium">{{ latePenaltyPerDay || 0 }}%</p>
              </div>
              <div>
                <Label class="text-xs text-muted-foreground">Max Late Days</Label>
                <p class="font-medium">{{ maxLateDays || 'No limit' }}</p>
              </div>
            </div>

            <div class="space-y-3">
              <div class="flex items-center justify-between text-sm">
                <span>Calculated Penalty:</span>
                <span class="font-medium text-red-600">-{{ calculatedPenalty }}%</span>
              </div>

              <div class="flex items-center justify-between text-sm">
                <span>Original Score:</span>
                <span class="font-medium">{{ gradeEntry.percentage_score || 0 }}%</span>
              </div>

              <Separator />

              <div class="flex items-center justify-between font-medium">
                <span>Adjusted Score:</span>
                <span
                  :class="{
                    'text-red-600': adjustedScore < (gradeEntry.percentage_score || 0),
                    'text-green-600': adjustedScore >= (gradeEntry.percentage_score || 0),
                  }"
                >
                  {{ adjustedScore }}%
                </span>
              </div>
            </div>

            <!-- Penalty Actions -->
            <div class="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant="outline"
                @click="applyCalculatedPenalty"
                :disabled="customPenalty === calculatedPenalty"
              >
                Apply Calculated Penalty
              </Button>

              <Button size="sm" variant="outline" @click="removePenalty" :disabled="customPenalty === 0">
                Remove Penalty
              </Button>
            </div>
          </div>

          <!-- Custom Penalty Input -->
          <div class="space-y-2">
            <Label for="custom-penalty" class="text-sm font-medium">Custom Penalty (%)</Label>
            <div class="flex gap-2">
              <Input
                id="custom-penalty"
                type="number"
                min="0"
                max="100"
                step="0.1"
                v-model.number="customPenalty"
                class="w-32"
                placeholder="0"
              />
              <Button size="sm" @click="applyCustomPenalty" :disabled="customPenalty === gradeEntry.late_penalty">
                Apply
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Late Excuse Management -->
      <div v-if="isLateSubmission" class="space-y-4">
        <Separator />

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <h4 class="font-medium flex items-center gap-2">
              <MessageSquare class="h-4 w-4" />
              Late Excuse
            </h4>

            <Dialog v-model:open="showExcuseDialog">
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" :disabled="currentExcuse?.status === 'pending'">
                  {{ currentExcuse ? 'View Excuse' : 'Submit Excuse' }}
                </Button>
              </DialogTrigger>

              <DialogContent class="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Late Submission Excuse</DialogTitle>
                  <DialogDescription>
                    {{ currentExcuse ? 'Review the submitted excuse' : 'Submit a reason for late submission' }}
                  </DialogDescription>
                </DialogHeader>

                <div class="space-y-4">
                  <div class="space-y-2">
                    <Label for="excuse-reason">Reason for Late Submission</Label>
                    <Textarea
                      id="excuse-reason"
                      v-model="excuseReason"
                      placeholder="Explain the circumstances that led to the late submission..."
                      rows="3"
                      :readonly="!!currentExcuse"
                      :value="currentExcuse?.reason || excuseReason"
                    />
                  </div>

                  <div class="space-y-2">
                    <Label for="excuse-documentation">Supporting Documentation (Optional)</Label>
                    <Textarea
                      id="excuse-documentation"
                      v-model="excuseDocumentation"
                      placeholder="Reference any supporting documents or additional details..."
                      rows="2"
                      :readonly="!!currentExcuse"
                      :value="currentExcuse?.documentation || excuseDocumentation"
                    />
                  </div>
                </div>

                <DialogFooter>
                  <Button v-if="!currentExcuse" @click="submitExcuse" :disabled="!excuseReason.trim()">
                    Submit Excuse
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <!-- Current Excuse Status -->
          <div v-if="currentExcuse" class="p-4 border rounded-lg space-y-3">
            <div class="flex items-center justify-between">
              <Badge
                :variant="
                  currentExcuse.status === 'approved'
                    ? 'default'
                    : currentExcuse.status === 'denied'
                      ? 'destructive'
                      : 'secondary'
                "
                class="gap-1"
              >
                <component
                  :is="
                    currentExcuse.status === 'approved'
                      ? CheckCircle
                      : currentExcuse.status === 'denied'
                        ? XCircle
                        : Clock
                  "
                  class="h-3 w-3"
                />
                {{ currentExcuse.status.charAt(0).toUpperCase() + currentExcuse.status.slice(1) }}
              </Badge>

              <div v-if="currentExcuse.status === 'pending'" class="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  @click="approveExcuse"
                  class="text-green-600 border-green-600 hover:bg-green-50"
                >
                  <CheckCircle class="h-4 w-4 mr-1" />
                  Approve
                </Button>

                <Dialog v-model:open="showDenyDialog">
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline" class="text-red-600 border-red-600 hover:bg-red-50">
                      <XCircle class="h-4 w-4 mr-1" />
                      Deny
                    </Button>
                  </DialogTrigger>

                  <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Deny Late Excuse</DialogTitle>
                      <DialogDescription>Provide a reason for denying this late submission excuse.</DialogDescription>
                    </DialogHeader>

                    <div class="space-y-2">
                      <Label for="deny-reason">Reason for Denial</Label>
                      <Textarea
                        id="deny-reason"
                        v-model="denyReason"
                        placeholder="Explain why this excuse is not acceptable..."
                        rows="3"
                      />
                    </div>

                    <DialogFooter>
                      <Button variant="destructive" @click="denyExcuse" :disabled="!denyReason.trim()">
                        Deny Excuse
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <div class="text-sm space-y-2">
              <div>
                <span class="font-medium">Reason:</span>
                <p class="mt-1 text-muted-foreground">{{ currentExcuse.reason }}</p>
              </div>

              <div v-if="currentExcuse.documentation">
                <span class="font-medium">Documentation:</span>
                <p class="mt-1 text-muted-foreground">{{ currentExcuse.documentation }}</p>
              </div>

              <div v-if="currentExcuse.reviewedAt" class="pt-2 border-t">
                <span class="font-medium">Reviewed:</span>
                <p class="mt-1 text-muted-foreground">
                  {{ formatDate(currentExcuse.reviewedAt) }}
                  {{ currentExcuse.reviewedBy ? `by ${currentExcuse.reviewedBy}` : '' }}
                </p>

                <div v-if="currentExcuse.reviewNotes" class="mt-2">
                  <span class="font-medium">Review Notes:</span>
                  <p class="mt-1 text-muted-foreground">{{ currentExcuse.reviewNotes }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Summary -->
      <Alert
        v-if="isLateSubmission"
        :class="{
          'border-blue-500 bg-blue-50': currentExcuse?.status === 'approved',
          'border-red-500 bg-red-50': currentExcuse?.status === 'denied' || gradeEntry.late_penalty > 0,
          'border-yellow-500 bg-yellow-50': currentExcuse?.status === 'pending',
        }"
      >
        <component :is="penaltyStatusIcon" class="h-4 w-4" />
        <AlertDescription>
          <span v-if="currentExcuse?.status === 'approved'" class="text-blue-700">
            Late excuse approved. No penalty applied.
          </span>
          <span v-else-if="currentExcuse?.status === 'denied'" class="text-red-700">
            Late excuse denied. Penalty of {{ gradeEntry.late_penalty }}% applied.
          </span>
          <span v-else-if="currentExcuse?.status === 'pending'" class="text-yellow-700">
            Late excuse pending review. Current penalty: {{ gradeEntry.late_penalty }}%
          </span>
          <span v-else class="text-red-700">Late submission with {{ gradeEntry.late_penalty }}% penalty applied.</span>
        </AlertDescription>
      </Alert>
    </CardContent>
  </Card>
</template>

<style scoped>
/* Custom styling for late submission indicators */
.border-l-4 {
  border-left-width: 4px;
}

/* Ensure proper contrast for status colors */
.text-green-600 {
  color: #059669;
}

.text-blue-600 {
  color: #2563eb;
}

.text-red-600 {
  color: #dc2626;
}

.text-yellow-600 {
  color: #d97706;
}

/* Focus indicators for accessibility */
input:focus,
textarea:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .text-muted-foreground {
    color: inherit;
    opacity: 0.8;
  }
}
</style>
