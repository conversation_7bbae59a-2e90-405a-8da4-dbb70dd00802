<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { But<PERSON> } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Textarea } from '@/shared/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Separator } from '@/shared/components/ui/separator'
import { Progress } from '@/shared/components/ui/progress'
import {
  Shield,
  ShieldA<PERSON>t,
  ShieldCheck,
  ShieldX,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  MessageSquare,
  Eye,
  Flag,
  Scale,
  Gavel,
  History,
} from 'lucide-vue-next'
import type { GradeEntry } from '@/lecturer/types/models/assessment'
import { AcademicIntegrityStatus } from '@/lecturer/types/models/assessment'

interface Props {
  gradeEntry: GradeEntry
}

interface IntegrityCase {
  id?: number
  studentId: number
  assessmentId: number
  type: 'plagiarism' | 'collusion' | 'unauthorized_assistance' | 'falsification' | 'other'
  severity: 'minor' | 'moderate' | 'major' | 'severe'
  status: AcademicIntegrityStatus
  plagiarismScore?: number
  description: string
  evidence: string[]
  investigatorNotes: string
  studentResponse?: string
  appealReason?: string
  appealEvidence?: string
  appealStatus?: 'pending' | 'approved' | 'denied'
  resolution?: string
  penalty?: string
  createdAt: string
  updatedAt: string
  reviewedBy?: string
  reviewedAt?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:grade': [grade: Partial<GradeEntry>]
  'flag-integrity': [case_: Partial<IntegrityCase>]
  'update-case': [case_: IntegrityCase]
  'submit-appeal': [appeal: { reason: string; evidence: string }]
}>()

// Local state
const showFlagDialog = ref(false)
const showCaseDialog = ref(false)
const showAppealDialog = ref(false)
const showHistoryDialog = ref(false)

// Form state
const flagForm = ref({
  type: 'plagiarism' as IntegrityCase['type'],
  severity: 'moderate' as IntegrityCase['severity'],
  plagiarismScore: 0,
  description: '',
  evidence: [''],
  investigatorNotes: '',
})

const appealForm = ref({
  reason: '',
  evidence: '',
})

const resolutionForm = ref({
  status: props.gradeEntry.academic_integrity,
  resolution: '',
  penalty: '',
})

// Mock case data - in real app this would come from props or API
const currentCase = ref<IntegrityCase | null>(null)
const caseHistory = ref<IntegrityCase[]>([])

// Computed properties
const hasIntegrityFlag = computed(() => props.gradeEntry.academic_integrity !== AcademicIntegrityStatus.CLEAR)

const integrityStatusConfig = computed(() => {
  const status = props.gradeEntry.academic_integrity

  const configs = {
    [AcademicIntegrityStatus.CLEAR]: {
      icon: ShieldCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      label: 'Clear',
      description: 'No academic integrity concerns',
    },
    [AcademicIntegrityStatus.FLAGGED]: {
      icon: ShieldAlert,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      label: 'Flagged',
      description: 'Potential academic integrity violation detected',
    },
    [AcademicIntegrityStatus.UNDER_REVIEW]: {
      icon: Shield,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      label: 'Under Review',
      description: 'Case is being investigated',
    },
    [AcademicIntegrityStatus.VIOLATION_CONFIRMED]: {
      icon: ShieldX,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      label: 'Violation Confirmed',
      description: 'Academic integrity violation has been confirmed',
    },
    [AcademicIntegrityStatus.APPEAL_PENDING]: {
      icon: Scale,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      label: 'Appeal Pending',
      description: 'Student has submitted an appeal',
    },
    [AcademicIntegrityStatus.APPEAL_APPROVED]: {
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      label: 'Appeal Approved',
      description: 'Appeal was successful, violation overturned',
    },
    [AcademicIntegrityStatus.APPEAL_DENIED]: {
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      label: 'Appeal Denied',
      description: 'Appeal was unsuccessful, violation stands',
    },
  }

  return configs[status] || configs[AcademicIntegrityStatus.CLEAR]
})

const plagiarismScoreColor = computed(() => {
  const score = props.gradeEntry.plagiarism_score || 0
  if (score < 20) return 'text-green-600'
  if (score < 40) return 'text-yellow-600'
  if (score < 70) return 'text-orange-600'
  return 'text-red-600'
})

const severityConfig = {
  minor: { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Minor' },
  moderate: { color: 'text-orange-600', bg: 'bg-orange-100', label: 'Moderate' },
  major: { color: 'text-red-600', bg: 'bg-red-100', label: 'Major' },
  severe: { color: 'text-red-800', bg: 'bg-red-200', label: 'Severe' },
}

const typeLabels = {
  plagiarism: 'Plagiarism',
  collusion: 'Collusion',
  unauthorized_assistance: 'Unauthorized Assistance',
  falsification: 'Falsification',
  other: 'Other',
}

// Methods
const addEvidenceField = () => {
  flagForm.value.evidence.push('')
}

const removeEvidenceField = (index: number) => {
  if (flagForm.value.evidence.length > 1) {
    flagForm.value.evidence.splice(index, 1)
  }
}

const submitFlag = () => {
  if (!flagForm.value.description.trim()) return

  const integrityCase: Partial<IntegrityCase> = {
    studentId: props.gradeEntry.student_id,
    type: flagForm.value.type,
    severity: flagForm.value.severity,
    plagiarismScore: flagForm.value.plagiarismScore,
    description: flagForm.value.description,
    evidence: flagForm.value.evidence.filter((e) => e.trim()),
    investigatorNotes: flagForm.value.investigatorNotes,
    status: AcademicIntegrityStatus.FLAGGED,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  currentCase.value = integrityCase as IntegrityCase
  showFlagDialog.value = false

  // Update grade entry
  emit('update:grade', {
    academic_integrity: AcademicIntegrityStatus.FLAGGED,
    plagiarism_score: flagForm.value.plagiarismScore,
    plagiarism_notes: flagForm.value.description,
  })

  emit('flag-integrity', integrityCase)

  // Reset form
  resetFlagForm()
}

const updateCaseStatus = (newStatus: AcademicIntegrityStatus) => {
  if (!currentCase.value) return

  const updatedCase = {
    ...currentCase.value,
    status: newStatus,
    updatedAt: new Date().toISOString(),
    reviewedAt: new Date().toISOString(),
    reviewedBy: 'Current User', // This should come from auth
  }

  if (resolutionForm.value.resolution) {
    updatedCase.resolution = resolutionForm.value.resolution
  }

  if (resolutionForm.value.penalty) {
    updatedCase.penalty = resolutionForm.value.penalty
  }

  currentCase.value = updatedCase

  emit('update:grade', {
    academic_integrity: newStatus,
  })

  emit('update-case', updatedCase)

  showCaseDialog.value = false
}

const submitAppeal = () => {
  if (!appealForm.value.reason.trim()) return

  if (currentCase.value) {
    const updatedCase = {
      ...currentCase.value,
      status: AcademicIntegrityStatus.APPEAL_PENDING,
      appealReason: appealForm.value.reason,
      appealEvidence: appealForm.value.evidence,
      appealStatus: 'pending' as const,
      updatedAt: new Date().toISOString(),
    }

    currentCase.value = updatedCase

    emit('update:grade', {
      academic_integrity: AcademicIntegrityStatus.APPEAL_PENDING,
    })

    emit('submit-appeal', {
      reason: appealForm.value.reason,
      evidence: appealForm.value.evidence,
    })
  }

  showAppealDialog.value = false

  // Reset form
  appealForm.value = { reason: '', evidence: '' }
}

const clearFlag = () => {
  currentCase.value = null

  emit('update:grade', {
    academic_integrity: AcademicIntegrityStatus.CLEAR,
    plagiarism_score: undefined,
    plagiarism_notes: undefined,
  })

  resetFlagForm()
}

const resetFlagForm = () => {
  flagForm.value = {
    type: 'plagiarism',
    severity: 'moderate',
    plagiarismScore: 0,
    description: '',
    evidence: [''],
    investigatorNotes: '',
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Initialize with existing data
watch(
  () => props.gradeEntry,
  (newGrade) => {
    if (newGrade.academic_integrity !== AcademicIntegrityStatus.CLEAR && !currentCase.value) {
      // Mock case data based on grade entry
      currentCase.value = {
        id: 1,
        studentId: newGrade.student_id,
        assessmentId: newGrade.assessment_detail_id,
        type: 'plagiarism',
        severity: 'moderate',
        status: newGrade.academic_integrity,
        plagiarismScore: newGrade.plagiarism_score,
        description: newGrade.plagiarism_notes || 'Potential academic integrity violation detected',
        evidence: [],
        investigatorNotes: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    }
  },
  { immediate: true },
)
</script>

<template>
  <Card class="border-l-4" :class="integrityStatusConfig.borderColor">
    <CardHeader class="pb-3">
      <div class="flex items-center justify-between">
        <CardTitle class="text-lg flex items-center gap-2">
          <component :is="integrityStatusConfig.icon" class="h-5 w-5" :class="integrityStatusConfig.color" />
          Academic Integrity
        </CardTitle>

        <Badge :variant="hasIntegrityFlag ? 'destructive' : 'default'" class="gap-1">
          <component :is="integrityStatusConfig.icon" class="h-3 w-3" />
          {{ integrityStatusConfig.label }}
        </Badge>
      </div>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Status Overview -->
      <div class="p-4 rounded-lg" :class="integrityStatusConfig.bgColor">
        <div class="flex items-start gap-3">
          <component :is="integrityStatusConfig.icon" class="h-5 w-5 mt-0.5" :class="integrityStatusConfig.color" />
          <div class="flex-1">
            <p class="font-medium" :class="integrityStatusConfig.color">
              {{ integrityStatusConfig.label }}
            </p>
            <p class="text-sm text-muted-foreground mt-1">
              {{ integrityStatusConfig.description }}
            </p>
          </div>
        </div>
      </div>

      <!-- Plagiarism Score -->
      <div v-if="gradeEntry.plagiarism_score !== undefined" class="space-y-2">
        <div class="flex items-center justify-between">
          <Label class="text-sm font-medium">Plagiarism Score</Label>
          <span class="text-sm font-medium" :class="plagiarismScoreColor">{{ gradeEntry.plagiarism_score }}%</span>
        </div>
        <Progress
          :value="gradeEntry.plagiarism_score"
          class="h-2"
          :class="{
            '[&>div]:bg-green-500': gradeEntry.plagiarism_score < 20,
            '[&>div]:bg-yellow-500': gradeEntry.plagiarism_score >= 20 && gradeEntry.plagiarism_score < 40,
            '[&>div]:bg-orange-500': gradeEntry.plagiarism_score >= 40 && gradeEntry.plagiarism_score < 70,
            '[&>div]:bg-red-500': gradeEntry.plagiarism_score >= 70,
          }"
        />
        <div class="flex justify-between text-xs text-muted-foreground">
          <span>Low Risk</span>
          <span>High Risk</span>
        </div>
      </div>

      <!-- Current Case Details -->
      <div v-if="currentCase" class="space-y-4">
        <Separator />

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <h4 class="font-medium flex items-center gap-2">
              <FileText class="h-4 w-4" />
              Case Details
            </h4>

            <div class="flex gap-2">
              <Button variant="outline" size="sm" @click="showHistoryDialog = true">
                <History class="h-4 w-4 mr-1" />
                History
              </Button>

              <Button variant="outline" size="sm" @click="showCaseDialog = true">
                <Eye class="h-4 w-4 mr-1" />
                View Case
              </Button>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
            <div class="space-y-2">
              <Label class="text-sm font-medium">Type</Label>
              <p class="text-sm">{{ typeLabels[currentCase.type] }}</p>
            </div>

            <div class="space-y-2">
              <Label class="text-sm font-medium">Severity</Label>
              <Badge
                :class="severityConfig[currentCase.severity].bg + ' ' + severityConfig[currentCase.severity].color"
                variant="outline"
              >
                {{ severityConfig[currentCase.severity].label }}
              </Badge>
            </div>

            <div class="space-y-2">
              <Label class="text-sm font-medium">Created</Label>
              <p class="text-sm">{{ formatDate(currentCase.createdAt) }}</p>
            </div>

            <div class="space-y-2">
              <Label class="text-sm font-medium">Last Updated</Label>
              <p class="text-sm">{{ formatDate(currentCase.updatedAt) }}</p>
            </div>
          </div>

          <div v-if="currentCase.description" class="space-y-2">
            <Label class="text-sm font-medium">Description</Label>
            <p class="text-sm text-muted-foreground p-3 bg-muted/50 rounded-lg">
              {{ currentCase.description }}
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-2 pt-4">
        <Dialog v-model:open="showFlagDialog" v-if="!hasIntegrityFlag">
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" class="text-red-600 border-red-600 hover:bg-red-50">
              <Flag class="h-4 w-4 mr-1" />
              Flag for Review
            </Button>
          </DialogTrigger>

          <DialogContent class="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Flag Academic Integrity Concern</DialogTitle>
              <DialogDescription>Report a potential academic integrity violation for investigation.</DialogDescription>
            </DialogHeader>

            <div class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <Label for="flag-type">Violation Type</Label>
                  <Select v-model="flagForm.type">
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="plagiarism">Plagiarism</SelectItem>
                      <SelectItem value="collusion">Collusion</SelectItem>
                      <SelectItem value="unauthorized_assistance">Unauthorized Assistance</SelectItem>
                      <SelectItem value="falsification">Falsification</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div class="space-y-2">
                  <Label for="flag-severity">Severity</Label>
                  <Select v-model="flagForm.severity">
                    <SelectTrigger>
                      <SelectValue placeholder="Select severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="minor">Minor</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="major">Major</SelectItem>
                      <SelectItem value="severe">Severe</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div v-if="flagForm.type === 'plagiarism'" class="space-y-2">
                <Label for="plagiarism-score">Plagiarism Score (%)</Label>
                <Input
                  id="plagiarism-score"
                  type="number"
                  min="0"
                  max="100"
                  v-model.number="flagForm.plagiarismScore"
                  placeholder="0"
                />
              </div>

              <div class="space-y-2">
                <Label for="flag-description">Description</Label>
                <Textarea
                  id="flag-description"
                  v-model="flagForm.description"
                  placeholder="Describe the suspected violation and circumstances..."
                  rows="4"
                />
              </div>

              <div class="space-y-2">
                <Label>Evidence</Label>
                <div class="space-y-2">
                  <div v-for="(evidence, index) in flagForm.evidence" :key="index" class="flex gap-2">
                    <Input
                      v-model="flagForm.evidence[index]"
                      placeholder="Describe evidence or reference supporting materials..."
                      class="flex-1"
                    />
                    <Button
                      v-if="flagForm.evidence.length > 1"
                      variant="outline"
                      size="sm"
                      @click="removeEvidenceField(index)"
                    >
                      <XCircle class="h-4 w-4" />
                    </Button>
                  </div>
                  <Button variant="outline" size="sm" @click="addEvidenceField" class="w-full">Add Evidence</Button>
                </div>
              </div>

              <div class="space-y-2">
                <Label for="investigator-notes">Investigator Notes (Optional)</Label>
                <Textarea
                  id="investigator-notes"
                  v-model="flagForm.investigatorNotes"
                  placeholder="Additional notes for the investigation..."
                  rows="3"
                />
              </div>
            </div>

            <DialogFooter>
              <Button @click="submitFlag" :disabled="!flagForm.description.trim()" class="bg-red-600 hover:bg-red-700">
                Submit Flag
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Button
          v-if="hasIntegrityFlag && currentCase?.status === AcademicIntegrityStatus.FLAGGED"
          variant="outline"
          size="sm"
          @click="updateCaseStatus(AcademicIntegrityStatus.UNDER_REVIEW)"
        >
          <Shield class="h-4 w-4 mr-1" />
          Start Investigation
        </Button>

        <Button
          v-if="currentCase?.status === AcademicIntegrityStatus.UNDER_REVIEW"
          variant="outline"
          size="sm"
          @click="updateCaseStatus(AcademicIntegrityStatus.VIOLATION_CONFIRMED)"
          class="text-red-600 border-red-600 hover:bg-red-50"
        >
          <Gavel class="h-4 w-4 mr-1" />
          Confirm Violation
        </Button>

        <Button
          v-if="currentCase?.status === AcademicIntegrityStatus.UNDER_REVIEW"
          variant="outline"
          size="sm"
          @click="clearFlag"
          class="text-green-600 border-green-600 hover:bg-green-50"
        >
          <CheckCircle class="h-4 w-4 mr-1" />
          Clear Flag
        </Button>

        <Dialog
          v-model:open="showAppealDialog"
          v-if="currentCase?.status === AcademicIntegrityStatus.VIOLATION_CONFIRMED"
        >
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Scale class="h-4 w-4 mr-1" />
              Submit Appeal
            </Button>
          </DialogTrigger>

          <DialogContent class="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Submit Appeal</DialogTitle>
              <DialogDescription>Submit an appeal against the academic integrity violation.</DialogDescription>
            </DialogHeader>

            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="appeal-reason">Reason for Appeal</Label>
                <Textarea
                  id="appeal-reason"
                  v-model="appealForm.reason"
                  placeholder="Explain why you believe the violation finding is incorrect..."
                  rows="4"
                />
              </div>

              <div class="space-y-2">
                <Label for="appeal-evidence">Supporting Evidence</Label>
                <Textarea
                  id="appeal-evidence"
                  v-model="appealForm.evidence"
                  placeholder="Provide any additional evidence or documentation..."
                  rows="3"
                />
              </div>
            </div>

            <DialogFooter>
              <Button @click="submitAppeal" :disabled="!appealForm.reason.trim()">Submit Appeal</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Button
          v-if="currentCase?.status === AcademicIntegrityStatus.APPEAL_PENDING"
          variant="outline"
          size="sm"
          @click="updateCaseStatus(AcademicIntegrityStatus.APPEAL_APPROVED)"
          class="text-green-600 border-green-600 hover:bg-green-50"
        >
          <CheckCircle class="h-4 w-4 mr-1" />
          Approve Appeal
        </Button>

        <Button
          v-if="currentCase?.status === AcademicIntegrityStatus.APPEAL_PENDING"
          variant="outline"
          size="sm"
          @click="updateCaseStatus(AcademicIntegrityStatus.APPEAL_DENIED)"
          class="text-red-600 border-red-600 hover:bg-red-50"
        >
          <XCircle class="h-4 w-4 mr-1" />
          Deny Appeal
        </Button>
      </div>

      <!-- Case Management Dialog -->
      <Dialog v-model:open="showCaseDialog">
        <DialogContent class="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Academic Integrity Case</DialogTitle>
            <DialogDescription>Manage the academic integrity case for this submission.</DialogDescription>
          </DialogHeader>

          <div v-if="currentCase" class="space-y-4">
            <!-- Case Overview -->
            <div class="p-4 border rounded-lg space-y-3">
              <div class="flex items-center justify-between">
                <h4 class="font-medium">Case Overview</h4>
                <Badge
                  :class="severityConfig[currentCase.severity].bg + ' ' + severityConfig[currentCase.severity].color"
                  variant="outline"
                >
                  {{ severityConfig[currentCase.severity].label }}
                </Badge>
              </div>

              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="font-medium">Type:</span>
                  {{ typeLabels[currentCase.type] }}
                </div>
                <div>
                  <span class="font-medium">Status:</span>
                  {{ integrityStatusConfig.label }}
                </div>
                <div>
                  <span class="font-medium">Created:</span>
                  {{ formatDate(currentCase.createdAt) }}
                </div>
                <div v-if="currentCase.reviewedAt">
                  <span class="font-medium">Reviewed:</span>
                  {{ formatDate(currentCase.reviewedAt) }}
                </div>
              </div>
            </div>

            <!-- Description and Evidence -->
            <div class="space-y-3">
              <div>
                <Label class="font-medium">Description</Label>
                <p class="text-sm text-muted-foreground mt-1 p-3 bg-muted/50 rounded-lg">
                  {{ currentCase.description }}
                </p>
              </div>

              <div v-if="currentCase.evidence.length > 0">
                <Label class="font-medium">Evidence</Label>
                <ul class="text-sm text-muted-foreground mt-1 space-y-1">
                  <li v-for="(evidence, index) in currentCase.evidence" :key="index" class="flex items-start gap-2">
                    <span class="text-xs bg-muted px-2 py-1 rounded">{{ index + 1 }}</span>
                    {{ evidence }}
                  </li>
                </ul>
              </div>

              <div v-if="currentCase.investigatorNotes">
                <Label class="font-medium">Investigator Notes</Label>
                <p class="text-sm text-muted-foreground mt-1 p-3 bg-muted/50 rounded-lg">
                  {{ currentCase.investigatorNotes }}
                </p>
              </div>
            </div>

            <!-- Appeal Information -->
            <div v-if="currentCase.appealReason" class="space-y-3">
              <Separator />
              <h4 class="font-medium">Appeal Information</h4>

              <div>
                <Label class="font-medium">Appeal Reason</Label>
                <p class="text-sm text-muted-foreground mt-1 p-3 bg-muted/50 rounded-lg">
                  {{ currentCase.appealReason }}
                </p>
              </div>

              <div v-if="currentCase.appealEvidence">
                <Label class="font-medium">Appeal Evidence</Label>
                <p class="text-sm text-muted-foreground mt-1 p-3 bg-muted/50 rounded-lg">
                  {{ currentCase.appealEvidence }}
                </p>
              </div>
            </div>

            <!-- Resolution -->
            <div v-if="currentCase.status === AcademicIntegrityStatus.UNDER_REVIEW" class="space-y-3">
              <Separator />
              <h4 class="font-medium">Resolution</h4>

              <div class="space-y-2">
                <Label for="resolution-notes">Resolution Notes</Label>
                <Textarea
                  id="resolution-notes"
                  v-model="resolutionForm.resolution"
                  placeholder="Document the resolution and reasoning..."
                  rows="3"
                />
              </div>

              <div class="space-y-2">
                <Label for="penalty-notes">Penalty/Action Taken</Label>
                <Textarea
                  id="penalty-notes"
                  v-model="resolutionForm.penalty"
                  placeholder="Describe any penalties or actions taken..."
                  rows="2"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <div class="flex gap-2">
              <Button
                v-if="currentCase?.status === AcademicIntegrityStatus.UNDER_REVIEW"
                variant="outline"
                @click="updateCaseStatus(AcademicIntegrityStatus.VIOLATION_CONFIRMED)"
              >
                Confirm Violation
              </Button>
              <Button v-if="currentCase?.status === AcademicIntegrityStatus.UNDER_REVIEW" @click="clearFlag">
                Clear Flag
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <!-- History Dialog -->
      <Dialog v-model:open="showHistoryDialog">
        <DialogContent class="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Case History</DialogTitle>
            <DialogDescription>Timeline of all academic integrity actions for this student.</DialogDescription>
          </DialogHeader>

          <div class="space-y-4 max-h-96 overflow-y-auto">
            <div v-if="caseHistory.length === 0" class="text-center py-8 text-muted-foreground">
              No previous cases found for this student.
            </div>

            <div v-for="case_ in caseHistory" :key="case_.id" class="border rounded-lg p-4 space-y-2">
              <div class="flex items-center justify-between">
                <span class="font-medium">{{ typeLabels[case_.type] }}</span>
                <Badge variant="outline">{{ case_.status }}</Badge>
              </div>
              <p class="text-sm text-muted-foreground">{{ case_.description }}</p>
              <p class="text-xs text-muted-foreground">{{ formatDate(case_.createdAt) }}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <!-- Status Alert -->
      <Alert :class="integrityStatusConfig.bgColor + ' ' + integrityStatusConfig.borderColor">
        <component :is="integrityStatusConfig.icon" class="h-4 w-4" />
        <AlertDescription>
          <span :class="integrityStatusConfig.color">
            {{ integrityStatusConfig.description }}
          </span>
          <span v-if="currentCase?.resolution" class="block mt-2 text-sm">
            <strong>Resolution:</strong>
            {{ currentCase.resolution }}
          </span>
        </AlertDescription>
      </Alert>
    </CardContent>
  </Card>
</template>

<style scoped>
/* Custom styling for integrity status indicators */
.border-l-4 {
  border-left-width: 4px;
}

/* Progress bar color overrides */
.progress-green [data-progress-indicator] {
  background-color: #10b981;
}

.progress-yellow [data-progress-indicator] {
  background-color: #f59e0b;
}

.progress-orange [data-progress-indicator] {
  background-color: #f97316;
}

.progress-red [data-progress-indicator] {
  background-color: #ef4444;
}

/* Focus indicators for accessibility */
input:focus,
textarea:focus,
button:focus,
[role='combobox']:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .text-muted-foreground {
    color: inherit;
    opacity: 0.8;
  }
}
</style>
