<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Textarea } from '@/shared/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import { useAssessmentStore } from '@/lecturer/stores/assessment'
import { useWeightValidation } from '@/lecturer/composables/useWeightValidation'
import {
  AssessmentType,
  SubmissionType,
  type Assessment,
  type CreateAssessmentRequest,
  type UpdateAssessmentRequest,
  type CreateAssessmentDetailRequest,
} from '@/lecturer/types/models/assessment'
import { Plus, Trash2, AlertTriangle, GripVertical, Info } from 'lucide-vue-next'

interface Props {
  open: boolean
  courseId: string
  assessment?: Assessment | null
  mode: 'create' | 'edit'
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', assessment: Assessment): void
  (e: 'error', error: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const assessmentStore = useAssessmentStore()
const { loading, assessments } = storeToRefs(assessmentStore)

// Weight validation
const { validateWeightChange, validateComponentWeights } = useWeightValidation(assessments)

// Form state
const formData = ref({
  name: '',
  type: AssessmentType.ASSIGNMENT,
  weight: 0,
  max_points: 100,
  due_date: '',
  is_required: true,
  description: '',
  instructions: '',
  submission_type: SubmissionType.ONLINE,
  late_penalty_per_day: 0,
  max_late_days: 0,
  allow_resubmission: false,
  details: [] as CreateAssessmentDetailRequest[],
})

const formErrors = ref<Record<string, string>>({})
const detailErrors = ref<Record<number, Record<string, string>>>({})

// Assessment type options
const assessmentTypeOptions = [
  { value: AssessmentType.QUIZ, label: 'Quiz' },
  { value: AssessmentType.ASSIGNMENT, label: 'Assignment' },
  { value: AssessmentType.PROJECT, label: 'Project' },
  { value: AssessmentType.EXAM, label: 'Exam' },
  { value: AssessmentType.ONLINE_ACTIVITY, label: 'Online Activity' },
  { value: AssessmentType.PRESENTATION, label: 'Presentation' },
  { value: AssessmentType.PARTICIPATION, label: 'Participation' },
  { value: AssessmentType.OTHER, label: 'Other' },
]

const submissionTypeOptions = [
  { value: SubmissionType.ONLINE, label: 'Online Submission' },
  { value: SubmissionType.PHYSICAL, label: 'Physical Submission' },
  { value: SubmissionType.BOTH, label: 'Both Online and Physical' },
  { value: SubmissionType.NONE, label: 'No Submission Required' },
]

// Computed properties
const dialogTitle = computed(() => (props.mode === 'create' ? 'Create Assessment' : 'Edit Assessment'))

const totalDetailsWeight = computed(() => formData.value.details.reduce((sum, detail) => sum + detail.weight, 0))

const isDetailsWeightValid = computed(() => formData.value.details.length === 0 || totalDetailsWeight.value === 100)

const weightValidationResult = computed(() => {
  if (props.assessment) {
    return validateWeightChange(props.assessment.id, formData.value.weight)
  } else {
    // For new assessments, simulate validation
    const currentTotal = assessments.value.reduce((sum, a) => sum + a.weight, 0)
    const projectedTotal = currentTotal + formData.value.weight

    if (formData.value.weight <= 0) {
      return { isValid: false, message: 'Weight must be greater than 0', severity: 'high' as const }
    }

    if (projectedTotal > 100) {
      return {
        isValid: false,
        message: `This would exceed total weight by ${(projectedTotal - 100).toFixed(1)}%`,
        severity: 'high' as const,
      }
    }

    return {
      isValid: true,
      message: `${(100 - projectedTotal).toFixed(1)}% will remain after adding this assessment`,
      severity: 'low' as const,
    }
  }
})

const componentWeightValidation = computed(() => {
  if (formData.value.details.length === 0) {
    return { isValid: true, message: 'No components defined' }
  }

  const weights = formData.value.details.map((d) => d.weight)
  return validateComponentWeights(props.assessment?.id || 0, weights)
})

const isProjectedWeightValid = computed(() => weightValidationResult.value.isValid)

const canSubmit = computed(() => {
  return (
    !loading.value &&
    Object.keys(formErrors.value).length === 0 &&
    Object.keys(detailErrors.value).length === 0 &&
    componentWeightValidation.value.isValid &&
    weightValidationResult.value.isValid &&
    formData.value.name.trim() !== '' &&
    formData.value.weight > 0
  )
})

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    type: AssessmentType.ASSIGNMENT,
    weight: 0,
    max_points: 100,
    due_date: '',
    is_required: true,
    description: '',
    instructions: '',
    submission_type: SubmissionType.ONLINE,
    late_penalty_per_day: 0,
    max_late_days: 0,
    allow_resubmission: false,
    details: [],
  }
  formErrors.value = {}
  detailErrors.value = {}
}

const populateForm = (assessment: Assessment) => {
  formData.value = {
    name: assessment.name,
    type: assessment.type,
    weight: assessment.weight,
    max_points: assessment.max_points,
    due_date: assessment.due_date ? assessment.due_date.split('T')[0] : '',
    is_required: assessment.is_required,
    description: assessment.description || '',
    instructions: assessment.instructions || '',
    submission_type: assessment.submission_type,
    late_penalty_per_day: assessment.late_penalty_per_day || 0,
    max_late_days: assessment.max_late_days || 0,
    allow_resubmission: assessment.allow_resubmission,
    details: assessment.details.map((detail, index) => ({
      name: detail.name,
      weight: detail.weight,
      max_points: detail.max_points,
      description: detail.description || '',
      order_index: index,
    })),
  }
}

const validateForm = () => {
  const errors: Record<string, string> = {}

  if (!formData.value.name.trim()) {
    errors.name = 'Assessment name is required'
  }

  if (formData.value.weight <= 0) {
    errors.weight = 'Weight must be greater than 0'
  }

  if (formData.value.weight > 100) {
    errors.weight = 'Weight cannot exceed 100%'
  }

  if (formData.value.max_points <= 0) {
    errors.max_points = 'Maximum points must be greater than 0'
  }

  if (formData.value.late_penalty_per_day < 0) {
    errors.late_penalty_per_day = 'Late penalty cannot be negative'
  }

  if (formData.value.max_late_days < 0) {
    errors.max_late_days = 'Maximum late days cannot be negative'
  }

  formErrors.value = errors
  return Object.keys(errors).length === 0
}

const validateDetails = () => {
  const errors: Record<number, Record<string, string>> = {}

  formData.value.details.forEach((detail, index) => {
    const detailErrors: Record<string, string> = {}

    if (!detail.name.trim()) {
      detailErrors.name = 'Component name is required'
    }

    if (detail.weight <= 0) {
      detailErrors.weight = 'Weight must be greater than 0'
    }

    if (detail.max_points <= 0) {
      detailErrors.max_points = 'Maximum points must be greater than 0'
    }

    if (Object.keys(detailErrors).length > 0) {
      errors[index] = detailErrors
    }
  })

  detailErrors.value = errors
  return Object.keys(errors).length === 0
}

const addDetail = () => {
  const newDetail: CreateAssessmentDetailRequest = {
    name: '',
    weight: 0,
    max_points: 100,
    description: '',
    order_index: formData.value.details.length,
  }
  formData.value.details.push(newDetail)

  // Focus on the new detail name input
  nextTick(() => {
    const inputs = document.querySelectorAll(`[data-detail-name="${formData.value.details.length - 1}"]`)
    if (inputs.length > 0) {
      ;(inputs[0] as HTMLInputElement).focus()
    }
  })
}

const removeDetail = (index: number) => {
  formData.value.details.splice(index, 1)
  // Update order indices
  formData.value.details.forEach((detail, i) => {
    detail.order_index = i
  })
  // Remove any errors for this detail
  delete detailErrors.value[index]
}

const moveDetail = (fromIndex: number, toIndex: number) => {
  const detail = formData.value.details.splice(fromIndex, 1)[0]
  formData.value.details.splice(toIndex, 0, detail)
  // Update order indices
  formData.value.details.forEach((detail, i) => {
    detail.order_index = i
  })
}

const handleSubmit = async () => {
  if (!validateForm() || !validateDetails()) {
    return
  }

  try {
    if (props.mode === 'create') {
      const createData: CreateAssessmentRequest = {
        course_offering_id: parseInt(props.courseId),
        ...formData.value,
        due_date: formData.value.due_date || undefined,
      }
      const assessment = await assessmentStore.createAssessment(createData)
      emit('success', assessment)
    } else if (props.assessment) {
      const updateData: UpdateAssessmentRequest = {
        id: props.assessment.id,
        ...formData.value,
        due_date: formData.value.due_date || undefined,
      }
      const assessment = await assessmentStore.updateAssessment(updateData)
      emit('success', assessment)
    }

    handleClose()
  } catch (error) {
    emit('error', error instanceof Error ? error.message : 'Failed to save assessment')
  }
}

const handleClose = () => {
  emit('update:open', false)
}

// Watchers
watch(
  () => props.open,
  (isOpen) => {
    if (isOpen) {
      if (props.mode === 'edit' && props.assessment) {
        populateForm(props.assessment)
      } else {
        resetForm()
      }
    }
  },
)

watch(
  () => formData.value,
  () => {
    validateForm()
    validateDetails()
  },
  { deep: true },
)
</script>

<template>
  <Dialog :open="open" @update:open="handleClose">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>{{ dialogTitle }}</DialogTitle>
        <DialogDescription>
          {{
            mode === 'create'
              ? 'Create a new assessment component for this course'
              : 'Edit the assessment component details'
          }}
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="name">Assessment Name *</Label>
            <Input
              id="name"
              v-model="formData.name"
              placeholder="e.g., Assignment 1, Quiz 2, Final Exam"
              :class="{ 'border-red-500': formErrors.name }"
            />
            <div v-if="formErrors.name" class="text-sm text-red-600">
              {{ formErrors.name }}
            </div>
          </div>

          <div class="space-y-2">
            <Label for="type">Assessment Type *</Label>
            <Select v-model="formData.type">
              <SelectTrigger>
                <SelectValue placeholder="Select assessment type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in assessmentTypeOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Weight and Points -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label for="weight">Weight (%) *</Label>
            <Input
              id="weight"
              v-model.number="formData.weight"
              type="number"
              min="0"
              max="100"
              step="0.1"
              :class="{ 'border-red-500': formErrors.weight }"
            />
            <div v-if="formErrors.weight" class="text-sm text-red-600">
              {{ formErrors.weight }}
            </div>
          </div>

          <div class="space-y-2">
            <Label for="max_points">Maximum Points *</Label>
            <Input
              id="max_points"
              v-model.number="formData.max_points"
              type="number"
              min="1"
              step="1"
              :class="{ 'border-red-500': formErrors.max_points }"
            />
            <div v-if="formErrors.max_points" class="text-sm text-red-600">
              {{ formErrors.max_points }}
            </div>
          </div>

          <div class="space-y-2">
            <Label for="due_date">Due Date</Label>
            <Input id="due_date" v-model="formData.due_date" type="date" />
          </div>
        </div>

        <!-- Weight Validation Alerts -->
        <Alert
          v-if="!weightValidationResult.isValid"
          :variant="weightValidationResult.severity === 'high' ? 'destructive' : 'default'"
        >
          <AlertTriangle class="h-4 w-4" />
          <AlertDescription>
            {{ weightValidationResult.message }}
          </AlertDescription>
        </Alert>

        <Alert v-else-if="weightValidationResult.message && formData.weight > 0" variant="default">
          <Info class="h-4 w-4" />
          <AlertDescription>
            {{ weightValidationResult.message }}
          </AlertDescription>
        </Alert>

        <!-- Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="submission_type">Submission Type</Label>
            <Select v-model="formData.submission_type">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in submissionTypeOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-4">
            <div class="flex items-center space-x-2">
              <Checkbox id="is_required" v-model:checked="formData.is_required" />
              <Label for="is_required">Required Assessment</Label>
            </div>
            <div class="flex items-center space-x-2">
              <Checkbox id="allow_resubmission" v-model:checked="formData.allow_resubmission" />
              <Label for="allow_resubmission">Allow Resubmission</Label>
            </div>
          </div>
        </div>

        <!-- Late Submission Settings -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="late_penalty_per_day">Late Penalty (% per day)</Label>
            <Input
              id="late_penalty_per_day"
              v-model.number="formData.late_penalty_per_day"
              type="number"
              min="0"
              max="100"
              step="0.1"
              :class="{ 'border-red-500': formErrors.late_penalty_per_day }"
            />
            <div v-if="formErrors.late_penalty_per_day" class="text-sm text-red-600">
              {{ formErrors.late_penalty_per_day }}
            </div>
          </div>

          <div class="space-y-2">
            <Label for="max_late_days">Maximum Late Days</Label>
            <Input
              id="max_late_days"
              v-model.number="formData.max_late_days"
              type="number"
              min="0"
              step="1"
              :class="{ 'border-red-500': formErrors.max_late_days }"
            />
            <div v-if="formErrors.max_late_days" class="text-sm text-red-600">
              {{ formErrors.max_late_days }}
            </div>
          </div>
        </div>

        <!-- Description and Instructions -->
        <div class="space-y-4">
          <div class="space-y-2">
            <Label for="description">Description</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="Brief description of the assessment"
              rows="3"
            />
          </div>

          <div class="space-y-2">
            <Label for="instructions">Instructions</Label>
            <Textarea
              id="instructions"
              v-model="formData.instructions"
              placeholder="Detailed instructions for students"
              rows="4"
            />
          </div>
        </div>

        <!-- Assessment Components -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>Assessment Components</CardTitle>
              <Button type="button" variant="outline" size="sm" @click="addDetail">
                <Plus class="h-4 w-4 mr-2" />
                Add Component
              </Button>
            </div>
            <div class="text-sm text-muted-foreground">
              Break down this assessment into smaller components (optional)
            </div>
          </CardHeader>
          <CardContent>
            <div v-if="formData.details.length === 0" class="text-center py-8 text-muted-foreground">
              No components added. This assessment will be graded as a single component.
            </div>

            <div v-else class="space-y-4">
              <!-- Weight Summary -->
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm font-medium">Total Component Weight:</span>
                <span
                  class="text-sm font-bold"
                  :class="componentWeightValidation.isValid ? 'text-green-600' : 'text-red-600'"
                >
                  {{ totalDetailsWeight }}%
                </span>
              </div>

              <!-- Component Weight Validation Alert -->
              <Alert v-if="!componentWeightValidation.isValid" variant="destructive">
                <AlertTriangle class="h-4 w-4" />
                <AlertDescription>
                  {{ componentWeightValidation.message }}
                </AlertDescription>
              </Alert>

              <Alert v-else-if="componentWeightValidation.message && formData.details.length > 0" variant="default">
                <Info class="h-4 w-4" />
                <AlertDescription>
                  {{ componentWeightValidation.message }}
                </AlertDescription>
              </Alert>

              <!-- Component List -->
              <div class="space-y-3">
                <div v-for="(detail, index) in formData.details" :key="index" class="border rounded-lg p-4 space-y-3">
                  <div class="flex items-center gap-2">
                    <GripVertical class="h-4 w-4 text-muted-foreground cursor-move" />
                    <span class="text-sm font-medium">Component {{ index + 1 }}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      @click="removeDetail(index)"
                      class="ml-auto text-red-600 hover:text-red-700"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div class="space-y-1">
                      <Label :for="`detail-name-${index}`">Name *</Label>
                      <Input
                        :id="`detail-name-${index}`"
                        :data-detail-name="index"
                        v-model="detail.name"
                        placeholder="Component name"
                        :class="{ 'border-red-500': detailErrors[index]?.name }"
                      />
                      <div v-if="detailErrors[index]?.name" class="text-xs text-red-600">
                        {{ detailErrors[index].name }}
                      </div>
                    </div>

                    <div class="space-y-1">
                      <Label :for="`detail-weight-${index}`">Weight (%) *</Label>
                      <Input
                        :id="`detail-weight-${index}`"
                        v-model.number="detail.weight"
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        :class="{ 'border-red-500': detailErrors[index]?.weight }"
                      />
                      <div v-if="detailErrors[index]?.weight" class="text-xs text-red-600">
                        {{ detailErrors[index].weight }}
                      </div>
                    </div>

                    <div class="space-y-1">
                      <Label :for="`detail-points-${index}`">Max Points *</Label>
                      <Input
                        :id="`detail-points-${index}`"
                        v-model.number="detail.max_points"
                        type="number"
                        min="1"
                        step="1"
                        :class="{ 'border-red-500': detailErrors[index]?.max_points }"
                      />
                      <div v-if="detailErrors[index]?.max_points" class="text-xs text-red-600">
                        {{ detailErrors[index].max_points }}
                      </div>
                    </div>
                  </div>

                  <div class="space-y-1">
                    <Label :for="`detail-description-${index}`">Description</Label>
                    <Textarea
                      :id="`detail-description-${index}`"
                      v-model="detail.description"
                      placeholder="Component description"
                      rows="2"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>

      <DialogFooter>
        <Button type="button" variant="outline" @click="handleClose">Cancel</Button>
        <Button type="button" @click="handleSubmit" :disabled="!canSubmit" class="flex items-center gap-2">
          <LoadingSpinner v-if="loading" size="sm" />
          {{ mode === 'create' ? 'Create Assessment' : 'Update Assessment' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
