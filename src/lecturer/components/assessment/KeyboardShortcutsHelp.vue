<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON><PERSON> } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/shared/components/ui/dialog'
import { Separator } from '@/shared/components/ui/separator'
import { ScrollArea } from '@/shared/components/ui/scroll-area'
import { Keyboard, X, Info } from 'lucide-vue-next'
import type { ShortcutCategory } from '@/lecturer/composables/useKeyboardShortcuts'

interface Props {
  open: boolean
  categories: ShortcutCategory[]
  formatShortcutKey: (shortcut: any) => string
}

interface Emits {
  (e: 'update:open', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Group shortcuts by category with proper ordering
const orderedCategories = computed(() => {
  const categoryOrder = ['General', 'Navigation', 'Grading', 'View Modes', 'Bulk Operations', 'Search', 'Help']

  return categoryOrder
    .map((categoryName) => props.categories.find((cat) => cat.name === categoryName))
    .filter(Boolean)
    .concat(props.categories.filter((cat) => !categoryOrder.includes(cat.name)))
})

const totalShortcuts = computed(() => {
  return props.categories.reduce((total, category) => total + category.shortcuts.length, 0)
})

const handleClose = () => {
  emit('update:open', false)
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleClose()
  }
}
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-hidden" @keydown="handleKeyDown">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Keyboard class="h-5 w-5" />
          Keyboard Shortcuts
        </DialogTitle>
        <DialogDescription>
          Use these keyboard shortcuts to navigate and interact with the grading interface more efficiently.
          <Badge variant="outline" class="ml-2">{{ totalShortcuts }} shortcuts available</Badge>
        </DialogDescription>
      </DialogHeader>

      <ScrollArea class="max-h-[70vh] pr-4">
        <div class="space-y-6">
          <!-- Quick Tips -->
          <Card class="border-blue-200 bg-blue-50">
            <CardHeader class="pb-3">
              <CardTitle class="text-sm flex items-center gap-2 text-blue-800">
                <Info class="h-4 w-4" />
                Quick Tips
              </CardTitle>
            </CardHeader>
            <CardContent class="pt-0">
              <ul class="text-sm text-blue-700 space-y-1">
                <li>• Most shortcuts work globally throughout the grading interface</li>
                <li>• Use Tab and Shift+Tab to navigate between form fields</li>
                <li>• Press Enter to save the current grade and move to the next field</li>
                <li>• Press Escape to cancel editing and revert changes</li>
                <li>• Shortcuts are disabled when typing in text fields (except Ctrl/Cmd combinations)</li>
              </ul>
            </CardContent>
          </Card>

          <!-- Shortcut Categories -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card v-for="category in orderedCategories" :key="category.name" class="h-fit">
              <CardHeader class="pb-3">
                <CardTitle class="text-base">{{ category.name }}</CardTitle>
              </CardHeader>
              <CardContent class="pt-0">
                <div class="space-y-3">
                  <div
                    v-for="shortcut in category.shortcuts"
                    :key="`${shortcut.key}-${shortcut.category}`"
                    class="flex items-center justify-between gap-4"
                  >
                    <div class="flex-1">
                      <p class="text-sm font-medium">{{ shortcut.description }}</p>
                    </div>
                    <Badge variant="outline" class="font-mono text-xs whitespace-nowrap">
                      {{ formatShortcutKey(shortcut) }}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Special Key Combinations -->
          <Card>
            <CardHeader class="pb-3">
              <CardTitle class="text-base">Special Key Combinations</CardTitle>
            </CardHeader>
            <CardContent class="pt-0">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <h4 class="text-sm font-medium text-muted-foreground">Table Navigation</h4>
                  <div class="space-y-2 text-sm">
                    <div class="flex items-center justify-between">
                      <span>Move to next cell</span>
                      <Badge variant="outline" class="font-mono text-xs">Tab</Badge>
                    </div>
                    <div class="flex items-center justify-between">
                      <span>Move to previous cell</span>
                      <Badge variant="outline" class="font-mono text-xs">Shift + Tab</Badge>
                    </div>
                    <div class="flex items-center justify-between">
                      <span>Move down one row</span>
                      <Badge variant="outline" class="font-mono text-xs">Enter</Badge>
                    </div>
                    <div class="flex items-center justify-between">
                      <span>Cancel edit</span>
                      <Badge variant="outline" class="font-mono text-xs">Esc</Badge>
                    </div>
                  </div>
                </div>

                <div class="space-y-2">
                  <h4 class="text-sm font-medium text-muted-foreground">Selection</h4>
                  <div class="space-y-2 text-sm">
                    <div class="flex items-center justify-between">
                      <span>Select all visible rows</span>
                      <Badge variant="outline" class="font-mono text-xs">Ctrl + A</Badge>
                    </div>
                    <div class="flex items-center justify-between">
                      <span>Toggle row selection</span>
                      <Badge variant="outline" class="font-mono text-xs">Space</Badge>
                    </div>
                    <div class="flex items-center justify-between">
                      <span>Clear selection</span>
                      <Badge variant="outline" class="font-mono text-xs">Ctrl + D</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Accessibility Features -->
          <Card class="border-green-200 bg-green-50">
            <CardHeader class="pb-3">
              <CardTitle class="text-sm text-green-800">Accessibility Features</CardTitle>
            </CardHeader>
            <CardContent class="pt-0">
              <div class="text-sm text-green-700 space-y-2">
                <p>• All shortcuts are announced to screen readers</p>
                <p>• Focus indicators are clearly visible throughout the interface</p>
                <p>• Skip links are available for efficient navigation</p>
                <p>• All interactive elements have proper ARIA labels</p>
                <p>• High contrast mode is supported for better visibility</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>

      <div class="flex justify-end pt-4 border-t">
        <Button variant="outline" @click="handleClose">
          <X class="h-4 w-4 mr-2" />
          Close
        </Button>
      </div>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
/* Ensure proper focus indicators */
button:focus,
[role='button']:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .text-muted-foreground {
    color: inherit;
    opacity: 0.8;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
