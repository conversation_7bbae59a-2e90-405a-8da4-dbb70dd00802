<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Progress } from '@/shared/components/ui/progress'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/shared/components/ui/tooltip'
import { useWeightValidation, type ValidationMessage } from '@/lecturer/composables/useWeightValidation'
import { AlertTriangle, CheckCircle, Clock, Info, Circle, HelpCircle } from 'lucide-vue-next'
import type { Assessment } from '@/lecturer/types/models/assessment'

interface Props {
  assessments: Assessment[]
  showDetails?: boolean
  showSuggestions?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: true,
  showSuggestions: true,
  compact: false,
})

const {
  totalWeight,
  isWeightValid,
  weightExceeded,
  weightRemaining,
  weightPercentage,
  validationStatus,
  statusColor,
  statusIcon,
  progressBarColor,
  componentValidations,
  hasComponentErrors,
  validationMessages,
  getWeightSuggestions,
} = useWeightValidation(computed(() => props.assessments))

// Icon mapping
const iconComponents = {
  AlertTriangle,
  CheckCircle,
  Clock,
  Info,
  Circle,
}

const statusMessages = computed(() => ({
  exceeded: `Exceeds limit by ${weightExceeded.value.toFixed(1)}%`,
  complete: 'Weight allocation complete',
  warning: `${weightRemaining.value.toFixed(1)}% remaining`,
  incomplete: `${weightRemaining.value.toFixed(1)}% remaining`,
  empty: 'No assessments added',
}))

const suggestions = computed(() => getWeightSuggestions())

const errorMessages = computed(() => validationMessages.value.filter((msg) => msg.type === 'error'))

const warningMessages = computed(() => validationMessages.value.filter((msg) => msg.type === 'warning'))

const getMessageIcon = (type: ValidationMessage['type']) => {
  switch (type) {
    case 'error':
      return AlertTriangle
    case 'warning':
      return Clock
    case 'success':
      return CheckCircle
    case 'info':
      return Info
    default:
      return Info
  }
}

const getMessageColor = (type: ValidationMessage['type']) => {
  switch (type) {
    case 'error':
      return 'text-red-600'
    case 'warning':
      return 'text-yellow-600'
    case 'success':
      return 'text-green-600'
    case 'info':
      return 'text-blue-600'
    default:
      return 'text-gray-600'
  }
}
</script>

<template>
  <div class="space-y-4">
    <!-- Compact View -->
    <div v-if="compact" class="flex items-center gap-3 p-3 border rounded-lg">
      <component :is="iconComponents[statusIcon]" class="h-5 w-5" :class="statusColor" />
      <div class="flex-1">
        <div class="flex items-center justify-between">
          <span class="font-medium">{{ totalWeight.toFixed(1) }}%</span>
          <span class="text-sm text-muted-foreground">
            {{ statusMessages[validationStatus] }}
          </span>
        </div>
        <div class="mt-1">
          <Progress :value="Math.min(weightPercentage, 100)" class="h-2" />
        </div>
      </div>
    </div>

    <!-- Full View -->
    <Card v-else>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <component :is="iconComponents[statusIcon]" class="h-5 w-5" :class="statusColor" />
          Assessment Weight Summary
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle class="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Total assessment weights should equal 100% for proper grade calculation</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- Weight Display -->
        <div class="flex items-center justify-between">
          <div class="space-y-1">
            <div class="text-3xl font-bold" :class="statusColor">{{ totalWeight.toFixed(1) }}%</div>
            <div class="text-sm text-muted-foreground">
              {{ assessments.length }} assessment{{ assessments.length !== 1 ? 's' : '' }}
            </div>
          </div>
          <div class="text-right space-y-1">
            <Badge
              :variant="
                validationStatus === 'complete'
                  ? 'default'
                  : validationStatus === 'exceeded'
                    ? 'destructive'
                    : 'secondary'
              "
            >
              {{ statusMessages[validationStatus] }}
            </Badge>
            <div v-if="hasComponentErrors" class="text-sm text-red-600">Component errors detected</div>
          </div>
        </div>

        <!-- Progress Bar -->
        <div class="space-y-2">
          <Progress :value="Math.min(weightPercentage, 100)" class="h-3" :class="progressBarColor" />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>

        <!-- Validation Messages -->
        <div v-if="validationMessages.length > 0" class="space-y-2">
          <div v-for="message in validationMessages" :key="message.code">
            <Alert
              :variant="message.type === 'error' ? 'destructive' : message.type === 'warning' ? 'default' : 'default'"
            >
              <component :is="getMessageIcon(message.type)" class="h-4 w-4" />
              <AlertDescription :class="getMessageColor(message.type)">
                {{ message.message }}
              </AlertDescription>
            </Alert>
          </div>
        </div>

        <!-- Component Validation Details -->
        <div v-if="showDetails && componentValidations.length > 0" class="space-y-3">
          <h4 class="text-sm font-medium">Assessment Details</h4>
          <div class="space-y-2">
            <div
              v-for="validation in componentValidations"
              :key="validation.assessment_id"
              class="flex items-center justify-between p-2 border rounded"
              :class="validation.is_valid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'"
            >
              <div class="flex items-center gap-2">
                <component
                  :is="validation.is_valid ? CheckCircle : AlertTriangle"
                  class="h-4 w-4"
                  :class="validation.is_valid ? 'text-green-600' : 'text-red-600'"
                />
                <span class="text-sm font-medium">{{ validation.assessment_name }}</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium">{{ validation.current_weight }}%</div>
                <div v-if="validation.detail_weights_sum > 0" class="text-xs text-muted-foreground">
                  Components: {{ validation.detail_weights_sum }}%
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Suggestions -->
        <div v-if="showSuggestions && suggestions.length > 0" class="space-y-2">
          <h4 class="text-sm font-medium flex items-center gap-2">
            <Info class="h-4 w-4 text-blue-600" />
            Suggestions
          </h4>
          <ul class="space-y-1">
            <li
              v-for="(suggestion, index) in suggestions"
              :key="index"
              class="text-sm text-muted-foreground flex items-start gap-2"
            >
              <span class="text-blue-600 mt-1">•</span>
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
