<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { But<PERSON> } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Textarea } from '@/shared/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Separator } from '@/shared/components/ui/separator'
import { Checkbox } from '@/shared/components/ui/checkbox'
import {
  Plus,
  Minus,
  Calculator,
  Eye,
  <PERSON>Off,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  XCircle,
  Info,
  Star,
  Gift,
  Ban,
  Undo,
  History,
  TrendingUp,
  TrendingDown,
} from 'lucide-vue-next'
import type { GradeEntry } from '@/lecturer/types/models/assessment'

interface Props {
  gradeEntry: GradeEntry
  maxPoints: number
  allowNegativeAdjustments?: boolean
}

interface ScoreAdjustment {
  id?: number
  type: 'bonus' | 'penalty' | 'correction'
  amount: number
  reason: string
  description?: string
  appliedBy: string
  appliedAt: string
  category?: 'participation' | 'improvement' | 'extra_credit' | 'technical_issue' | 'grading_error' | 'other'
}

interface ExclusionReason {
  code: string
  label: string
  description: string
  requiresApproval: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allowNegativeAdjustments: true,
})

const emit = defineEmits<{
  'update:grade': [grade: Partial<GradeEntry>]
  'apply-adjustment': [adjustment: ScoreAdjustment]
  'exclude-score': [reason: string, description?: string]
  'include-score': []
}>()

// Local state
const showAdjustmentDialog = ref(false)
const showExclusionDialog = ref(false)
const showHistoryDialog = ref(false)
const showConfirmDialog = ref(false)
const confirmAction = ref<'exclude' | 'include' | null>(null)

// Form state
const adjustmentForm = ref({
  type: 'bonus' as ScoreAdjustment['type'],
  amount: 0,
  reason: '',
  description: '',
  category: 'other' as ScoreAdjustment['category'],
})

const exclusionForm = ref({
  reason: '',
  description: '',
  requiresApproval: false,
})

// Mock adjustment history - in real app this would come from props or API
const adjustmentHistory = ref<ScoreAdjustment[]>([])

// Predefined exclusion reasons
const exclusionReasons: ExclusionReason[] = [
  {
    code: 'medical',
    label: 'Medical Circumstances',
    description: 'Student was unable to complete due to medical reasons',
    requiresApproval: false,
  },
  {
    code: 'technical',
    label: 'Technical Issues',
    description: 'Technical problems prevented proper submission or completion',
    requiresApproval: false,
  },
  {
    code: 'academic_accommodation',
    label: 'Academic Accommodation',
    description: 'Excluded as part of approved academic accommodation',
    requiresApproval: true,
  },
  {
    code: 'late_enrollment',
    label: 'Late Enrollment',
    description: 'Student enrolled after assessment was due',
    requiresApproval: false,
  },
  {
    code: 'administrative',
    label: 'Administrative Decision',
    description: 'Excluded by administrative decision',
    requiresApproval: true,
  },
  {
    code: 'other',
    label: 'Other',
    description: 'Other circumstances requiring exclusion',
    requiresApproval: false,
  },
]

const adjustmentCategories = {
  participation: { label: 'Participation', icon: Star, color: 'text-blue-600' },
  improvement: { label: 'Improvement', icon: TrendingUp, color: 'text-green-600' },
  extra_credit: { label: 'Extra Credit', icon: Gift, color: 'text-purple-600' },
  technical_issue: { label: 'Technical Issue', icon: AlertTriangle, color: 'text-yellow-600' },
  grading_error: { label: 'Grading Error', icon: Undo, color: 'text-red-600' },
  other: { label: 'Other', icon: Info, color: 'text-gray-600' },
}

// Computed properties
const originalScore = computed(() => {
  // Calculate original score before adjustments
  const currentScore = props.gradeEntry.points_earned || 0
  const bonusPoints = props.gradeEntry.bonus_points || 0
  return currentScore - bonusPoints
})

const totalAdjustments = computed(() => {
  return adjustmentHistory.value.reduce((total, adj) => {
    return total + (adj.type === 'bonus' ? adj.amount : -adj.amount)
  }, 0)
})

const adjustedScore = computed(() => {
  const base = originalScore.value
  const adjustments = totalAdjustments.value
  const currentBonus = props.gradeEntry.bonus_points || 0
  return Math.max(0, base + adjustments + currentBonus)
})

const adjustedPercentage = computed(() => {
  if (props.maxPoints === 0) return 0
  return Math.round((adjustedScore.value / props.maxPoints) * 100 * 100) / 100
})

const hasAdjustments = computed(() => {
  return (props.gradeEntry.bonus_points && props.gradeEntry.bonus_points !== 0) || adjustmentHistory.value.length > 0
})

const isExcluded = computed(() => props.gradeEntry.is_excluded)

const exclusionReasonData = computed(() => {
  if (!props.gradeEntry.exclusion_reason) return null
  return (
    exclusionReasons.find((r) => r.code === props.gradeEntry.exclusion_reason) || {
      code: 'other',
      label: 'Other',
      description: props.gradeEntry.exclusion_reason,
    }
  )
})

const scoreChangeIndicator = computed(() => {
  const original = originalScore.value
  const adjusted = adjustedScore.value

  if (adjusted > original) {
    return { icon: TrendingUp, color: 'text-green-600', text: `+${adjusted - original}` }
  } else if (adjusted < original) {
    return { icon: TrendingDown, color: 'text-red-600', text: `${adjusted - original}` }
  }
  return null
})

// Methods
const applyAdjustment = () => {
  if (!adjustmentForm.value.reason.trim() || adjustmentForm.value.amount === 0) return

  const adjustment: ScoreAdjustment = {
    id: Date.now(),
    type: adjustmentForm.value.type,
    amount: Math.abs(adjustmentForm.value.amount),
    reason: adjustmentForm.value.reason,
    description: adjustmentForm.value.description,
    category: adjustmentForm.value.category,
    appliedBy: 'Current User', // This should come from auth
    appliedAt: new Date().toISOString(),
  }

  adjustmentHistory.value.push(adjustment)

  // Update grade entry
  const newBonusPoints =
    (props.gradeEntry.bonus_points || 0) + (adjustment.type === 'bonus' ? adjustment.amount : -adjustment.amount)

  emit('update:grade', {
    bonus_points: newBonusPoints,
    bonus_reason: adjustment.reason,
    points_earned: adjustedScore.value,
    percentage_score: adjustedPercentage.value,
  })

  emit('apply-adjustment', adjustment)

  showAdjustmentDialog.value = false
  resetAdjustmentForm()
}

const removeAdjustment = (adjustmentId: number) => {
  const adjustmentIndex = adjustmentHistory.value.findIndex((a) => a.id === adjustmentId)
  if (adjustmentIndex === -1) return

  const adjustment = adjustmentHistory.value[adjustmentIndex]
  adjustmentHistory.value.splice(adjustmentIndex, 1)

  // Recalculate bonus points
  const newBonusPoints =
    (props.gradeEntry.bonus_points || 0) - (adjustment.type === 'bonus' ? adjustment.amount : -adjustment.amount)

  emit('update:grade', {
    bonus_points: newBonusPoints,
    points_earned: adjustedScore.value,
    percentage_score: adjustedPercentage.value,
  })
}

const excludeScore = () => {
  if (!exclusionForm.value.reason) return

  const selectedReason = exclusionReasons.find((r) => r.code === exclusionForm.value.reason)

  emit('update:grade', {
    is_excluded: true,
    exclusion_reason: exclusionForm.value.reason,
    points_earned: 0,
    percentage_score: 0,
  })

  emit('exclude-score', exclusionForm.value.reason, exclusionForm.value.description)

  showExclusionDialog.value = false
  showConfirmDialog.value = false
  resetExclusionForm()
}

const includeScore = () => {
  emit('update:grade', {
    is_excluded: false,
    exclusion_reason: undefined,
  })

  emit('include-score')
  showConfirmDialog.value = false
}

const handleConfirmAction = () => {
  if (confirmAction.value === 'exclude') {
    excludeScore()
  } else if (confirmAction.value === 'include') {
    includeScore()
  }
}

const openExclusionDialog = () => {
  confirmAction.value = 'exclude'
  showExclusionDialog.value = true
}

const openInclusionConfirm = () => {
  confirmAction.value = 'include'
  showConfirmDialog.value = true
}

const resetAdjustmentForm = () => {
  adjustmentForm.value = {
    type: 'bonus',
    amount: 0,
    reason: '',
    description: '',
    category: 'other',
  }
}

const resetExclusionForm = () => {
  exclusionForm.value = {
    reason: '',
    description: '',
    requiresApproval: false,
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Watch for exclusion reason changes
watch(
  () => exclusionForm.value.reason,
  (newReason) => {
    const reasonData = exclusionReasons.find((r) => r.code === newReason)
    if (reasonData) {
      exclusionForm.value.requiresApproval = reasonData.requiresApproval
    }
  },
)
</script>

<template>
  <Card
    class="border-l-4"
    :class="{
      'border-l-red-500': isExcluded,
      'border-l-green-500': hasAdjustments && !isExcluded,
      'border-l-gray-300': !hasAdjustments && !isExcluded,
    }"
  >
    <CardHeader class="pb-3">
      <div class="flex items-center justify-between">
        <CardTitle class="text-lg flex items-center gap-2">
          <Calculator class="h-5 w-5" />
          Score Adjustments
        </CardTitle>

        <div class="flex items-center gap-2">
          <Badge v-if="isExcluded" variant="destructive" class="gap-1">
            <EyeOff class="h-3 w-3" />
            Excluded
          </Badge>
          <Badge v-else-if="hasAdjustments" variant="default" class="gap-1">
            <component :is="scoreChangeIndicator?.icon || Plus" class="h-3 w-3" />
            Adjusted
          </Badge>
          <Badge v-else variant="outline" class="gap-1">
            <Eye class="h-3 w-3" />
            Included
          </Badge>
        </div>
      </div>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Score Overview -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
        <div class="space-y-1">
          <Label class="text-sm font-medium text-muted-foreground">Original Score</Label>
          <p class="text-lg font-semibold">
            {{ originalScore }}/{{ maxPoints }}
            <span class="text-sm text-muted-foreground ml-1">
              ({{ Math.round((originalScore / maxPoints) * 100) }}%)
            </span>
          </p>
        </div>

        <div class="space-y-1">
          <Label class="text-sm font-medium text-muted-foreground">Adjustments</Label>
          <p
            class="text-lg font-semibold"
            :class="{
              'text-green-600': totalAdjustments > 0,
              'text-red-600': totalAdjustments < 0,
              'text-gray-600': totalAdjustments === 0,
            }"
          >
            {{ totalAdjustments > 0 ? '+' : '' }}{{ totalAdjustments }}
            <span v-if="scoreChangeIndicator" class="ml-2">
              <component :is="scoreChangeIndicator.icon" class="h-4 w-4 inline" :class="scoreChangeIndicator.color" />
            </span>
          </p>
        </div>

        <div class="space-y-1">
          <Label class="text-sm font-medium text-muted-foreground">Final Score</Label>
          <p
            class="text-lg font-semibold"
            :class="{
              'text-red-600': isExcluded,
              'text-green-600': adjustedScore > originalScore,
              'text-gray-900': adjustedScore === originalScore,
            }"
          >
            {{ isExcluded ? 'Excluded' : `${adjustedScore}/${maxPoints}` }}
            <span v-if="!isExcluded" class="text-sm text-muted-foreground ml-1">({{ adjustedPercentage }}%)</span>
          </p>
        </div>
      </div>

      <!-- Exclusion Status -->
      <div v-if="isExcluded" class="space-y-3">
        <Separator />

        <div class="p-4 border rounded-lg bg-red-50 border-red-200">
          <div class="flex items-start gap-3">
            <EyeOff class="h-5 w-5 text-red-600 mt-0.5" />
            <div class="flex-1">
              <h4 class="font-medium text-red-800">Score Excluded from Calculations</h4>
              <p class="text-sm text-red-700 mt-1">This score will not be included in grade calculations.</p>

              <div v-if="exclusionReasonData" class="mt-3 space-y-2">
                <div>
                  <span class="text-sm font-medium text-red-800">Reason:</span>
                  <span class="text-sm text-red-700 ml-2">{{ exclusionReasonData.label }}</span>
                </div>
                <div v-if="gradeEntry.exclusion_reason && gradeEntry.exclusion_reason !== exclusionReasonData.code">
                  <span class="text-sm font-medium text-red-800">Details:</span>
                  <p class="text-sm text-red-700 mt-1">{{ gradeEntry.exclusion_reason }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Adjustment History -->
      <div v-if="adjustmentHistory.length > 0 && !isExcluded" class="space-y-3">
        <Separator />

        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <h4 class="font-medium">Applied Adjustments</h4>
            <Button variant="outline" size="sm" @click="showHistoryDialog = true">
              <History class="h-4 w-4 mr-1" />
              View History
            </Button>
          </div>

          <div class="space-y-2">
            <div
              v-for="adjustment in adjustmentHistory.slice(-3)"
              :key="adjustment.id"
              class="flex items-center justify-between p-3 border rounded-lg bg-background"
            >
              <div class="flex items-center gap-3">
                <component
                  :is="adjustmentCategories[adjustment.category || 'other'].icon"
                  class="h-4 w-4"
                  :class="adjustmentCategories[adjustment.category || 'other'].color"
                />
                <div>
                  <p class="text-sm font-medium">{{ adjustment.reason }}</p>
                  <p class="text-xs text-muted-foreground">
                    {{ adjustmentCategories[adjustment.category || 'other'].label }} •
                    {{ formatDate(adjustment.appliedAt) }}
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-2">
                <Badge :variant="adjustment.type === 'bonus' ? 'default' : 'destructive'" class="gap-1">
                  <component :is="adjustment.type === 'bonus' ? Plus : Minus" class="h-3 w-3" />
                  {{ adjustment.amount }}
                </Badge>

                <Button
                  variant="ghost"
                  size="sm"
                  @click="removeAdjustment(adjustment.id!)"
                  class="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <XCircle class="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-2 pt-4">
        <Dialog v-model:open="showAdjustmentDialog" v-if="!isExcluded">
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" class="text-green-600 border-green-600 hover:bg-green-50">
              <Plus class="h-4 w-4 mr-1" />
              Add Adjustment
            </Button>
          </DialogTrigger>

          <DialogContent class="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Apply Score Adjustment</DialogTitle>
              <DialogDescription>Add bonus points, penalties, or corrections to this score.</DialogDescription>
            </DialogHeader>

            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                  <Label for="adjustment-type">Type</Label>
                  <Select v-model="adjustmentForm.type">
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bonus">Bonus Points</SelectItem>
                      <SelectItem value="penalty" v-if="allowNegativeAdjustments">Penalty</SelectItem>
                      <SelectItem value="correction">Correction</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div class="space-y-2">
                  <Label for="adjustment-amount">Amount</Label>
                  <Input
                    id="adjustment-amount"
                    type="number"
                    min="0"
                    step="0.5"
                    v-model.number="adjustmentForm.amount"
                    placeholder="0"
                  />
                </div>
              </div>

              <div class="space-y-2">
                <Label for="adjustment-category">Category</Label>
                <Select v-model="adjustmentForm.category">
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="(config, key) in adjustmentCategories" :key="key" :value="key">
                      {{ config.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div class="space-y-2">
                <Label for="adjustment-reason">Reason (Required)</Label>
                <Textarea
                  id="adjustment-reason"
                  v-model="adjustmentForm.reason"
                  placeholder="Explain why this adjustment is being applied..."
                  rows="3"
                />
              </div>

              <div class="space-y-2">
                <Label for="adjustment-description">Additional Details (Optional)</Label>
                <Textarea
                  id="adjustment-description"
                  v-model="adjustmentForm.description"
                  placeholder="Any additional context or details..."
                  rows="2"
                />
              </div>

              <!-- Preview -->
              <div class="p-3 bg-muted/50 rounded-lg space-y-2">
                <Label class="text-sm font-medium">Preview</Label>
                <div class="text-sm space-y-1">
                  <div class="flex justify-between">
                    <span>Current Score:</span>
                    <span>{{ originalScore }}/{{ maxPoints }}</span>
                  </div>
                  <div
                    class="flex justify-between"
                    :class="{
                      'text-green-600': adjustmentForm.type === 'bonus',
                      'text-red-600': adjustmentForm.type === 'penalty',
                    }"
                  >
                    <span>{{ adjustmentForm.type === 'bonus' ? 'Bonus:' : 'Penalty:' }}</span>
                    <span>{{ adjustmentForm.type === 'bonus' ? '+' : '-' }}{{ adjustmentForm.amount || 0 }}</span>
                  </div>
                  <Separator />
                  <div class="flex justify-between font-medium">
                    <span>New Score:</span>
                    <span>
                      {{
                        originalScore +
                        (adjustmentForm.type === 'bonus' ? adjustmentForm.amount || 0 : -(adjustmentForm.amount || 0))
                      }}/{{ maxPoints }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button @click="applyAdjustment" :disabled="!adjustmentForm.reason.trim() || adjustmentForm.amount === 0">
                Apply Adjustment
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog v-model:open="showExclusionDialog" v-if="!isExcluded">
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" class="text-red-600 border-red-600 hover:bg-red-50">
              <Ban class="h-4 w-4 mr-1" />
              Exclude Score
            </Button>
          </DialogTrigger>

          <DialogContent class="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Exclude Score from Calculations</DialogTitle>
              <DialogDescription>
                This score will not be included in grade calculations. This action can be reversed.
              </DialogDescription>
            </DialogHeader>

            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="exclusion-reason">Reason for Exclusion</Label>
                <Select v-model="exclusionForm.reason">
                  <SelectTrigger>
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="reason in exclusionReasons" :key="reason.code" :value="reason.code">
                      {{ reason.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div v-if="exclusionForm.reason" class="p-3 bg-muted/50 rounded-lg">
                <p class="text-sm text-muted-foreground">
                  {{ exclusionReasons.find((r) => r.code === exclusionForm.reason)?.description }}
                </p>
                <div v-if="exclusionForm.requiresApproval" class="mt-2">
                  <Badge variant="outline" class="text-yellow-600 border-yellow-600">
                    <AlertTriangle class="h-3 w-3 mr-1" />
                    Requires Approval
                  </Badge>
                </div>
              </div>

              <div class="space-y-2">
                <Label for="exclusion-description">Additional Details (Optional)</Label>
                <Textarea
                  id="exclusion-description"
                  v-model="exclusionForm.description"
                  placeholder="Provide any additional context or details..."
                  rows="3"
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="destructive" @click="excludeScore" :disabled="!exclusionForm.reason">
                Exclude Score
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Button
          v-if="isExcluded"
          variant="outline"
          size="sm"
          @click="openInclusionConfirm"
          class="text-green-600 border-green-600 hover:bg-green-50"
        >
          <Eye class="h-4 w-4 mr-1" />
          Include Score
        </Button>
      </div>

      <!-- History Dialog -->
      <Dialog v-model:open="showHistoryDialog">
        <DialogContent class="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Adjustment History</DialogTitle>
            <DialogDescription>Complete history of all score adjustments for this submission.</DialogDescription>
          </DialogHeader>

          <div class="space-y-4 max-h-96 overflow-y-auto">
            <div v-if="adjustmentHistory.length === 0" class="text-center py-8 text-muted-foreground">
              No adjustments have been applied to this score.
            </div>

            <div v-for="adjustment in adjustmentHistory" :key="adjustment.id" class="border rounded-lg p-4 space-y-3">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <component
                    :is="adjustmentCategories[adjustment.category || 'other'].icon"
                    class="h-4 w-4"
                    :class="adjustmentCategories[adjustment.category || 'other'].color"
                  />
                  <span class="font-medium">{{ adjustment.reason }}</span>
                </div>

                <Badge :variant="adjustment.type === 'bonus' ? 'default' : 'destructive'" class="gap-1">
                  <component :is="adjustment.type === 'bonus' ? Plus : Minus" class="h-3 w-3" />
                  {{ adjustment.amount }}
                </Badge>
              </div>

              <div class="text-sm space-y-1">
                <div class="flex justify-between">
                  <span class="text-muted-foreground">Type:</span>
                  <span>{{ adjustmentCategories[adjustment.category || 'other'].label }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">Applied by:</span>
                  <span>{{ adjustment.appliedBy }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">Date:</span>
                  <span>{{ formatDate(adjustment.appliedAt) }}</span>
                </div>
              </div>

              <div v-if="adjustment.description" class="text-sm">
                <span class="font-medium">Details:</span>
                <p class="text-muted-foreground mt-1">{{ adjustment.description }}</p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <!-- Confirmation Dialog -->
      <Dialog v-model:open="showConfirmDialog">
        <DialogContent class="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {{ confirmAction === 'include' ? 'Include Score' : 'Confirm Action' }}
            </DialogTitle>
            <DialogDescription>
              {{
                confirmAction === 'include'
                  ? 'This score will be included in grade calculations.'
                  : 'Are you sure you want to proceed with this action?'
              }}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" @click="showConfirmDialog = false">Cancel</Button>
            <Button @click="handleConfirmAction">Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <!-- Status Alert -->
      <Alert
        :class="{
          'border-red-500 bg-red-50': isExcluded,
          'border-green-500 bg-green-50': hasAdjustments && !isExcluded,
          'border-gray-200 bg-gray-50': !hasAdjustments && !isExcluded,
        }"
      >
        <component :is="isExcluded ? EyeOff : hasAdjustments ? Calculator : Eye" class="h-4 w-4" />
        <AlertDescription>
          <span v-if="isExcluded" class="text-red-700">
            This score is excluded from grade calculations.
            <span v-if="exclusionReasonData">Reason: {{ exclusionReasonData.label }}</span>
          </span>
          <span v-else-if="hasAdjustments" class="text-green-700">
            Score has been adjusted by {{ totalAdjustments > 0 ? '+' : '' }}{{ totalAdjustments }} points. Final score:
            {{ adjustedScore }}/{{ maxPoints }} ({{ adjustedPercentage }}%)
          </span>
          <span v-else class="text-gray-700">Original score with no adjustments applied.</span>
        </AlertDescription>
      </Alert>
    </CardContent>
  </Card>
</template>

<style scoped>
/* Custom styling for adjustment indicators */
.border-l-4 {
  border-left-width: 4px;
}

/* Focus indicators for accessibility */
input:focus,
textarea:focus,
button:focus,
[role='combobox']:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .text-muted-foreground {
    color: inherit;
    opacity: 0.8;
  }
}

/* Ensure proper color contrast */
.text-green-600 {
  color: #059669;
}

.text-red-600 {
  color: #dc2626;
}

.text-yellow-600 {
  color: #d97706;
}

.text-blue-600 {
  color: #2563eb;
}

.text-purple-600 {
  color: #9333ea;
}

.text-gray-600 {
  color: #4b5563;
}
</style>
