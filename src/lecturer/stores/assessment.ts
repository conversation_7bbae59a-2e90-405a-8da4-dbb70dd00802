import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAssessmentApi } from '@/lecturer/composables/useAssessmentApi'
import type {
  Assessment,
  AssessmentDetail,
  GradeEntry,
  GradeStatistics,
  StudentPerformance,
  GradingSession,
  BulkGradeUpdate,
  BulkOperationResult,
  ExportOptions,
  ExportResult,
  ValidationResult,
  WeightValidation,
  CreateAssessmentRequest,
  UpdateAssessmentRequest,
  UpdateGradeRequest,
  GradeQueryParams,
  GradeFilter,
  AssessmentType,
} from '@/lecturer/types/models/assessment'
import { GradingMode, GradeStatus } from '@/lecturer/types/models/assessment'

export const useAssessmentStore = defineStore('assessment', () => {
  const assessmentApi = useAssessmentApi()

  // State
  const assessments = ref<Assessment[]>([])
  const selectedAssessment = ref<Assessment | null>(null)
  const assessmentDetails = ref<AssessmentDetail[]>([])
  const grades = ref<GradeEntry[]>([])
  const gradeStatistics = ref<GradeStatistics | null>(null)
  const studentPerformance = ref<StudentPerformance[]>([])
  const currentGradingSession = ref<GradingSession | null>(null)
  const unsavedChanges = ref<GradeEntry[]>([])
  const validationResults = ref<ValidationResult | null>(null)
  const weightValidation = ref<WeightValidation | null>(null)

  // Loading states
  const loading = ref(false)
  const gradesLoading = ref(false)
  const statisticsLoading = ref(false)
  const exportLoading = ref(false)
  const bulkOperationLoading = ref(false)

  // Error states
  const error = ref<string | null>(null)
  const gradesError = ref<string | null>(null)
  const validationError = ref<string | null>(null)

  // UI state
  const gradingMode = ref<GradingMode>(GradingMode.BY_STUDENT)
  const selectedStudentId = ref<number | null>(null)
  const selectedAssessmentDetailId = ref<number | null>(null)
  const gradeFilters = ref<GradeFilter>({})
  const autoSaveEnabled = ref(true)
  const lastSaved = ref<string | null>(null)

  // Computed properties
  const totalWeight = computed(() => assessments.value.reduce((sum, assessment) => sum + assessment.weight, 0))

  const isWeightValid = computed(() => totalWeight.value <= 100)

  const weightExceeded = computed(() => Math.max(0, totalWeight.value - 100))

  const assessmentsByType = computed(() => {
    const grouped: Record<AssessmentType, Assessment[]> = {} as Record<AssessmentType, Assessment[]>
    assessments.value.forEach((assessment) => {
      if (!grouped[assessment.type]) {
        grouped[assessment.type] = []
      }
      grouped[assessment.type].push(assessment)
    })
    return grouped
  })

  const requiredAssessments = computed(() => assessments.value.filter((assessment) => assessment.is_required))

  const optionalAssessments = computed(() => assessments.value.filter((assessment) => !assessment.is_required))

  const gradingProgress = computed(() => {
    if (grades.value.length === 0) return { completed: 0, total: 0, percentage: 0 }

    const completed = grades.value.filter(
      (grade) => grade.status === GradeStatus.FINAL || grade.status === GradeStatus.PROVISIONAL,
    ).length
    const total = grades.value.length
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0

    return { completed, total, percentage }
  })

  const pendingGrades = computed(() =>
    grades.value.filter((grade) => grade.status === GradeStatus.DRAFT || grade.status === GradeStatus.PENDING),
  )

  const lateSubmissions = computed(() => grades.value.filter((grade) => grade.is_late))

  const academicIntegrityFlags = computed(() => grades.value.filter((grade) => grade.academic_integrity !== 'clear'))

  const hasUnsavedChanges = computed(() => unsavedChanges.value.length > 0)

  const filteredGrades = computed(() => {
    let filtered = [...grades.value]

    if (gradeFilters.value.status?.length) {
      filtered = filtered.filter((grade) => gradeFilters.value.status!.includes(grade.status))
    }

    if (gradeFilters.value.is_late !== undefined) {
      filtered = filtered.filter((grade) => grade.is_late === gradeFilters.value.is_late)
    }

    if (gradeFilters.value.is_excluded !== undefined) {
      filtered = filtered.filter((grade) => grade.is_excluded === gradeFilters.value.is_excluded)
    }

    if (gradeFilters.value.has_academic_integrity_flag !== undefined) {
      filtered = filtered.filter(
        (grade) => (grade.academic_integrity !== 'clear') === gradeFilters.value.has_academic_integrity_flag,
      )
    }

    if (gradeFilters.value.score_range) {
      const { min, max } = gradeFilters.value.score_range
      filtered = filtered.filter((grade) => grade.percentage_score >= min && grade.percentage_score <= max)
    }

    if (gradeFilters.value.search_query) {
      const query = gradeFilters.value.search_query.toLowerCase()
      filtered = filtered.filter(
        (grade) =>
          grade.student_name.toLowerCase().includes(query) || grade.student_email.toLowerCase().includes(query),
      )
    }

    return filtered
  })

  const atRiskStudents = computed(() =>
    studentPerformance.value.filter((student) => student.risk_level === 'high' || student.risk_level === 'critical'),
  )

  const exceptionalStudents = computed(() =>
    studentPerformance.value.filter((student) => student.overall_grade >= 90 && student.risk_level === 'low'),
  )

  // Actions - Assessment Management
  const fetchAssessments = async (courseOfferingId: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await assessmentApi.assessments.getAll(courseOfferingId)
      if (response.success) {
        assessments.value = response.data || []
        await validateWeights(courseOfferingId)
      } else {
        throw new Error(response.message || 'Failed to fetch assessments')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch assessments'
      console.error('Assessment fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchAssessmentById = async (assessmentId: number) => {
    try {
      const response = await assessmentApi.assessments.getById(assessmentId)
      if (response.success) {
        const assessment = response.data!

        // Update in assessments array
        const index = assessments.value.findIndex((a) => a.id === assessmentId)
        if (index !== -1) {
          assessments.value[index] = assessment
        } else {
          assessments.value.push(assessment)
        }

        selectedAssessment.value = assessment
        return assessment
      } else {
        throw new Error(response.message || 'Failed to fetch assessment')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch assessment'
      throw err
    }
  }

  const createAssessment = async (assessmentData: CreateAssessmentRequest) => {
    loading.value = true
    error.value = null

    try {
      const response = await assessmentApi.assessments.create(assessmentData)
      if (response.success) {
        const newAssessment = response.data!
        assessments.value.push(newAssessment)
        await validateWeights(assessmentData.course_offering_id)
        return newAssessment
      } else {
        throw new Error(response.message || 'Failed to create assessment')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create assessment'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateAssessment = async (assessmentData: UpdateAssessmentRequest) => {
    loading.value = true
    error.value = null

    try {
      const response = await assessmentApi.assessments.update(assessmentData)
      if (response.success) {
        const updatedAssessment = response.data!

        // Update in assessments array
        const index = assessments.value.findIndex((a) => a.id === assessmentData.id)
        if (index !== -1) {
          assessments.value[index] = updatedAssessment
        }

        if (selectedAssessment.value?.id === assessmentData.id) {
          selectedAssessment.value = updatedAssessment
        }

        await validateWeights(updatedAssessment.course_offering_id)
        return updatedAssessment
      } else {
        throw new Error(response.message || 'Failed to update assessment')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update assessment'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteAssessment = async (assessmentId: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await assessmentApi.assessments.delete(assessmentId)
      if (response.success) {
        // Remove from assessments array
        const index = assessments.value.findIndex((a) => a.id === assessmentId)
        if (index !== -1) {
          const courseOfferingId = assessments.value[index].course_offering_id
          assessments.value.splice(index, 1)
          await validateWeights(courseOfferingId)
        }

        // Clear selection if deleted
        if (selectedAssessment.value?.id === assessmentId) {
          selectedAssessment.value = null
        }
      } else {
        throw new Error(response.message || 'Failed to delete assessment')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete assessment'
      throw err
    } finally {
      loading.value = false
    }
  }

  const duplicateAssessment = async (assessmentId: number, newName?: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await assessmentApi.assessments.duplicate(assessmentId, newName)
      if (response.success) {
        const duplicatedAssessment = response.data!
        assessments.value.push(duplicatedAssessment)
        await validateWeights(duplicatedAssessment.course_offering_id)
        return duplicatedAssessment
      } else {
        throw new Error(response.message || 'Failed to duplicate assessment')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to duplicate assessment'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Actions - Grading Management
  const fetchGrades = async (params: GradeQueryParams) => {
    gradesLoading.value = true
    gradesError.value = null

    try {
      const response = await assessmentApi.grades.getAll(params)
      if (response.success) {
        grades.value = response.data || []
      } else {
        throw new Error(response.message || 'Failed to fetch grades')
      }
    } catch (err) {
      gradesError.value = err instanceof Error ? err.message : 'Failed to fetch grades'
      console.error('Grades fetch error:', err)
    } finally {
      gradesLoading.value = false
    }
  }

  const updateGrade = async (gradeData: UpdateGradeRequest) => {
    try {
      const response = await assessmentApi.grades.update(gradeData)
      if (response.success) {
        const updatedGrade = response.data!

        // Update in grades array
        const index = grades.value.findIndex((g) => g.id === gradeData.id)
        if (index !== -1) {
          grades.value[index] = updatedGrade
        }

        // Remove from unsaved changes
        const unsavedIndex = unsavedChanges.value.findIndex((g) => g.id === gradeData.id)
        if (unsavedIndex !== -1) {
          unsavedChanges.value.splice(unsavedIndex, 1)
        }

        lastSaved.value = new Date().toISOString()
        return updatedGrade
      } else {
        throw new Error(response.message || 'Failed to update grade')
      }
    } catch (err) {
      gradesError.value = err instanceof Error ? err.message : 'Failed to update grade'
      throw err
    }
  }

  const bulkUpdateGrades = async (bulkData: BulkGradeUpdate) => {
    bulkOperationLoading.value = true
    gradesError.value = null

    try {
      const response = await assessmentApi.grades.bulkUpdate(bulkData)
      if (response.success) {
        const result = response.data!

        // Update grades in local state
        result.updated_entries.forEach((updatedGrade) => {
          const index = grades.value.findIndex((g) => g.id === updatedGrade.id)
          if (index !== -1) {
            grades.value[index] = updatedGrade
          }
        })

        // Clear unsaved changes for successfully updated grades
        result.updated_entries.forEach((updatedGrade) => {
          const unsavedIndex = unsavedChanges.value.findIndex((g) => g.id === updatedGrade.id)
          if (unsavedIndex !== -1) {
            unsavedChanges.value.splice(unsavedIndex, 1)
          }
        })

        lastSaved.value = new Date().toISOString()
        return result
      } else {
        throw new Error(response.message || 'Failed to bulk update grades')
      }
    } catch (err) {
      gradesError.value = err instanceof Error ? err.message : 'Failed to bulk update grades'
      throw err
    } finally {
      bulkOperationLoading.value = false
    }
  }

  // Actions - Statistics and Analytics
  const fetchStatistics = async (assessmentId: number) => {
    statisticsLoading.value = true

    try {
      const response = await assessmentApi.assessments.getStatistics(assessmentId)
      if (response.success) {
        gradeStatistics.value = response.data!
        return response.data!
      } else {
        throw new Error(response.message || 'Failed to fetch statistics')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch statistics'
      throw err
    } finally {
      statisticsLoading.value = false
    }
  }

  const fetchStudentPerformance = async (courseOfferingId: number) => {
    loading.value = true

    try {
      const response = await assessmentApi.performance.getByCourse(courseOfferingId)
      if (response.success) {
        studentPerformance.value = response.data || []
        return response.data || []
      } else {
        throw new Error(response.message || 'Failed to fetch student performance')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch student performance'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Actions - Validation
  const validateWeights = async (courseOfferingId: number) => {
    try {
      const response = await assessmentApi.assessments.validateWeights(courseOfferingId)
      if (response.success) {
        weightValidation.value = response.data!
        return response.data!
      } else {
        throw new Error(response.message || 'Failed to validate weights')
      }
    } catch (err) {
      validationError.value = err instanceof Error ? err.message : 'Failed to validate weights'
      throw err
    }
  }

  const validateGrade = async (gradeData: Partial<GradeEntry>) => {
    try {
      const response = await assessmentApi.grades.validate(gradeData)
      if (response.success) {
        validationResults.value = response.data!
        return response.data!
      } else {
        throw new Error(response.message || 'Failed to validate grade')
      }
    } catch (err) {
      validationError.value = err instanceof Error ? err.message : 'Failed to validate grade'
      throw err
    }
  }

  // Actions - Export
  const exportData = async (courseOfferingId: number, options: ExportOptions) => {
    exportLoading.value = true
    error.value = null

    try {
      const response = await assessmentApi.export.exportData(courseOfferingId, options)
      if (response.success) {
        return response.data!
      } else {
        throw new Error(response.message || 'Failed to export data')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to export data'
      throw err
    } finally {
      exportLoading.value = false
    }
  }

  // Actions - Session Management
  const createGradingSession = async (sessionData: Omit<GradingSession, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const response = await assessmentApi.sessions.create(sessionData)
      if (response.success) {
        currentGradingSession.value = response.data!
        return response.data!
      } else {
        throw new Error(response.message || 'Failed to create grading session')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create grading session'
      throw err
    }
  }

  const updateGradingSession = async (sessionId: string, sessionData: Partial<GradingSession>) => {
    try {
      const response = await assessmentApi.sessions.update(sessionId, sessionData)
      if (response.success) {
        currentGradingSession.value = response.data!
        return response.data!
      } else {
        throw new Error(response.message || 'Failed to update grading session')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update grading session'
      throw err
    }
  }

  const saveSessionChanges = async (sessionId: string) => {
    if (unsavedChanges.value.length === 0) return

    try {
      const response = await assessmentApi.sessions.saveChanges(sessionId, unsavedChanges.value)
      if (response.success) {
        const result = response.data!

        // Update grades with saved changes
        result.updated_entries.forEach((updatedGrade) => {
          const index = grades.value.findIndex((g) => g.id === updatedGrade.id)
          if (index !== -1) {
            grades.value[index] = updatedGrade
          }
        })

        // Clear unsaved changes
        unsavedChanges.value = []
        lastSaved.value = new Date().toISOString()

        return result
      } else {
        throw new Error(response.message || 'Failed to save session changes')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to save session changes'
      throw err
    }
  }

  // Actions - UI State Management
  const setGradingMode = (mode: GradingMode) => {
    gradingMode.value = mode
  }

  const setSelectedStudent = (studentId: number | null) => {
    selectedStudentId.value = studentId
  }

  const setSelectedAssessmentDetail = (detailId: number | null) => {
    selectedAssessmentDetailId.value = detailId
  }

  const setGradeFilters = (filters: Partial<GradeFilter>) => {
    gradeFilters.value = { ...gradeFilters.value, ...filters }
  }

  const clearGradeFilters = () => {
    gradeFilters.value = {}
  }

  const addUnsavedChange = (grade: GradeEntry) => {
    const existingIndex = unsavedChanges.value.findIndex((g) => g.id === grade.id)
    if (existingIndex !== -1) {
      unsavedChanges.value[existingIndex] = grade
    } else {
      unsavedChanges.value.push(grade)
    }
  }

  const removeUnsavedChange = (gradeId: number) => {
    const index = unsavedChanges.value.findIndex((g) => g.id === gradeId)
    if (index !== -1) {
      unsavedChanges.value.splice(index, 1)
    }
  }

  const clearUnsavedChanges = () => {
    unsavedChanges.value = []
  }

  // Actions - Utility
  const selectAssessment = (assessment: Assessment) => {
    selectedAssessment.value = assessment
  }

  const clearSelectedAssessment = () => {
    selectedAssessment.value = null
    assessmentDetails.value = []
  }

  const clearError = () => {
    error.value = null
    gradesError.value = null
    validationError.value = null
  }

  const resetStore = () => {
    assessments.value = []
    selectedAssessment.value = null
    assessmentDetails.value = []
    grades.value = []
    gradeStatistics.value = null
    studentPerformance.value = []
    currentGradingSession.value = null
    unsavedChanges.value = []
    validationResults.value = null
    weightValidation.value = null

    loading.value = false
    gradesLoading.value = false
    statisticsLoading.value = false
    exportLoading.value = false
    bulkOperationLoading.value = false

    error.value = null
    gradesError.value = null
    validationError.value = null

    gradingMode.value = GradingMode.BY_STUDENT
    selectedStudentId.value = null
    selectedAssessmentDetailId.value = null
    gradeFilters.value = {}
    autoSaveEnabled.value = true
    lastSaved.value = null
  }

  return {
    // State
    assessments,
    selectedAssessment,
    assessmentDetails,
    grades,
    gradeStatistics,
    studentPerformance,
    currentGradingSession,
    unsavedChanges,
    validationResults,
    weightValidation,

    // Loading states
    loading,
    gradesLoading,
    statisticsLoading,
    exportLoading,
    bulkOperationLoading,

    // Error states
    error,
    gradesError,
    validationError,

    // UI state
    gradingMode,
    selectedStudentId,
    selectedAssessmentDetailId,
    gradeFilters,
    autoSaveEnabled,
    lastSaved,

    // Computed properties
    totalWeight,
    isWeightValid,
    weightExceeded,
    assessmentsByType,
    requiredAssessments,
    optionalAssessments,
    gradingProgress,
    pendingGrades,
    lateSubmissions,
    academicIntegrityFlags,
    hasUnsavedChanges,
    filteredGrades,
    atRiskStudents,
    exceptionalStudents,

    // Actions - Assessment Management
    fetchAssessments,
    fetchAssessmentById,
    createAssessment,
    updateAssessment,
    deleteAssessment,
    duplicateAssessment,

    // Actions - Grading Management
    fetchGrades,
    updateGrade,
    bulkUpdateGrades,

    // Actions - Statistics and Analytics
    fetchStatistics,
    fetchStudentPerformance,

    // Actions - Validation
    validateWeights,
    validateGrade,

    // Actions - Export
    exportData,

    // Actions - Session Management
    createGradingSession,
    updateGradingSession,
    saveSessionChanges,

    // Actions - UI State Management
    setGradingMode,
    setSelectedStudent,
    setSelectedAssessmentDetail,
    setGradeFilters,
    clearGradeFilters,
    addUnsavedChange,
    removeUnsavedChange,
    clearUnsavedChanges,

    // Actions - Utility
    selectAssessment,
    clearSelectedAssessment,
    clearError,
    resetStore,
  }
})
