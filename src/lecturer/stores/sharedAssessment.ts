import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type AssessmentTab = 'management' | 'grading' | 'reports'

export interface RecentlyModifiedAssessment {
  id: number
  name: string
  modifiedAt: Date
  modifiedBy: string
  action: 'created' | 'updated' | 'deleted' | 'graded'
}

export interface NavigationState {
  currentCourseId: string | null
  lastVisitedTab: AssessmentTab
  previousTab: AssessmentTab | null
}

export interface NotificationState {
  unreadCount: number
  lastChecked: Date | null
}

/**
 * Minimal shared store for assessment-related state that needs to persist across pages.
 * This store only contains state that truly needs to be shared between different assessment pages.
 * All other state should remain in tab-specific composables for better performance and maintainability.
 */
export const useSharedAssessmentStore = defineStore('sharedAssessment', () => {
  // Navigation state - persists across page navigation
  const navigation = ref<NavigationState>({
    currentCourseId: null,
    lastVisitedTab: 'management',
    previousTab: null,
  })

  // Recently modified assessments - for cross-tab notifications and quick access
  const recentlyModified = ref<RecentlyModifiedAssessment[]>([])

  // Notification state - for showing unread changes across tabs
  const notifications = ref<NotificationState>({
    unreadCount: 0,
    lastChecked: null,
  })

  // Session persistence - for maintaining state across browser sessions
  const sessionData = ref<{
    lastActiveTab: AssessmentTab | null
    lastActiveTime: Date | null
    unsavedChangesWarning: boolean
  }>({
    lastActiveTab: null,
    lastActiveTime: null,
    unsavedChangesWarning: false,
  })

  // Computed properties
  const currentCourseId = computed(() => navigation.value.currentCourseId)
  const lastVisitedTab = computed(() => navigation.value.lastVisitedTab)
  const hasRecentModifications = computed(() => recentlyModified.value.length > 0)
  const hasUnreadNotifications = computed(() => notifications.value.unreadCount > 0)

  // Recent modifications within last hour
  const recentModificationsThisHour = computed(() => {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    return recentlyModified.value.filter((item) => item.modifiedAt > oneHourAgo)
  })

  // Navigation actions
  const setCurrentCourse = (courseId: string): void => {
    navigation.value.currentCourseId = courseId

    // Clear course-specific data when switching courses
    if (courseId !== navigation.value.currentCourseId) {
      clearCourseSpecificData()
    }
  }

  const setLastVisitedTab = (tab: AssessmentTab): void => {
    navigation.value.previousTab = navigation.value.lastVisitedTab
    navigation.value.lastVisitedTab = tab

    // Update session data
    sessionData.value.lastActiveTab = tab
    sessionData.value.lastActiveTime = new Date()
  }

  const getPreviousTab = (): AssessmentTab | null => {
    return navigation.value.previousTab
  }

  // Recently modified assessments management
  const addRecentlyModified = (assessment: Omit<RecentlyModifiedAssessment, 'modifiedAt'>): void => {
    const newItem: RecentlyModifiedAssessment = {
      ...assessment,
      modifiedAt: new Date(),
    }

    // Remove existing entry for the same assessment if it exists
    const existingIndex = recentlyModified.value.findIndex((item) => item.id === assessment.id)
    if (existingIndex !== -1) {
      recentlyModified.value.splice(existingIndex, 1)
    }

    // Add to the beginning of the array
    recentlyModified.value.unshift(newItem)

    // Keep only the last 20 modifications
    if (recentlyModified.value.length > 20) {
      recentlyModified.value = recentlyModified.value.slice(0, 20)
    }

    // Increment unread count
    notifications.value.unreadCount++
  }

  const removeRecentlyModified = (assessmentId: number): void => {
    recentlyModified.value = recentlyModified.value.filter((item) => item.id !== assessmentId)
  }

  const clearRecentlyModified = (): void => {
    recentlyModified.value = []
    notifications.value.unreadCount = 0
  }

  const getRecentlyModified = (limit?: number): RecentlyModifiedAssessment[] => {
    return limit ? recentlyModified.value.slice(0, limit) : recentlyModified.value
  }

  const getRecentlyModifiedByAction = (action: RecentlyModifiedAssessment['action']): RecentlyModifiedAssessment[] => {
    return recentlyModified.value.filter((item) => item.action === action)
  }

  // Notification management
  const markNotificationsAsRead = (): void => {
    notifications.value.unreadCount = 0
    notifications.value.lastChecked = new Date()
  }

  const incrementUnreadCount = (count: number = 1): void => {
    notifications.value.unreadCount += count
  }

  const getUnreadCount = (): number => {
    return notifications.value.unreadCount
  }

  // Session management
  const setUnsavedChangesWarning = (hasUnsavedChanges: boolean): void => {
    sessionData.value.unsavedChangesWarning = hasUnsavedChanges
  }

  const hasUnsavedChangesWarning = (): boolean => {
    return sessionData.value.unsavedChangesWarning
  }

  const updateLastActiveTime = (): void => {
    sessionData.value.lastActiveTime = new Date()
  }

  const getLastActiveTime = (): Date | null => {
    return sessionData.value.lastActiveTime
  }

  const getSessionDuration = (): number => {
    if (!sessionData.value.lastActiveTime) return 0
    return Date.now() - sessionData.value.lastActiveTime.getTime()
  }

  // Utility functions
  const clearCourseSpecificData = (): void => {
    recentlyModified.value = []
    notifications.value.unreadCount = 0
    notifications.value.lastChecked = null
    sessionData.value.unsavedChangesWarning = false
  }

  const clearAllData = (): void => {
    navigation.value = {
      currentCourseId: null,
      lastVisitedTab: 'management',
      previousTab: null,
    }
    recentlyModified.value = []
    notifications.value = {
      unreadCount: 0,
      lastChecked: null,
    }
    sessionData.value = {
      lastActiveTab: null,
      lastActiveTime: null,
      unsavedChangesWarning: false,
    }
  }

  // Persistence helpers (for localStorage integration if needed)
  const exportState = () => {
    return {
      navigation: navigation.value,
      recentlyModified: recentlyModified.value,
      notifications: notifications.value,
      sessionData: sessionData.value,
    }
  }

  const importState = (state: ReturnType<typeof exportState>): void => {
    navigation.value = state.navigation
    recentlyModified.value = state.recentlyModified.map((item) => ({
      ...item,
      modifiedAt: new Date(item.modifiedAt), // Ensure Date objects
    }))
    notifications.value = state.notifications
    sessionData.value = {
      ...state.sessionData,
      lastActiveTime: state.sessionData.lastActiveTime ? new Date(state.sessionData.lastActiveTime) : null,
    }
  }

  // Cleanup function for when the store is no longer needed
  const cleanup = (): void => {
    clearAllData()
  }

  // Auto-cleanup old modifications (older than 24 hours)
  const cleanupOldModifications = (): void => {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
    recentlyModified.value = recentlyModified.value.filter((item) => item.modifiedAt > twentyFourHoursAgo)
  }

  // Set up periodic cleanup (run every hour)
  if (typeof window !== 'undefined') {
    setInterval(cleanupOldModifications, 60 * 60 * 1000) // 1 hour
  }

  return {
    // State (readonly computed)
    currentCourseId,
    lastVisitedTab,
    hasRecentModifications,
    hasUnreadNotifications,
    recentModificationsThisHour,

    // Raw state access (for debugging/advanced use)
    navigation: computed(() => navigation.value),
    recentlyModified: computed(() => recentlyModified.value),
    notifications: computed(() => notifications.value),
    sessionData: computed(() => sessionData.value),

    // Navigation actions
    setCurrentCourse,
    setLastVisitedTab,
    getPreviousTab,

    // Recently modified management
    addRecentlyModified,
    removeRecentlyModified,
    clearRecentlyModified,
    getRecentlyModified,
    getRecentlyModifiedByAction,

    // Notification management
    markNotificationsAsRead,
    incrementUnreadCount,
    getUnreadCount,

    // Session management
    setUnsavedChangesWarning,
    hasUnsavedChangesWarning,
    updateLastActiveTime,
    getLastActiveTime,
    getSessionDuration,

    // Utility functions
    clearCourseSpecificData,
    clearAllData,
    exportState,
    importState,
    cleanup,
    cleanupOldModifications,
  }
})

// Type exports for external use
export type SharedAssessmentStore = ReturnType<typeof useSharedAssessmentStore>
