<script setup lang="ts">
import { RouterView } from 'vue-router';
import { onMounted } from 'vue';
import { useAuthStore } from '@/shared/stores/auth';
import { Toaster } from '@/shared/components/ui/sonner';
import 'vue-sonner/style.css'; // vue-sonner v2 requires this import

const authStore = useAuthStore();

// Initialize auth state
onMounted(async () => {
  authStore.initializeAuth();

  // If user is authenticated, fetch fresh user data
  if (authStore.isAuthenticated) {
    try {
      await authStore.getMe();
    } catch (error) {
      console.error('Failed to fetch user data on app start:', error);
    }
  }
});
</script>

<template>
  <div id="app">
    <RouterView />
    <Toaster position="top-center" rich-colors />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
}
</style>
