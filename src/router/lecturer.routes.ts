import type { RouteRecordRaw } from 'vue-router'

export const lecturerRoutes: RouteRecordRaw = {
  path: '/lecturer',
  component: () => import('@/lecturer/components/layout/LecturerLayout.vue'),
  meta: { requiresAuth: true, role: 'lecturer' },
  children: [
    {
      path: 'dashboard',
      name: 'lecturer-dashboard',
      component: () => import('@/lecturer/views/DashboardView.vue'),
    },
    {
      path: 'teaching',
      name: 'lecturer-teaching',
      children: [
        {
          path: 'courses',
          name: 'lecturer-courses',
          component: () => import('@/lecturer/views/CoursesView.vue'),
        },
        {
          path: 'courses/:courseId/sessions',
          name: 'lecturer-course-sessions',
          component: () => import('@/lecturer/views/CourseSessionsView.vue'),
          props: true,
        },
        {
          path: 'courses/:courseId/students',
          name: 'lecturer-course-students',
          component: () => import('@/lecturer/views/CourseStudentView.vue'),
          props: true,
        },
        {
          path: 'courses/:courseId/assessments',
          name: 'lecturer-assessment-management',
          component: () => import('@/lecturer/views/AssessmentManagementView.vue'),
          props: true,
        },
        {
          path: 'courses/:courseId/assessments/grading',
          name: 'lecturer-assessment-grading',
          component: () => import('@/lecturer/views/AssessmentGradingView.vue'),
          props: true,
        },
        {
          path: 'courses/:courseId/assessments/grading/:assessmentId',
          name: 'lecturer-assessment-grading-detail',
          component: () => import('@/lecturer/views/AssessmentGradingView.vue'),
          props: true,
        },
        {
          path: 'courses/:courseId/assessments/report',
          name: 'lecturer-assessment-report',
          component: () => import('@/lecturer/views/AssessmentReportView.vue'),
          props: true,
        },
        {
          path: 'courses/:courseId/assessments/grades',
          name: 'lecturer-grade-matrix',
          component: () => import('@/lecturer/views/GradeMatrixView.vue'),
          props: true,
        },
        {
          path: 'courses/sessions/:sessionId/attendance',
          name: 'lecturer-course-session-detail',
          component: () => import('@/lecturer/views/CourseSessionAttendanceView.vue'),
          props: true,
        },
        {
          path: 'timetable',
          name: 'lecturer-timetable',
          component: () => import('@/lecturer/views/TimetableView.vue'),
        },
      ],
    },
    {
      path: 'attendance',
      name: 'lecturer-attendance',
      component: () => import('@/lecturer/views/AttendanceView.vue'),
    },
    // Commented routes for future implementation
    // {
    //   path: 'students',
    //   name: 'lecturer-students',
    //   component: () => import('@/lecturer/views/StudentsView.vue'),
    // },
    // {
    //   path: 'feedback',
    //   name: 'lecturer-feedback',
    //   component: () => import('@/lecturer/views/FeedbackView.vue'),
    // },
    // {
    //   path: 'notifications',
    //   name: 'lecturer-notifications',
    //   component: () => import('@/lecturer/views/NotificationsView.vue'),
    // },
    // {
    //   path: 'profile',
    //   name: 'lecturer-profile',
    //   component: () => import('@/lecturer/views/ProfileView.vue'),
    // },
    // {
    //   path: 'settings',
    //   name: 'lecturer-settings',
    //   component: () => import('@/lecturer/views/SettingsView.vue'),
    // },
  ],
}
