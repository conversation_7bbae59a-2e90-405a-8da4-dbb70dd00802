import type { RouteRecordRaw } from 'vue-router'

export const studentRoutes: RouteRecordRaw = {
  path: '/student',
  component: () => import('@/student/components/layout/StudentLayout.vue'),
  meta: { requiresAuth: true, role: 'student' },
  children: [
    {
      path: 'dashboard',
      name: 'student-dashboard',
      component: () => import('@/student/views/DashboardView.vue'),
    },
    {
      path: 'schedule',
      name: 'student-schedule',
      component: () => import('@/student/views/ScheduleView.vue'),
    },
    {
      path: 'courses/open',
      name: 'student-courses',
      component: () => import('@/student/views/CoursesView.vue'),
    },
    {
      path: 'profile/info',
      name: 'student-profile',
      component: () => import('@/student/views/ProfileView.vue'),
    },
    {
      path: 'settings',
      name: 'student-settings',
      component: () => import('@/student/views/SettingsView.vue'),
    },
    {
      path: 'assessment',
      name: 'student-assessment',
      component: () => import('@/student/views/AssessmentView.vue'),
    },
    {
      path: 'attendance',
      name: 'student-attendance',
      component: () => import('@/student/views/AttendanceView.vue'),
    },
    {
      path: 'holds',
      name: 'student-holds',
      component: () => import('@/student/views/HoldsView.vue'),
    },
    {
      path: 'curriculum',
      name: 'student-curriculum',
      component: () => import('@/student/views/CurriculumView.vue'),
    },
    {
      path: 'notifications',
      name: 'student-notifications',
      component: () => import('@/student/views/NotificationsView.vue'),
    },
    {
      path: 'profile/study-plan',
      name: 'student-profile-study-plan',
      component: () => import('@/student/views/ProfileStudyPlanView.vue'),
    },
    {
      path: 'courses/registrations',
      name: 'student-course-registrations',
      component: () => import('@/student/views/CourseRegistrationsView.vue'),
    },
    {
      path: 'grades/trend',
      name: 'student-grades-trend',
      component: () => import('@/student/views/GradesTrendView.vue'),
    },
  ],
}
