# Backend API Requirements for Student Portal

## Overview

This document outlines the complete backend API requirements, database models, and technical specifications needed to support the Student Portal frontend implementation. The backend should provide RESTful APIs with proper authentication, validation, and error handling.

## Required Database Models

### 1. Core Academic Structure

```sql
-- Academic Program Management
CREATE TABLE programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE specializations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_id UUID REFERENCES programs(id),
  name VA<PERSON>HAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE semesters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VA<PERSON>HA<PERSON>(100) NOT NULL,
  code VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE curriculum_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_id UUID REFERENCES programs(id),
  specialization_id UUID REFERENCES specializations(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE units (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(20) NOT NULL,
  name VARCHAR(255) NOT NULL,
  credit_points INTEGER NOT NULL,
  unit_scope VARCHAR(50) DEFAULT 'program_specific', -- program_specific, cross_program, general_education
  unit_type VARCHAR(50) DEFAULT 'core', -- core, elective, prerequisite
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE curriculum_units (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  curriculum_version_id UUID REFERENCES curriculum_versions(id),
  unit_id UUID REFERENCES units(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Facilities and Staff

```sql
-- Facilities
CREATE TABLE campuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  address TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE buildings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campus_id UUID REFERENCES campuses(id),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campus_id UUID REFERENCES campuses(id),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Staff
CREATE TABLE lectures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id VARCHAR(50) NOT NULL,
  email VARCHAR(255) NOT NULL,
  campus_id UUID REFERENCES campuses(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Course Delivery and Registration

```sql
-- Course Offerings
CREATE TABLE course_offerings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  curriculum_unit_id UUID REFERENCES curriculum_units(id),
  semester_id UUID REFERENCES semesters(id),
  lecture_id UUID REFERENCES lectures(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE class_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_offering_id UUID REFERENCES course_offerings(id),
  instructor_id UUID REFERENCES lectures(id),
  room_id UUID REFERENCES rooms(id),
  room_booking_id UUID, -- References room_bookings if implemented
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Management
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id VARCHAR(50) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  program_id UUID REFERENCES programs(id),
  specialization_id UUID REFERENCES specializations(id),
  campus_id UUID REFERENCES campuses(id),
  curriculum_version_id UUID REFERENCES curriculum_versions(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE course_registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  semester_id UUID REFERENCES semesters(id),
  curriculum_version_id UUID REFERENCES curriculum_versions(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Academic Records and Assessment

```sql
-- Academic Records
CREATE TABLE academic_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  semester_id UUID REFERENCES semesters(id),
  unit_id UUID REFERENCES units(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE academic_standings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE gpa_calculations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  semester_id UUID REFERENCES semesters(id),
  program_id UUID REFERENCES programs(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Assessment System
CREATE TABLE syllabus (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  curriculum_unit_id UUID REFERENCES curriculum_units(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE assessment_components (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  syllabus_id UUID REFERENCES syllabus(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE assessment_component_details (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  component_id UUID REFERENCES assessment_components(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE assessment_component_detail_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assessment_component_detail_id UUID REFERENCES assessment_component_details(id),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Attendance
CREATE TABLE attendances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_session_id UUID REFERENCES class_sessions(id),
  student_id UUID REFERENCES students(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Academic Support and System Management

```sql
-- Academic Holds
CREATE TABLE academic_holds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  placed_by_user_id UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE program_change_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  from_program_id UUID REFERENCES programs(id),
  to_program_id UUID REFERENCES programs(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- System Management
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE campus_user_roles (
  user_id UUID REFERENCES users(id),
  campus_id UUID REFERENCES campuses(id),
  role_id UUID REFERENCES roles(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  PRIMARY KEY (user_id, campus_id, role_id)
);
```

## Required API Endpoints

### 1. Dashboard APIs

```http
GET /api/student/dashboard
# Response: Current semester, GPA calculations, credit progress, academic holds, upcoming assessments
# Includes: Semester, GPACalculation, CurriculumUnit progress, AcademicHold[], AssessmentComponentDetail[]

GET /api/student/dashboard/semester/current
# Response: Current semester details with enrollment status
# Includes: Semester with Enrollment data

GET /api/student/dashboard/credit-progress
# Response: Earned vs required credits based on CurriculumVersion and AcademicRecord
# Includes: CurriculumUnit[] remaining requirements, completion percentage

GET /api/student/dashboard/gpa
# Response: GPACalculation with semester and cumulative GPA, trend analysis
# Includes: GPACalculation with Program and Semester relationships

GET /api/student/dashboard/holds
# Response: Active AcademicHold entities with User (placed_by) information
# Includes: AcademicHold[] with resolution details
```

### 2. Course Registration APIs

```http
GET /api/student/course-offerings/available/{semester_id}
# Query params: curriculum_version_id, search, page, limit
# Response: Available CourseOffering entities with CurriculumUnit, Lecturer, and ClassSession data
# Includes: CourseOffering[] with enrollment capacity and conflict information

GET /api/student/course-registrations
# Query params: semester_id, status
# Response: Student's CourseRegistration entities with CourseOffering details
# Includes: CourseRegistration[] with CourseOffering, Semester relationships

POST /api/student/course-registrations
# Body: { course_offering_id: uuid, semester_id: uuid }
# Response: CourseRegistration confirmation with conflict detection
# Validates: ClassSession time conflicts, prerequisite requirements

DELETE /api/student/course-registrations/{course_offering_id}
# Response: CourseRegistration removal confirmation

GET /api/student/course-offerings/{course_offering_id}/details
# Response: CourseOffering with CurriculumUnit, Unit, ClassSession, and Lecturer details
# Includes: Complete course offering information with schedule

GET /api/student/course-offerings/conflicts
# Body: { course_offering_ids: [uuid] }
# Response: ClassSession time conflicts and CurriculumUnit prerequisite violations
# Includes: Detailed conflict analysis with affected sessions

GET /api/student/curriculum-units/{curriculum_unit_id}/prerequisites
# Response: Unit prerequisite tree with completion status from AcademicRecord
# Includes: Unit relationships and student completion status

GET /api/course-offerings/cross-program-electives/{semester_id}
# Query params: student_specialization_id, unit_scope=cross_program
# Response: CourseOffering entities from other specializations with unit_scope = cross_program
# Includes: CourseOffering[] excluding student's specialization, with eligibility status

GET /api/course-offerings/elective-eligibility
# Body: { course_offering_id: uuid, student_id: uuid }
# Response: Eligibility validation including prerequisites, holds, and schedule conflicts
# Includes: Detailed eligibility status with specific blocking conditions

GET /api/course-registrations/elective-selections/{semester_id}
# Response: Student's current cross-program elective selections for the semester
# Includes: CourseRegistration[] filtered for cross-program electives only
```

### 3. Timetable APIs

```http
GET /api/student/timetable
# Query params: semester_id, week_start_date
# Response: ClassSession entities for student's registered CourseOffering
# Includes: ClassSession[] with CourseOffering, Room, and Lecturer information

GET /api/student/class-sessions/{class_session_id}
# Response: ClassSession details with Room, CourseOffering, and online meeting information
# Includes: Complete session details with location and instructor data

GET /api/student/timetable/filters
# Response: Available filter options based on student's CourseOffering entities
# Includes: CourseOffering types, Lecturer names, Room locations
```

### 4. Grades and Academic Progress APIs

```http
GET /api/student/grades
# Query params: semester_id (optional)
# Response: AcademicRecord entities grouped by Semester with Unit and CourseOffering details
# Includes: AcademicRecord[], AssessmentScore[], GPACalculation[], semester summaries

GET /api/student/grades/gpa-trend
# Response: GPACalculation history across Semester entities with trend analysis
# Includes: GPACalculation[] with Semester and Program relationships

GET /api/student/grades/course-offering/{course_offering_id}
# Response: AcademicRecord and AssessmentScore entities for specific CourseOffering
# Includes: Detailed assessment breakdown with Syllabus and AssessmentComponent data

GET /api/student/assessments
# Query params: course_offering_id, semester_id, status
# Response: AssessmentComponentDetail entities with submission status
# Includes: AssessmentComponentDetail[] with AssessmentScore relationships

GET /api/student/assessments/{assessment_component_detail_id}
# Response: AssessmentComponentDetail with AssessmentComponent and Syllabus information
# Includes: Complete assessment details with scoring rubric

GET /api/student/assessment-scores
# Query params: course_offering_id, assessment_component_detail_id
# Response: AssessmentScore entities for student with CourseOffering context
# Includes: AssessmentScore[] with grading details and feedback
```

### 5. Attendance APIs

```http
GET /api/student/attendance
# Query params: semester_id, course_offering_id
# Response: Attendance entities with ClassSession and CourseOffering relationships
# Includes: Attendance[] with session details and percentage calculations

GET /api/student/attendance/summary
# Response: Attendance summary per CourseOffering with ClassSession totals
# Includes: CourseOffering attendance percentages, total/attended session counts

GET /api/student/attendance/alerts
# Response: CourseOffering entities where attendance falls below thresholds
# Includes: CourseOffering[] with attendance percentage and risk status
```

### 6. Profile Management APIs

```http
PUT /api/student/profile/personal
# Body: Student entity fields (full_name, email, etc.)
# Response: Updated Student entity confirmation

GET /api/student/profile/study-plan
# Response: CurriculumVersion with CurriculumUnit layout and AcademicRecord completion status
# Includes: CurriculumVersion, CurriculumUnit[] with Unit details and completion tracking

PUT /api/student/profile/avatar
# Body: FormData with image file
# Response: Updated Student entity with new avatar URL

GET /api/student/profile/academic-history
# Response: Complete AcademicRecord history with Enrollment and AcademicStanding milestones
# Includes: AcademicRecord[], Enrollment[], AcademicStanding[] across all semesters
```

### 7. Curriculum and Program APIs

```http
GET /api/student/curriculum
# Query params: curriculum_version_id, program_id, specialization_id
# Response: CurriculumVersion with CurriculumUnit entities and Unit relationships
# Includes: Complete curriculum roadmap with Unit prerequisites and semester placement

GET /api/student/curriculum/prerequisites/{unit_id}
# Response: Unit prerequisite tree with AcademicRecord completion status
# Includes: Unit relationships and student completion tracking

GET /api/student/programs/{program_id}/requirements
# Response: Program graduation requirements with CurriculumVersion progress tracking
# Includes: Program, Specialization, CurriculumUnit requirements and completion status
```

### 8. Notification and Communication APIs

```http
GET /api/student/notifications
# Query params: type, category, read, page, limit
# Response: System-generated notifications for academic events
# Includes: Academic deadline alerts, grade notifications, hold notifications

PUT /api/student/notifications/{notification_id}/read
# Response: Mark notification as read confirmation

POST /api/student/notifications/push/subscribe
# Body: { subscription: PushSubscription }
# Response: Push notification subscription confirmation

DELETE /api/student/notifications/push/unsubscribe
# Response: Push notification unsubscription confirmation

GET /api/student/notifications/preferences
# Response: Student notification preferences and settings

PUT /api/student/notifications/preferences
# Body: { email_enabled, push_enabled, categories: [] }
# Response: Updated notification preferences
```

### 9. Academic Calendar and System APIs

```http
GET /api/student/semesters
# Query params: year, is_current
# Response: Semester entities with academic calendar information
# Includes: Semester[] with registration periods and important dates

GET /api/student/semesters/{semester_id}/deadlines
# Query params: date_range, type
# Response: Semester-specific deadlines and AssessmentComponentDetail due dates
# Includes: Academic deadlines, assessment due dates, registration periods

GET /api/student/academic-calendar
# Response: Academic events, holidays, and Semester schedules
# Includes: System-wide academic calendar with important dates
```

## API Response Standards

### Success Response Format

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data aligned with database entities
  },
  "meta": {
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "last_page": 5
    },
    "filters": {
      "applied": ["semester_id:uuid", "program_id:uuid"],
      "available": ["semester_id", "program_id", "specialization_id"]
    }
  }
}
```

### Dashboard Response Example

```json
{
  "success": true,
  "data": {
    "current_semester": {
      "id": "uuid",
      "name": "Fall 2024",
      "code": "2024-3",
      "start_date": "2024-09-01",
      "end_date": "2024-12-15"
    },
    "credit_progress": {
      "earned_credits": 45,
      "required_credits": 120,
      "remaining_requirements": [
        {
          "id": "uuid",
          "curriculum_version_id": "uuid",
          "unit": {
            "id": "uuid",
            "code": "CS301",
            "name": "Data Structures",
            "credit_points": 3
          },
          "semester": {
            "id": "uuid",
            "name": "Spring 2025"
          }
        }
      ],
      "completion_percentage": 37.5
    },
    "current_gpa": {
      "id": "uuid",
      "student_id": "uuid",
      "semester_id": "uuid",
      "program_id": "uuid",
      "semester_gpa": 3.45,
      "cumulative_gpa": 3.32,
      "trend": "up"
    },
    "academic_holds": [
      {
        "id": "uuid",
        "student_id": "uuid",
        "placed_by_user_id": "uuid",
        "placed_by": {
          "id": "uuid",
          "name": "Academic Office",
          "email": "<EMAIL>"
        }
      }
    ],
    "upcoming_assessments": [
      {
        "id": "uuid",
        "component_id": "uuid",
        "assessment_component": {
          "id": "uuid",
          "syllabus": {
            "curriculum_unit": {
              "unit": {
                "code": "CS101",
                "name": "Introduction to Programming"
              }
            }
          }
        }
      }
    ],
    "enrollment_status": {
      "id": "uuid",
      "student_id": "uuid",
      "semester_id": "uuid",
      "curriculum_version_id": "uuid"
    },
    "academic_standing": {
      "id": "uuid",
      "student_id": "uuid",
      "semester_id": "uuid"
    }
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "email": ["Email is required", "Email must be valid"],
    "course_id": ["Course not found"]
  },
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Authentication & Authorization

### JWT Token Structure

```json
{
  "sub": "student_uuid",
  "email": "<EMAIL>",
  "student_id": "STU123456",
  "role": "student",
  "campus_id": "campus_uuid",
  "program_id": "program_uuid",
  "permissions": ["view_grades", "register_courses", "view_transcript"],
  "iat": 1642234567,
  "exp": 1642320967
}
```

### Required Middleware

- Authentication verification
- Rate limiting (per endpoint)
- Request validation
- CORS handling
- Error logging and monitoring

## Database Indexes and Performance

### Critical Indexes

```sql
-- Student lookups
CREATE INDEX idx_students_email ON students(email);
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_students_program_campus ON students(program_id, campus_id);
CREATE INDEX idx_students_curriculum_version ON students(curriculum_version_id);

-- Course registration and offerings
CREATE INDEX idx_course_registrations_student_offering ON course_registrations(student_id, course_offering_id);
CREATE INDEX idx_course_registrations_semester ON course_registrations(semester_id);
CREATE INDEX idx_course_offerings_semester ON course_offerings(semester_id);
CREATE INDEX idx_course_offerings_curriculum_unit ON course_offerings(curriculum_unit_id);

-- Academic records and assessments
CREATE INDEX idx_academic_records_student_semester ON academic_records(student_id, semester_id);
CREATE INDEX idx_academic_records_course_offering ON academic_records(course_offering_id);
CREATE INDEX idx_assessment_scores_student ON assessment_component_detail_scores(student_id);
CREATE INDEX idx_assessment_scores_course_offering ON assessment_component_detail_scores(course_offering_id);

-- Attendance tracking
CREATE INDEX idx_attendances_student ON attendances(student_id);
CREATE INDEX idx_attendances_class_session ON attendances(class_session_id);
CREATE INDEX idx_class_sessions_course_offering ON class_sessions(course_offering_id);

-- GPA and academic standing
CREATE INDEX idx_gpa_calculations_student_semester ON gpa_calculations(student_id, semester_id);
CREATE INDEX idx_academic_standings_student_semester ON academic_standings(student_id, semester_id);

-- Academic holds and support
CREATE INDEX idx_academic_holds_student ON academic_holds(student_id);
CREATE INDEX idx_academic_holds_placed_by ON academic_holds(placed_by_user_id);

-- Curriculum structure
CREATE INDEX idx_curriculum_units_version ON curriculum_units(curriculum_version_id);
CREATE INDEX idx_curriculum_units_unit ON curriculum_units(unit_id);
CREATE INDEX idx_curriculum_units_semester ON curriculum_units(semester_id);
CREATE INDEX idx_curriculum_versions_program ON curriculum_versions(program_id);
CREATE INDEX idx_curriculum_versions_specialization ON curriculum_versions(specialization_id);

-- Enrollment tracking
CREATE INDEX idx_enrollments_student_semester ON enrollments(student_id, semester_id);
CREATE INDEX idx_enrollments_curriculum_version ON enrollments(curriculum_version_id);
```

## Data Validation Rules

### Student Registration Validation

- Maximum credits per semester (default: 18)
- Prerequisite completion verification
- Time conflict detection
- Hold status check
- Registration period validation

### Grade Calculation Rules

- GPA calculation: (Sum of grade points × credits) / Total credits
- Academic standing thresholds:
  - Good Standing: GPA ≥ 2.0
  - Academic Probation: GPA < 2.0
  - Academic Suspension: GPA < 1.5 for 2 consecutive semesters

### Attendance Requirements

- Minimum attendance: 75% for exam eligibility
- Warning threshold: 80%
- Automatic notifications when below thresholds

## Security Requirements

### Data Protection

- Encrypt sensitive personal information
- Hash and salt passwords (if local auth implemented)
- Secure file upload validation
- SQL injection prevention
- XSS protection

### API Security

- Rate limiting: 100 requests/minute per user
- Input validation and sanitization
- HTTPS enforcement
- CORS policy configuration
- Request logging for audit trails

## Testing Requirements

### Unit Tests Required

- All API endpoints with success/error scenarios
- Database model validations
- Business logic functions (GPA calculation, conflict detection)
- Authentication and authorization flows

### Integration Tests Required

- Complete user workflows (registration, grade viewing)
- Database transaction integrity
- File upload and processing
- Push notification delivery

### Performance Requirements

- API response time: < 500ms for 95% of requests
- Database query optimization
- Caching strategy for frequently accessed data
- File upload handling up to 10MB per file

## Deployment Considerations

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:pass@host:port/dbname
REDIS_URL=redis://host:port/db

# Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRY=24h
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Storage
STORAGE_DRIVER=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_BUCKET=your-bucket-name

# Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Email (for notifications)
MAIL_DRIVER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

### Infrastructure Requirements

- PostgreSQL 14+ with UUID extension
- Redis for caching and session storage
- File storage (AWS S3 or compatible)
- Background job processing (for notifications)
- SSL certificate for HTTPS
- CDN for static assets (optional)

This comprehensive backend specification should provide everything needed to implement the APIs that support your student portal frontend. Would you like me to elaborate on any specific section or add additional requirements?
