# 📘 PRD: Lecturer Portal — Assessments & Assessment Report

## 1. `/lecturer/teaching/courses/:id/assessments`

### Purpose

Manage all assessment components of a `course_offering`. Lecturers can view assessment structure, input grades, and monitor grading progress.

### Data Display

### 1.1 Assessment Components (from `assessment_components`)

- Fields: `name`, `code`, `type`, `weight`, `due_date`, `status`
- Flags: `is_published`, `scores_published`
- Linked via: `course_offering.curriculum_unit_id → syllabus.curriculum_unit_id → assessment_components.syllabus_id`

### 1.2 Assessment Details (from `assessment_component_details`)

- Fields: `name`, `weight`
- Stats: number of submissions, number graded, grading status
- Aggregated from: `assessment_component_detail_scores`

### 1.3 Grading Status Summary

- Count of scores in each state: `draft`, `provisional`, `final`
- Number of late submissions, suspected plagiarism, appeals, etc.

### Table Relationships

```
plaintext
CopyEdit
course_offerings → curriculum_unit_id
    → syllabus.id → assessment_components.syllabus_id
        → assessment_component_details.component_id
            → assessment_component_detail_scores.detail_id

```

### Actions

- Add new assessment component (if permitted)
- Grade by student or by component
- Change score status (`draft → final`)
- Export scores as Excel

## 2. `/lecturer/teaching/courses/:id/assessments/report`

### 🎯 Purpose

Summarize and visualize student performance across all assessments in the course — useful for final grading and progress evaluation.

### 📊 Data Display

### 2.1 Overview

- Total number of students
- Completion statistics per component
- Average / high / low score distribution

### 2.2 Grade Table (row per student)

| Student  | A1 (10%) | Quiz (20%) | Midterm (30%) | Final (40%) | Total | Grade |
| -------- | -------- | ---------- | ------------- | ----------- | ----- | ----- |
| Nguyen A | 9.0      | 17.5       | 26.0          | 34.0        | 86.5  | B+    |

✅ Final score is calculated from component weights

✅ Can map to `letter_grade` from `academic_records` (optional)

### Table Relationships

```
plaintext
CopyEdit
students → assessment_component_detail_scores
    ← assessment_component_details ← assessment_components
        ← syllabus ← curriculum_units ← course_offerings

```

- Optional link: `academic_records` for comparing or syncing final grades

### 📈 Optional Features

- Grade distribution chart
- Detect at-risk or exceptional students
- Export report as PDF / Excel
