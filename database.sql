create table campuses
(
    id         bigint unsigned auto_increment
        primary key,
    name       varchar(255) not null,
    code       varchar(255) not null,
    address    varchar(255) not null,
    created_at timestamp    null,
    updated_at timestamp    null,
    constraint campuses_code_unique
        unique (code)
)

create table buildings
(
    id          bigint unsigned auto_increment
        primary key,
    campus_id   bigint unsigned not null,
    name        varchar(100)    not null,
    code        varchar(20)     not null,
    description text            null,
    address     text            null,
    created_at  timestamp       null,
    updated_at  timestamp       null,
    deleted_at  timestamp       null,
    constraint buildings_code_unique
        unique (code),
    constraint buildings_campus_id_foreign
        foreign key (campus_id) references campuses (id)
)
create table lectures
(
    id                             bigint unsigned auto_increment
        primary key,
    employee_id                    varchar(20)                                                                                                                                                  not null,
    title                          varchar(10)                                                                                                                                                  null,
    first_name                     varchar(100)                                                                                                                                                 not null,
    last_name                      varchar(100)                                                                                                                                                 not null,
    email                          varchar(255)                                                                                                                                                 not null,
    phone                          varchar(20)                                                                                                                                                  null,
    mobile_phone                   varchar(20)                                                                                                                                                  null,
    campus_id                      bigint unsigned                                                                                                                                              not null,
    department                     varchar(100)                                                                                                                                                 null,
    faculty                        varchar(100)                                                                                                                                                 null,
    specialization                 varchar(255)                                                                                                                                                 null,
    expertise_areas                json                                                                                                                                                         null,
    academic_rank                  enum ('lecturer', 'senior_lecturer', 'associate_professor', 'professor', 'emeritus_professor', 'visiting_lecturer', 'adjunct_professor') default 'lecturer'  not null,
    highest_degree                 varchar(50)                                                                                                                                                  null,
    degree_field                   varchar(255)                                                                                                                                                 null,
    alma_mater                     varchar(255)                                                                                                                                                 null,
    graduation_year                year                                                                                                                                                         null,
    hire_date                      date                                                                                                                                                         not null,
    contract_start_date            date                                                                                                                                                         null,
    contract_end_date              date                                                                                                                                                         null,
    employment_type                enum ('full_time', 'part_time', 'contract', 'visiting', 'emeritus')                                                                      default 'full_time' not null,
    employment_status              enum ('active', 'on_leave', 'sabbatical', 'retired', 'terminated', 'suspended')                                                          default 'active'    not null,
    preferred_teaching_days        json                                                                                                                                                         null,
    preferred_start_time           time                                                                                                                                                         null,
    preferred_end_time             time                                                                                                                                                         null,
    max_teaching_hours_per_week    int                                                                                                                                      default 40          not null,
    teaching_modalities            json                                                                                                                                                         null,
    office_address                 text                                                                                                                                                         null,
    office_phone                   varchar(20)                                                                                                                                                  null,
    emergency_contact_name         text                                                                                                                                                         null,
    emergency_contact_phone        varchar(20)                                                                                                                                                  null,
    emergency_contact_relationship varchar(50)                                                                                                                                                  null,
    biography                      text                                                                                                                                                         null,
    certifications                 json                                                                                                                                                         null,
    languages                      json                                                                                                                                                         null,
    hourly_rate                    decimal(10, 2)                                                                                                                                               null,
    salary                         decimal(12, 2)                                                                                                                                               null,
    is_active                      tinyint(1)                                                                                                                               default 1           not null,
    can_teach_online               tinyint(1)                                                                                                                               default 1           not null,
    is_available_for_assignment    tinyint(1)                                                                                                                               default 1           not null,
    notes                          text                                                                                                                                                         null,
    created_at                     timestamp                                                                                                                                                    null,
    updated_at                     timestamp                                                                                                                                                    null,
    deleted_at                     timestamp                                                                                                                                                    null,
    constraint lectures_email_unique
        unique (email),
    constraint lectures_employee_id_unique
        unique (employee_id),
    constraint lectures_campus_id_foreign
        foreign key (campus_id) references campuses (id)
)

create table permissions
(
    id           bigint unsigned auto_increment
        primary key,
    name         varchar(255) not null,
    display_name varchar(255) null,
    code         varchar(255) null,
    description  varchar(255) null,
    module       varchar(255) null,
    parent_id    int          null,
    created_at   timestamp    null,
    updated_at   timestamp    null,
    constraint permissions_code_unique
        unique (code),
    constraint permissions_name_unique
        unique (name)
)

create table programs
(
    id          bigint unsigned auto_increment
        primary key,
    name        varchar(255) not null,
    code        varchar(255) not null,
    description text         null,
    created_at  timestamp    null,
    updated_at  timestamp    null,
    constraint programs_code_unique
        unique (code)
)

create table roles
(
    id                  bigint unsigned auto_increment
        primary key,
    name                varchar(255) not null,
    code                varchar(255) not null,
    bitwise_permissions json         null,
    created_at          timestamp    null,
    updated_at          timestamp    null,
    constraint roles_code_unique
        unique (code),
    constraint roles_name_unique
        unique (name)
)

create table role_permissions
(
    id            bigint unsigned auto_increment
        primary key,
    role_id       bigint unsigned not null,
    permission_id bigint unsigned not null,
    created_at    timestamp       null,
    updated_at    timestamp       null,
    constraint role_permissions_permission_id_foreign
        foreign key (permission_id) references permissions (id)
            on delete cascade,
    constraint role_permissions_role_id_foreign
        foreign key (role_id) references roles (id)
            on delete cascade
)

create table rooms
(
    id                bigint unsigned auto_increment
        primary key,
    campus_id         bigint unsigned                                                                                                                                            not null,
    name              varchar(100)                                                                                                                                               not null,
    code              varchar(20)                                                                                                                                                not null,
    building          varchar(50)                                                                                                                                                null,
    floor             varchar(10)                                                                                                                                                null,
    type              enum ('classroom', 'laboratory', 'computer_lab', 'auditorium', 'meeting_room', 'library', 'study_room', 'workshop', 'office', 'other') default 'classroom' not null,
    capacity          int                                                                                                                                    default 1           not null,
    status            enum ('available', 'occupied', 'maintenance', 'out_of_service', 'reserved')                                                            default 'available' not null,
    is_bookable       tinyint(1)                                                                                                                             default 1           not null,
    requires_approval tinyint(1)                                                                                                                             default 0           not null,
    available_from    time                                                                                                                                   default '07:00:00'  not null,
    available_until   time                                                                                                                                   default '18:00:00'  not null,
    blocked_days      json                                                                                                                                                       null,
    description       text                                                                                                                                                       null,
    usage_guidelines  text                                                                                                                                                       null,
    booking_notes     text                                                                                                                                                       null,
    created_at        timestamp                                                                                                                                                  null,
    updated_at        timestamp                                                                                                                                                  null,
    deleted_at        timestamp                                                                                                                                                  null,
    constraint rooms_code_unique
        unique (code),
    constraint unique_campus_room_code
        unique (campus_id, code),
    constraint rooms_campus_id_foreign
        foreign key (campus_id) references campuses (id),
    constraint check_available_times
        check (`available_from` < `available_until`),
    constraint check_capacity_positive
        check (`capacity` > 0)
)

create table room_bookings
(
    id                   bigint unsigned auto_increment
        primary key,
    room_id              bigint unsigned                                                                                                    not null,
    booked_by_type       varchar(255)                                                                                                       not null,
    booked_by_id         bigint unsigned                                                                                                    not null,
    approved_by_type     varchar(255)                                                                                                       null,
    approved_by_id       bigint unsigned                                                                                                    null,
    title                varchar(200)                                                                                                       not null,
    description          text                                                                                                               null,
    booking_date         date                                                                                                               not null,
    start_time           time                                                                                                               not null,
    end_time             time                                                                                                               not null,
    booking_type         enum ('class', 'exam', 'meeting', 'event', 'maintenance', 'personal_study', 'workshop', 'other') default 'meeting' not null,
    status               enum ('pending', 'approved', 'rejected', 'cancelled', 'completed')                               default 'pending' not null,
    priority             enum ('low', 'normal', 'high', 'urgent')                                                         default 'normal'  not null,
    is_recurring         tinyint(1)                                                                                       default 0         not null,
    recurrence_type      enum ('daily', 'weekly', 'biweekly', 'monthly')                                                                    null,
    recurrence_end_date  date                                                                                                               null,
    recurrence_days      json                                                                                                               null,
    parent_booking_id    bigint unsigned                                                                                                    null,
    required_equipment   json                                                                                                               null,
    setup_requirements   json                                                                                                               null,
    special_requirements text                                                                                                               null,
    contact_person       varchar(100)                                                                                                       null,
    contact_phone        varchar(20)                                                                                                        null,
    contact_email        varchar(255)                                                                                                       null,
    send_reminders       tinyint(1)                                                                                       default 1         not null,
    rejection_reason     text                                                                                                               null,
    admin_notes          text                                                                                                               null,
    approved_at          timestamp                                                                                                          null,
    cancelled_at         timestamp                                                                                                          null,
    created_at           timestamp                                                                                                          null,
    updated_at           timestamp                                                                                                          null,
    deleted_at           timestamp                                                                                                          null,
    constraint room_bookings_parent_booking_id_foreign
        foreign key (parent_booking_id) references room_bookings (id)
            on delete set null,
    constraint room_bookings_room_id_foreign
        foreign key (room_id) references rooms (id)
            on delete cascade,
    constraint check_booking_times
        check (`start_time` < `end_time`),
    constraint check_recurrence_end_date
        check ((`recurrence_end_date` is null) or (`recurrence_end_date` >= `booking_date`))
)

create table semesters
(
    id                    bigint unsigned auto_increment
        primary key,
    code                  varchar(255)         not null,
    name                  varchar(255)         not null,
    start_date            datetime             null,
    end_date              datetime             null,
    enrollment_start_date datetime             null,
    enrollment_end_date   datetime             null,
    is_active             tinyint(1) default 0 not null,
    is_archived           tinyint(1) default 0 not null,
    created_at            timestamp            null,
    updated_at            timestamp            null,
    deleted_at            timestamp            null
)

create table sessions
(
    id            varchar(255)    not null
        primary key,
    user_id       bigint unsigned null,
    ip_address    varchar(45)     null,
    user_agent    text            null,
    payload       longtext        not null,
    last_activity int             not null
)

create table specializations
(
    id          bigint unsigned auto_increment
        primary key,
    program_id  bigint unsigned      not null,
    name        varchar(255)         not null,
    code        varchar(255)         not null,
    description text                 null,
    is_active   tinyint(1) default 1 not null,
    created_at  timestamp            null,
    updated_at  timestamp            null,
    constraint specializations_code_unique
        unique (code),
    constraint specializations_program_id_name_unique
        unique (program_id, name),
    constraint specializations_program_id_foreign
        foreign key (program_id) references programs (id)
            on delete cascade
)

create table curriculum_versions
(
    id                bigint unsigned auto_increment
        primary key,
    program_id        bigint unsigned not null,
    specialization_id bigint unsigned null,
    version_code      varchar(20)     null,
    semester_id       bigint unsigned not null,
    notes             text            null,
    created_at        timestamp       null,
    updated_at        timestamp       null,
    constraint curriculum_versions_program_id_foreign
        foreign key (program_id) references programs (id),
    constraint curriculum_versions_semester_id_foreign
        foreign key (semester_id) references semesters (id),
    constraint curriculum_versions_specialization_id_foreign
        foreign key (specialization_id) references specializations (id)
            on delete cascade
)
create table graduation_requirements
(
    id                             bigint unsigned auto_increment
        primary key,
    program_id                     bigint unsigned            not null,
    specialization_id              bigint unsigned            null,
    total_credits_required         decimal(5, 2)              not null,
    core_credits_required          decimal(5, 2) default 0.00 not null,
    major_credits_required         decimal(5, 2) default 0.00 not null,
    elective_credits_required      decimal(5, 2) default 0.00 not null,
    minimum_gpa                    decimal(3, 2) default 2.00 not null,
    minimum_major_gpa              decimal(3, 2) default 2.00 not null,
    maximum_study_years            int           default 6    not null,
    required_internship            tinyint(1)    default 0    not null,
    required_thesis                tinyint(1)    default 0    not null,
    required_english_certification tinyint(1)    default 0    not null,
    special_requirements           json                       null,
    effective_from                 date                       not null,
    effective_to                   date                       null,
    is_active                      tinyint(1)    default 1    not null,
    created_at                     timestamp                  null,
    updated_at                     timestamp                  null,
    constraint graduation_requirements_program_id_foreign
        foreign key (program_id) references programs (id)
            on delete cascade,
    constraint graduation_requirements_specialization_id_foreign
        foreign key (specialization_id) references specializations (id)
            on delete cascade
)
create table units
(
    id            bigint unsigned auto_increment
        primary key,
    code          varchar(255)  not null,
    name          varchar(255)  not null,
    credit_points decimal(8, 2) not null,
    created_at    timestamp     null,
    updated_at    timestamp     null,
    constraint units_code_unique
        unique (code)
)

create table curriculum_units
(
    id                    bigint unsigned auto_increment
        primary key,
    curriculum_version_id bigint unsigned                    not null,
    unit_id               bigint unsigned                    not null,
    semester_id           bigint unsigned                    null,
    type                  enum ('core', 'major', 'elective') not null,
    unit_scope            varchar(255) default 'program'     not null comment 'Scope of the unit: program, common, specialization_specific, cross_program',
    year_level            tinyint unsigned                   null comment 'Academic year level (1-3)',
    semester_number       tinyint unsigned                   null comment 'Suggested semester number within the course (1-9)',
    is_required           tinyint(1)   default 1             not null,
    minimum_grade         decimal(4, 2)                      null comment 'Minimum grade required for completion (optional)',
    note                  text                               null,
    created_at            timestamp                          null,
    updated_at            timestamp                          null,
    constraint curriculum_unit_unique
        unique (curriculum_version_id, unit_id),
    constraint curriculum_units_curriculum_version_id_foreign
        foreign key (curriculum_version_id) references curriculum_versions (id)
            on delete cascade,
    constraint curriculum_units_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete cascade,
    constraint curriculum_units_unit_id_foreign
        foreign key (unit_id) references units (id)
            on delete cascade
)
create table course_offerings
(
    id                      bigint unsigned auto_increment
        primary key,
    semester_id             bigint unsigned                                                           not null,
    curriculum_unit_id      bigint unsigned                                                           not null,
    lecture_id              bigint unsigned                                                           null,
    section_code            varchar(10)                                                               null,
    max_capacity            int                                                   default 1000        not null,
    current_enrollment      int                                                   default 0           not null,
    waitlist_capacity       int                                                   default 10          not null,
    current_waitlist        int                                                   default 0           not null,
    delivery_mode           enum ('in_person', 'online', 'hybrid', 'blended')     default 'in_person' not null,
    schedule_days           json                                                                      null,
    schedule_time_start     time                                                                      null,
    schedule_time_end       time                                                                      null,
    location                varchar(255)                                                              null,
    is_active               tinyint(1)                                            default 1           not null,
    enrollment_status       enum ('open', 'closed', 'waitlist_only', 'cancelled') default 'open'      not null,
    registration_start_date date                                                                      null,
    registration_end_date   date                                                                      null,
    special_requirements    text                                                                      null,
    notes                   text                                                                      null,
    created_at              timestamp                                                                 null,
    updated_at              timestamp                                                                 null,
    deleted_at              timestamp                                                                 null,
    constraint unique_semester_unit_section
        unique (semester_id, curriculum_unit_id, section_code),
    constraint course_offerings_curriculum_unit_id_foreign
        foreign key (curriculum_unit_id) references curriculum_units (id)
            on delete cascade,
    constraint course_offerings_lecture_id_foreign
        foreign key (lecture_id) references lectures (id)
            on delete set null,
    constraint course_offerings_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete cascade
)
create table class_sessions
(
    id                           bigint unsigned auto_increment
        primary key,
    course_offering_id           bigint unsigned                                                                                                                                                            not null,
    room_id                      bigint unsigned                                                                                                                                                            null,
    room_booking_id              bigint unsigned                                                                                                                                                            null,
    instructor_id                bigint unsigned                                                                                                                                                            null,
    session_title                varchar(200)                                                                                                                                                               null,
    session_description          text                                                                                                                                                                       null,
    session_date                 date                                                                                                                                                                       not null,
    start_time                   time                                                                                                                                                                       not null,
    end_time                     time                                                                                                                                                                       not null,
    duration_minutes             int                                                                                                                                                                        null,
    session_type                 enum ('lecture', 'tutorial', 'practical', 'laboratory', 'seminar', 'workshop', 'exam', 'assessment', 'field_trip', 'guest_lecture', 'review', 'other') default 'lecture'   not null,
    delivery_mode                enum ('in_person', 'online', 'hybrid', 'blended')                                                                                                      default 'in_person' not null,
    status                       enum ('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed', 'moved')                                                                      default 'scheduled' not null,
    online_meeting_url           varchar(500)                                                                                                                                                               null,
    meeting_id                   varchar(100)                                                                                                                                                               null,
    meeting_password             varchar(100)                                                                                                                                                               null,
    learning_objectives          json                                                                                                                                                                       null,
    required_materials           json                                                                                                                                                                       null,
    topics_covered               json                                                                                                                                                                       null,
    attendance_required          tinyint(1)                                                                                                                                             default 1           not null,
    attendance_tracking_enabled  tinyint(1)                                                                                                                                             default 1           not null,
    expected_attendees           int                                                                                                                                                                        null,
    actual_attendees             int                                                                                                                                                                        null,
    attendance_percentage        decimal(5, 2)                                                                                                                                                              null,
    is_assessment                tinyint(1)                                                                                                                                             default 0           not null,
    assessment_weight            decimal(5, 2)                                                                                                                                                              null,
    assessment_duration_minutes  int                                                                                                                                                                        null,
    assessment_materials_allowed json                                                                                                                                                                       null,
    is_recurring                 tinyint(1)                                                                                                                                             default 0           not null,
    parent_session_id            bigint unsigned                                                                                                                                                            null,
    sequence_number              int                                                                                                                                                                        null,
    instructor_notes             text                                                                                                                                                                       null,
    admin_notes                  text                                                                                                                                                                       null,
    student_instructions         text                                                                                                                                                                       null,
    cancellation_reason          text                                                                                                                                                                       null,
    scheduled_at                 timestamp                                                                                                                                                                  null,
    started_at                   timestamp                                                                                                                                                                  null,
    ended_at                     timestamp                                                                                                                                                                  null,
    cancelled_at                 timestamp                                                                                                                                                                  null,
    created_at                   timestamp                                                                                                                                                                  null,
    updated_at                   timestamp                                                                                                                                                                  null,
    deleted_at                   timestamp                                                                                                                                                                  null,
    constraint unique_co_sequence
        unique (course_offering_id, sequence_number),
    constraint class_sessions_course_offering_id_foreign
        foreign key (course_offering_id) references course_offerings (id)
            on delete cascade,
    constraint class_sessions_instructor_id_foreign
        foreign key (instructor_id) references lectures (id)
            on delete set null,
    constraint class_sessions_parent_session_id_foreign
        foreign key (parent_session_id) references class_sessions (id)
            on delete set null,
    constraint class_sessions_room_booking_id_foreign
        foreign key (room_booking_id) references room_bookings (id)
            on delete set null,
    constraint class_sessions_room_id_foreign
        foreign key (room_id) references rooms (id)
            on delete set null,
    constraint check_sessions_actual_attendees_valid
        check ((`actual_attendees` is null) or (`actual_attendees` >= 0)),
    constraint check_sessions_assessment_weight
        check ((`assessment_weight` is null) or ((`assessment_weight` >= 0) and (`assessment_weight` <= 100))),
    constraint check_sessions_attendance_percentage
        check ((`attendance_percentage` is null) or
               ((`attendance_percentage` >= 0) and (`attendance_percentage` <= 100))),
    constraint check_sessions_expected_attendees_positive
        check ((`expected_attendees` is null) or (`expected_attendees` > 0)),
    constraint check_sessions_times
        check (`start_time` < `end_time`)
)

create table equivalent_units
(
    id                 bigint unsigned auto_increment
        primary key,
    unit_id            bigint unsigned not null,
    equivalent_unit_id bigint unsigned not null,
    reason             varchar(255)    null,
    created_at         timestamp       null,
    updated_at         timestamp       null,
    constraint equivalent_units_unit_id_equivalent_unit_id_unique
        unique (unit_id, equivalent_unit_id),
    constraint equivalent_units_equivalent_unit_id_foreign
        foreign key (equivalent_unit_id) references units (id)
            on delete cascade,
    constraint equivalent_units_unit_id_foreign
        foreign key (unit_id) references units (id)
            on delete cascade
)
create table syllabus
(
    id                 bigint unsigned auto_increment
        primary key,
    curriculum_unit_id bigint unsigned      not null,
    version            varchar(30)          null,
    description        text                 null,
    total_hours        int                  null,
    hours_per_session  int                  null,
    is_active          tinyint(1) default 1 not null,
    created_at         timestamp            null,
    updated_at         timestamp            null,
    constraint syllabus_curriculum_unit_id_is_active_unique
        unique (curriculum_unit_id, is_active),
    constraint syllabus_curriculum_unit_id_foreign
        foreign key (curriculum_unit_id) references curriculum_units (id)
            on delete cascade
)
create table assessment_components
(
    id                            bigint unsigned auto_increment
        primary key,
    syllabus_id                   bigint unsigned                                                                                  not null,
    name                          varchar(100)                                                                                     null,
    code                          varchar(20)                                                                                      null,
    description                   text                                                                                             null,
    weight                        decimal(5, 2)                                                                                    null,
    type                          enum ('quiz', 'assignment', 'project', 'exam', 'online_activity', 'other')                       not null,
    is_required_to_sit_final_exam tinyint(1)                                                                      default 1        not null,
    created_at                    timestamp                                                                                        null,
    updated_at                    timestamp                                                                                        null,
    due_date                      datetime                                                                                         null,
    available_from                datetime                                                                                         null,
    late_submission_deadline      datetime                                                                                         null,
    late_penalty_percentage       decimal(5, 2)                                                                   default 0.00     not null,
    late_penalty_type             enum ('per_day', 'per_hour', 'fixed', 'none')                                   default 'none'   not null,
    submission_type               enum ('online', 'in_person', 'both', 'no_submission')                           default 'online' not null,
    allowed_file_types            json                                                                                             null,
    max_file_size_mb              int                                                                                              null,
    max_submissions               int                                                                             default 1        not null,
    allow_resubmission            tinyint(1)                                                                      default 0        not null,
    is_group_work                 tinyint(1)                                                                      default 0        not null,
    min_group_size                int                                                                                              null,
    max_group_size                int                                                                                              null,
    students_form_groups          tinyint(1)                                                                      default 1        not null,
    assessment_criteria           json                                                                                             null,
    grading_instructions          text                                                                                             null,
    is_published                  tinyint(1)                                                                      default 0        not null,
    scores_published              tinyint(1)                                                                      default 0        not null,
    is_extra_credit               tinyint(1)                                                                      default 0        not null,
    status                        enum ('draft', 'published', 'in_progress', 'grading', 'completed', 'cancelled') default 'draft'  not null,
    sort_order                    int                                                                             default 0        not null,
    category                      varchar(50)                                                                                      null,
    constraint unique_syllabus_component_code
        unique (syllabus_id, code),
    constraint assessment_components_syllabus_id_foreign
        foreign key (syllabus_id) references syllabus (id)
            on delete cascade,
    constraint check_file_size_positive
        check ((`max_file_size_mb` is null) or (`max_file_size_mb` > 0)),
    constraint check_group_size_valid
        check (((`min_group_size` is null) or (`min_group_size` >= 1)) and
               ((`max_group_size` is null) or (`max_group_size` >= 1)) and
               ((`min_group_size` is null) or (`max_group_size` is null) or (`min_group_size` <= `max_group_size`))),
    constraint check_late_penalty_percentage
        check ((`late_penalty_percentage` >= 0) and (`late_penalty_percentage` <= 100)),
    constraint check_max_submissions_positive
        check (`max_submissions` >= 1),
    constraint check_weight_percentage
        check ((`weight` >= 0) and (`weight` <= 100))
)

create table assessment_component_details
(
    id           bigint unsigned auto_increment
        primary key,
    component_id bigint unsigned not null,
    name         varchar(100)    not null,
    weight       decimal(5, 2)   null,
    created_at   timestamp       null,
    updated_at   timestamp       null,
    constraint assessment_component_details_component_id_foreign
        foreign key (component_id) references assessment_components (id)
            on delete cascade
)

create table unit_prerequisite_groups
(
    id             bigint unsigned auto_increment
        primary key,
    unit_id        bigint unsigned                  not null,
    logic_operator enum ('AND', 'OR') default 'AND' not null,
    description    text                             null,
    created_at     timestamp                        null,
    updated_at     timestamp                        null,
    constraint unit_prerequisite_groups_unit_id_foreign
        foreign key (unit_id) references units (id)
            on delete cascade
)

create table unit_prerequisite_conditions
(
    id               bigint unsigned auto_increment
        primary key,
    group_id         bigint unsigned                                                                                                                                    not null,
    type             enum ('prerequisite', 'co_requisite', 'concurrent', 'anti_requisite', 'assumed_knowledge', 'credit_requirement', 'textual') default 'prerequisite' not null,
    required_unit_id bigint unsigned                                                                                                                                    null,
    required_credits int                                                                                                                                                null,
    free_text        text                                                                                                                                               null,
    created_at       timestamp                                                                                                                                          null,
    updated_at       timestamp                                                                                                                                          null,
    constraint unit_prerequisite_conditions_group_id_foreign
        foreign key (group_id) references unit_prerequisite_groups (id)
            on delete cascade,
    constraint unit_prerequisite_conditions_required_unit_id_foreign
        foreign key (required_unit_id) references units (id)
            on delete cascade
)

create table users
(
    id                bigint unsigned auto_increment
        primary key,
    name              varchar(255) not null,
    email             varchar(255) not null,
    phone             varchar(20)  null,
    address           varchar(500) null,
    email_verified_at timestamp    null,
    password          varchar(255) not null,
    remember_token    varchar(100) null,
    created_at        timestamp    null,
    updated_at        timestamp    null,
    constraint users_email_unique
        unique (email)
)

create table campus_user_roles
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint unsigned not null,
    campus_id  bigint unsigned not null,
    role_id    bigint unsigned not null,
    created_at timestamp       null,
    updated_at timestamp       null,
    constraint campus_user_roles_campus_id_foreign
        foreign key (campus_id) references campuses (id)
            on delete cascade,
    constraint campus_user_roles_role_id_foreign
        foreign key (role_id) references roles (id)
            on delete cascade,
    constraint campus_user_roles_user_id_foreign
        foreign key (user_id) references users (id)
            on delete cascade
)

create table students
(
    id                             bigint unsigned auto_increment
        primary key,
    student_id                     varchar(20)                                                                             not null,
    full_name                      varchar(100)                                                                            not null,
    email                          varchar(255)                                                                            not null,
    phone                          varchar(20)                                                                             null,
    oauth_provider                 enum ('google', 'microsoft', 'manual')                             default 'google'     not null,
    oauth_provider_id              varchar(255)                                                                            null,
    avatar_url                     varchar(500)                                                                            null,
    date_of_birth                  date                                                                                    null,
    gender                         enum ('male', 'female', 'other')                                                        null,
    nationality                    varchar(100)                                                       default 'Vietnamese' not null,
    national_id                    varchar(20)                                                                             null,
    address                        text                                                                                    null,
    campus_id                      bigint unsigned                                                                         not null,
    program_id                     bigint unsigned                                                                         not null,
    specialization_id              bigint unsigned                                                                         null,
    curriculum_version_id          bigint unsigned                                                                         not null,
    admission_date                 date                                                                                    not null,
    expected_graduation_date       date                                                                                    null,
    emergency_contact_name         varchar(255)                                                                            null,
    emergency_contact_phone        varchar(20)                                                                             null,
    emergency_contact_relationship varchar(100)                                                                            null,
    high_school_name               varchar(255)                                                                            null,
    high_school_graduation_year    year                                                                                    null,
    entrance_exam_score            decimal(5, 2)                                                                           null,
    admission_notes                text                                                                                    null,
    status                         enum ('active', 'inactive', 'suspended', 'graduated')              default 'active'     not null,
    last_login_at                  timestamp                                                                               null,
    email_verified_at              timestamp                                                                               null,
    remember_token                 varchar(100)                                                                            null,
    created_at                     timestamp                                                                               null,
    updated_at                     timestamp                                                                               null,
    deleted_at                     timestamp                                                                               null,
    academic_status                enum ('active', 'inactive', 'graduated', 'suspended', 'withdrawn') default 'active'     not null,
    status_change_date             date                                                                                    null,
    status_reason                  text                                                                                    null,
    status_changed_by              bigint unsigned                                                                         null,
    constraint students_email_unique
        unique (email),
    constraint students_national_id_unique
        unique (national_id),
    constraint students_student_id_unique
        unique (student_id),
    constraint students_campus_id_foreign
        foreign key (campus_id) references campuses (id),
    constraint students_curriculum_version_id_foreign
        foreign key (curriculum_version_id) references curriculum_versions (id),
    constraint students_program_id_foreign
        foreign key (program_id) references programs (id),
    constraint students_specialization_id_foreign
        foreign key (specialization_id) references specializations (id)
            on delete set null,
    constraint students_status_changed_by_foreign
        foreign key (status_changed_by) references users (id)
            on delete set null
)

create table academic_holds
(
    id                  bigint unsigned auto_increment
        primary key,
    student_id          bigint unsigned                                                                       not null,
    hold_type           enum ('financial', 'academic', 'disciplinary', 'administrative', 'health', 'library') not null,
    hold_category       enum ('registration', 'graduation', 'transcript', 'all') default 'registration'       not null,
    title               varchar(255)                                                                          not null,
    description         text                                                                                  null,
    amount              decimal(10, 2)                                                                        null,
    priority            enum ('high', 'medium', 'low')                           default 'medium'             not null,
    status              enum ('active', 'resolved', 'waived', 'expired')         default 'active'             not null,
    placed_date         date                                                                                  not null,
    due_date            date                                                                                  null,
    resolved_date       date                                                                                  null,
    placed_by_user_id   bigint unsigned                                                                       null,
    resolved_by_user_id bigint unsigned                                                                       null,
    resolution_notes    text                                                                                  null,
    created_at          timestamp                                                                             null,
    updated_at          timestamp                                                                             null,
    constraint academic_holds_placed_by_user_id_foreign
        foreign key (placed_by_user_id) references users (id)
            on delete set null,
    constraint academic_holds_resolved_by_user_id_foreign
        foreign key (resolved_by_user_id) references users (id)
            on delete set null,
    constraint academic_holds_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade
)

create table academic_records
(
    id                             bigint unsigned auto_increment
        primary key,
    student_id                     bigint unsigned                                                                                                                                       not null,
    course_offering_id             bigint unsigned                                                                                                                                       not null,
    semester_id                    bigint unsigned                                                                                                                                       not null,
    unit_id                        bigint unsigned                                                                                                                                       not null,
    program_id                     bigint unsigned                                                                                                                                       not null,
    campus_id                      bigint unsigned                                                                                                                                       not null,
    final_percentage               decimal(5, 2)                                                                                                                                         null,
    final_letter_grade             varchar(5)                                                                                                                                            null,
    grade_points                   decimal(3, 2)                                                                                                                                         null,
    quality_points                 decimal(6, 2)                                                                                                                                         null,
    credit_hours                   decimal(4, 2)                                                                                                                                         not null,
    credit_hours_earned            decimal(4, 2)                                                                                                                   default 0.00          not null,
    grade_status                   enum ('in_progress', 'provisional', 'final', 'incomplete', 'withdrawn', 'failed', 'pass_no_credit', 'audit', 'transfer_credit') default 'in_progress' not null,
    completion_status              enum ('enrolled', 'completed', 'withdrawn', 'failed', 'incomplete', 'in_progress')                                              default 'enrolled'    not null,
    enrollment_date                date                                                                                                                                                  not null,
    completion_date                date                                                                                                                                                  null,
    grade_submission_date          date                                                                                                                                                  null,
    grade_finalized_date           date                                                                                                                                                  null,
    attendance_percentage          decimal(5, 2)                                                                                                                                         null,
    total_absences                 int                                                                                                                             default 0             not null,
    total_class_sessions           int                                                                                                                                                   null,
    meets_attendance_requirement   tinyint(1)                                                                                                                      default 1             not null,
    is_repeat_course               tinyint(1)                                                                                                                      default 0             not null,
    attempt_number                 int                                                                                                                             default 1             not null,
    original_record_id             bigint unsigned                                                                                                                                       null,
    is_transfer_credit             tinyint(1)                                                                                                                      default 0             not null,
    transfer_institution           varchar(200)                                                                                                                                          null,
    transfer_course_code           varchar(50)                                                                                                                                           null,
    transfer_course_title          varchar(200)                                                                                                                                          null,
    is_advanced_placement          tinyint(1)                                                                                                                      default 0             not null,
    is_challenge_exam              tinyint(1)                                                                                                                      default 0             not null,
    is_credit_by_exam              tinyint(1)                                                                                                                      default 0             not null,
    grade_breakdown                json                                                                                                                                                  null,
    raw_percentage                 decimal(5, 2)                                                                                                                                         null,
    curve_adjustment               decimal(5, 2)                                                                                                                   default 0.00          not null,
    grade_adjustment_reason        text                                                                                                                                                  null,
    excluded_from_gpa              tinyint(1)                                                                                                                      default 0             not null,
    gpa_exclusion_reason           text                                                                                                                                                  null,
    instructor_comments            text                                                                                                                                                  null,
    administrative_notes           text                                                                                                                                                  null,
    instructor_id                  bigint unsigned                                                                                                                                       null,
    grade_submitted_by_lecture_id  bigint unsigned                                                                                                                                       null,
    grade_approved_by_lecture_id   bigint unsigned                                                                                                                                       null,
    affects_academic_standing      tinyint(1)                                                                                                                      default 1             not null,
    affects_graduation_requirement tinyint(1)                                                                                                                      default 1             not null,
    satisfies_prerequisite         tinyint(1)                                                                                                                      default 1             not null,
    grade_history                  json                                                                                                                                                  null,
    last_grade_change_at           timestamp                                                                                                                                             null,
    last_changed_by_lecture_id     bigint unsigned                                                                                                                                       null,
    created_at                     timestamp                                                                                                                                             null,
    updated_at                     timestamp                                                                                                                                             null,
    deleted_at                     timestamp                                                                                                                                             null,
    constraint unique_student_course_offering
        unique (student_id, course_offering_id),
    constraint academic_records_campus_id_foreign
        foreign key (campus_id) references campuses (id),
    constraint academic_records_course_offering_id_foreign
        foreign key (course_offering_id) references course_offerings (id)
            on delete cascade,
    constraint academic_records_grade_approved_by_lecture_id_foreign
        foreign key (grade_approved_by_lecture_id) references lectures (id)
            on delete set null,
    constraint academic_records_grade_submitted_by_lecture_id_foreign
        foreign key (grade_submitted_by_lecture_id) references lectures (id)
            on delete set null,
    constraint academic_records_instructor_id_foreign
        foreign key (instructor_id) references lectures (id)
            on delete set null,
    constraint academic_records_last_changed_by_lecture_id_foreign
        foreign key (last_changed_by_lecture_id) references lectures (id)
            on delete set null,
    constraint academic_records_original_record_id_foreign
        foreign key (original_record_id) references academic_records (id)
            on delete set null,
    constraint academic_records_program_id_foreign
        foreign key (program_id) references programs (id),
    constraint academic_records_semester_id_foreign
        foreign key (semester_id) references semesters (id),
    constraint academic_records_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade,
    constraint academic_records_unit_id_foreign
        foreign key (unit_id) references units (id),
    constraint check_records_attempt_number_positive
        check (`attempt_number` >= 1),
    constraint check_records_attendance_percentage
        check ((`attendance_percentage` is null) or
               ((`attendance_percentage` >= 0) and (`attendance_percentage` <= 100))),
    constraint check_records_credit_hours_earned
        check ((`credit_hours_earned` >= 0) and (`credit_hours_earned` <= `credit_hours`)),
    constraint check_records_credit_hours_positive
        check (`credit_hours` > 0),
    constraint check_records_curve_adjustment
        check ((`curve_adjustment` >= -(100)) and (`curve_adjustment` <= 100)),
    constraint check_records_final_percentage
        check ((`final_percentage` is null) or ((`final_percentage` >= 0) and (`final_percentage` <= 100))),
    constraint check_records_grade_points
        check ((`grade_points` is null) or ((`grade_points` >= 0) and (`grade_points` <= 4))),
    constraint check_records_quality_points
        check ((`quality_points` is null) or (`quality_points` >= 0))
)

create table academic_standings
(
    id                      bigint unsigned auto_increment
        primary key,
    student_id              bigint unsigned                                                   not null,
    semester_id             bigint unsigned                                                   not null,
    standing                enum ('good', 'probation', 'suspension', 'honors') default 'good' not null,
    gpa                     decimal(4, 2)                                                     not null,
    cumulative_gpa          decimal(4, 2)                                                     null,
    total_credits_completed int                                                default 0      not null,
    reason                  text                                                              null,
    notes                   text                                                              null,
    is_active               tinyint(1)                                         default 1      not null,
    effective_date          timestamp                                                         not null,
    created_by              bigint unsigned                                                   null,
    created_at              timestamp                                                         null,
    updated_at              timestamp                                                         null,
    constraint academic_standings_student_id_semester_id_unique
        unique (student_id, semester_id),
    constraint academic_standings_created_by_foreign
        foreign key (created_by) references users (id)
            on delete set null,
    constraint academic_standings_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete cascade,
    constraint academic_standings_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade
)

create table assessment_component_detail_scores
(
    id                             bigint unsigned auto_increment
        primary key,
    assessment_component_detail_id bigint unsigned                                                                                                                                         not null,
    student_id                     bigint unsigned                                                                                                                                         not null,
    course_offering_id             bigint unsigned                                                                                                                                         not null,
    graded_by_lecture_id           bigint unsigned                                                                                                                                         null,
    points_earned                  decimal(8, 2)                                                                                                                                           null,
    percentage_score               decimal(5, 2)                                                                                                                                           null,
    letter_grade                   varchar(5)                                                                                                                                              null,
    gpa_points                     decimal(3, 2)                                                                                                                                           null,
    submitted_at                   timestamp                                                                                                                                               null,
    graded_at                      timestamp                                                                                                                                               null,
    submission_attempt             int                                                                                                                             default 1               not null,
    submission_files               json                                                                                                                                                    null,
    submission_text                longtext                                                                                                                                                null,
    submission_url                 varchar(500)                                                                                                                                            null,
    is_late                        tinyint(1)                                                                                                                      default 0               not null,
    minutes_late                   int                                                                                                                             default 0               not null,
    late_penalty_applied           decimal(5, 2)                                                                                                                   default 0.00            not null,
    late_excuse                    text                                                                                                                                                    null,
    late_excuse_approved           tinyint(1)                                                                                                                      default 0               not null,
    status                         enum ('not_submitted', 'submitted', 'grading', 'graded', 'returned', 'resubmit_required', 'excused', 'incomplete', 'cancelled') default 'not_submitted' not null,
    score_status                   enum ('draft', 'provisional', 'final', 'disputed', 'under_review')                                                              default 'draft'         not null,
    instructor_feedback            longtext                                                                                                                                                null,
    private_notes                  longtext                                                                                                                                                null,
    rubric_scores                  json                                                                                                                                                    null,
    bonus_points                   decimal(8, 2)                                                                                                                   default 0.00            not null,
    bonus_reason                   text                                                                                                                                                    null,
    plagiarism_suspected           tinyint(1)                                                                                                                      default 0               not null,
    plagiarism_score               decimal(5, 2)                                                                                                                                           null,
    plagiarism_notes               text                                                                                                                                                    null,
    integrity_status               enum ('clear', 'under_investigation', 'violation_confirmed', 'violation_minor', 'violation_major')                              default 'clear'         not null,
    score_history                  json                                                                                                                                                    null,
    last_modified_at               timestamp                                                                                                                                               null,
    last_modified_by_lecture_id    bigint unsigned                                                                                                                                         null,
    is_extra_credit                tinyint(1)                                                                                                                      default 0               not null,
    is_makeup                      tinyint(1)                                                                                                                      default 0               not null,
    special_circumstances          text                                                                                                                                                    null,
    score_excluded                 tinyint(1)                                                                                                                      default 0               not null,
    exclusion_reason               text                                                                                                                                                    null,
    student_comments               text                                                                                                                                                    null,
    appeal_requested               tinyint(1)                                                                                                                      default 0               not null,
    appeal_requested_at            timestamp                                                                                                                                               null,
    appeal_reason                  text                                                                                                                                                    null,
    appeal_status                  enum ('none', 'pending', 'under_review', 'approved', 'denied')                                                                  default 'none'          not null,
    created_at                     timestamp                                                                                                                                               null,
    updated_at                     timestamp                                                                                                                                               null,
    deleted_at                     timestamp                                                                                                                                               null,
    constraint unique_detail_student_course_attempt
        unique (assessment_component_detail_id, student_id, course_offering_id, submission_attempt),
    constraint assessment_component_detail_scores_course_offering_id_foreign
        foreign key (course_offering_id) references course_offerings (id)
            on delete cascade,
    constraint assessment_component_detail_scores_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade,
    constraint detail_scores_detail_id_fk
        foreign key (assessment_component_detail_id) references assessment_component_details (id)
            on delete cascade,
    constraint detail_scores_graded_by_fk
        foreign key (graded_by_lecture_id) references lectures (id)
            on delete set null,
    constraint detail_scores_modified_by_fk
        foreign key (last_modified_by_lecture_id) references lectures (id)
            on delete set null,
    constraint check_scores_gpa_points
        check ((`gpa_points` is null) or ((`gpa_points` >= 0) and (`gpa_points` <= 4))),
    constraint check_scores_late_penalty_applied
        check ((`late_penalty_applied` >= 0) and (`late_penalty_applied` <= 100)),
    constraint check_scores_minutes_late_positive
        check (`minutes_late` >= 0),
    constraint check_scores_percentage_score
        check ((`percentage_score` is null) or ((`percentage_score` >= 0) and (`percentage_score` <= 100))),
    constraint check_scores_plagiarism_score
        check ((`plagiarism_score` is null) or ((`plagiarism_score` >= 0) and (`plagiarism_score` <= 100))),
    constraint check_scores_submission_attempt_positive
        check (`submission_attempt` >= 1)
)

create table attendances
(
    id                     bigint unsigned auto_increment
        primary key,
    class_session_id       bigint unsigned                                                                                                       not null,
    student_id             bigint unsigned                                                                                                       not null,
    recorded_by_lecture_id bigint unsigned                                                                                                       null,
    status                 enum ('present', 'absent', 'late', 'excused', 'partial', 'medical_leave', 'official_leave')          default 'absent' not null,
    check_in_time          timestamp                                                                                                             null,
    check_out_time         timestamp                                                                                                             null,
    minutes_late           int                                                                                                  default 0        not null,
    minutes_present        int                                                                                                                   null,
    recording_method       enum ('manual', 'qr_code', 'rfid', 'biometric', 'mobile_app', 'online_participation', 'auto_system') default 'manual' not null,
    notes                  text                                                                                                                  null,
    excuse_reason          text                                                                                                                  null,
    excuse_document_path   varchar(500)                                                                                                          null,
    participation_level    enum ('excellent', 'good', 'average', 'poor', 'none')                                                                 null,
    participation_score    decimal(3, 1)                                                                                                         null,
    participation_notes    text                                                                                                                  null,
    is_verified            tinyint(1)                                                                                           default 0        not null,
    affects_grade          tinyint(1)                                                                                           default 1        not null,
    is_makeup_allowed      tinyint(1)                                                                                           default 0        not null,
    verified_at            timestamp                                                                                                             null,
    verified_by_lecture_id bigint unsigned                                                                                                       null,
    batch_id               varchar(50)                                                                                                           null,
    device_info            json                                                                                                                  null,
    ip_address             varchar(45)                                                                                                           null,
    latitude               decimal(10, 8)                                                                                                        null,
    longitude              decimal(11, 8)                                                                                                        null,
    created_at             timestamp                                                                                                             null,
    updated_at             timestamp                                                                                                             null,
    deleted_at             timestamp                                                                                                             null,
    constraint unique_session_student_attendance
        unique (class_session_id, student_id),
    constraint attendances_class_session_id_foreign
        foreign key (class_session_id) references class_sessions (id)
            on delete cascade,
    constraint attendances_recorded_by_lecture_id_foreign
        foreign key (recorded_by_lecture_id) references lectures (id)
            on delete set null,
    constraint attendances_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade,
    constraint attendances_verified_by_lecture_id_foreign
        foreign key (verified_by_lecture_id) references lectures (id)
            on delete set null,
    constraint check_attendances_minutes_late_positive
        check (`minutes_late` >= 0),
    constraint check_attendances_minutes_present_positive
        check ((`minutes_present` is null) or (`minutes_present` >= 0)),
    constraint check_attendances_participation_score
        check ((`participation_score` is null) or ((`participation_score` >= 0) and (`participation_score` <= 10)))
)
create table course_registrations
(
    id                       bigint unsigned auto_increment
        primary key,
    student_id               bigint unsigned                                                                            not null,
    course_offering_id       bigint unsigned                                                                            not null,
    semester_id              bigint unsigned                                                                            not null,
    registration_status      enum ('registered', 'confirmed', 'dropped', 'withdrawn', 'completed') default 'registered' not null,
    registration_date        timestamp                                                                                  not null,
    registration_method      enum ('online', 'advisor', 'admin_override')                          default 'online'     not null,
    credit_hours             decimal(4, 2)                                                                              not null,
    final_grade              varchar(3)                                                                                 null,
    grade_points             decimal(3, 2)                                                                              null,
    attempt_number           int                                                                   default 1            not null,
    is_retake                tinyint(1)                                                            default 0            not null,
    drop_date                timestamp                                                                                  null,
    withdrawal_date          timestamp                                                                                  null,
    completion_date          timestamp                                                                                  null,
    retake_fee               decimal(10, 2)                                                        default 0.00         not null,
    is_retake_paid           enum ('yes', 'no')                                                    default 'no'         not null,
    notes                    text                                                                                       null,
    created_at               timestamp                                                                                  null,
    updated_at               timestamp                                                                                  null,
    original_registration_id bigint unsigned                                                                            null,
    retake_reason            text                                                                                       null,
    constraint unique_student_course_semester
        unique (student_id, course_offering_id, semester_id),
    constraint course_registrations_course_offering_id_foreign
        foreign key (course_offering_id) references course_offerings (id)
            on delete cascade,
    constraint course_registrations_original_registration_id_foreign
        foreign key (original_registration_id) references course_registrations (id)
            on delete set null,
    constraint course_registrations_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete cascade,
    constraint course_registrations_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade
)

create table enrollments
(
    id                    bigint unsigned auto_increment
        primary key,
    student_id            bigint unsigned                                                      not null,
    semester_id           bigint unsigned                                                      not null,
    curriculum_version_id bigint unsigned                                                      not null,
    semester_number       tinyint unsigned                                                     not null,
    status                enum ('in_progress', 'completed', 'withdrawn') default 'in_progress' not null,
    notes                 text                                                                 null,
    created_at            timestamp                                                            null,
    updated_at            timestamp                                                            null,
    constraint unique_student_semester_enrollment
        unique (student_id, semester_id),
    constraint enrollments_curriculum_version_id_foreign
        foreign key (curriculum_version_id) references curriculum_versions (id)
            on delete cascade,
    constraint enrollments_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete cascade,
    constraint enrollments_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade
)

create table gpa_calculations
(
    id                         bigint unsigned auto_increment
        primary key,
    student_id                 bigint unsigned                                                                                             not null,
    semester_id                bigint unsigned                                                                                             null,
    program_id                 bigint unsigned                                                                                             null,
    calculation_type           enum ('semester', 'cumulative', 'major', 'program', 'year', 'transfer', 'institutional') default 'semester' not null,
    gpa                        decimal(4, 3)                                                                                               null,
    quality_points             decimal(8, 3)                                                                            default 0.000      not null,
    credit_hours_attempted     decimal(6, 2)                                                                            default 0.00       not null,
    credit_hours_earned        decimal(6, 2)                                                                            default 0.00       not null,
    credit_hours_gpa           decimal(6, 2)                                                                            default 0.00       not null,
    total_courses              int                                                                                      default 0          not null,
    completed_courses          int                                                                                      default 0          not null,
    failed_courses             int                                                                                      default 0          not null,
    withdrawn_courses          int                                                                                      default 0          not null,
    incomplete_courses         int                                                                                      default 0          not null,
    a_grades                   int                                                                                      default 0          not null,
    b_grades                   int                                                                                      default 0          not null,
    c_grades                   int                                                                                      default 0          not null,
    d_grades                   int                                                                                      default 0          not null,
    f_grades                   int                                                                                      default 0          not null,
    academic_standing          enum ('excellent', 'good', 'satisfactory', 'probation', 'suspension', 'dismissal', 'warning')               null,
    required_gpa               decimal(3, 2)                                                                                               null,
    meets_gpa_requirement      tinyint(1)                                                                               default 1          not null,
    gpa_deficit                decimal(4, 3)                                                                                               null,
    academic_year              varchar(10)                                                                                                 null,
    year_level                 int                                                                                                         null,
    semester_type              enum ('fall', 'spring', 'summer', 'winter')                                                                 null,
    class_rank                 int                                                                                                         null,
    class_size                 int                                                                                                         null,
    percentile                 decimal(5, 2)                                                                                               null,
    program_rank               int                                                                                                         null,
    program_class_size         int                                                                                                         null,
    credits_needed_to_graduate decimal(6, 2)                                                                                               null,
    completion_percentage      decimal(5, 2)                                                                                               null,
    projected_graduation_date  date                                                                                                        null,
    on_track_to_graduate       tinyint(1)                                                                               default 1          not null,
    includes_transfer_credits  tinyint(1)                                                                               default 0          not null,
    includes_repeated_courses  tinyint(1)                                                                               default 0          not null,
    dean_list_eligible         tinyint(1)                                                                               default 0          not null,
    honors_eligible            tinyint(1)                                                                               default 0          not null,
    graduation_honors_eligible tinyint(1)                                                                               default 0          not null,
    calculated_at              timestamp                                                                                                   not null,
    calculated_by_lecture_id   bigint unsigned                                                                                             null,
    calculation_parameters     json                                                                                                        null,
    calculation_notes          text                                                                                                        null,
    is_verified                tinyint(1)                                                                               default 0          not null,
    verified_at                timestamp                                                                                                   null,
    verified_by_lecture_id     bigint unsigned                                                                                             null,
    is_current                 tinyint(1)                                                                               default 1          not null,
    previous_gpa               decimal(4, 3)                                                                                               null,
    gpa_change                 decimal(4, 3)                                                                                               null,
    gpa_trend                  enum ('improving', 'declining', 'stable')                                                                   null,
    created_at                 timestamp                                                                                                   null,
    updated_at                 timestamp                                                                                                   null,
    deleted_at                 timestamp                                                                                                   null,
    constraint unique_student_semester_calc_type
        unique (student_id, semester_id, calculation_type),
    constraint gpa_calculations_calculated_by_lecture_id_foreign
        foreign key (calculated_by_lecture_id) references lectures (id)
            on delete set null,
    constraint gpa_calculations_program_id_foreign
        foreign key (program_id) references programs (id)
            on delete set null,
    constraint gpa_calculations_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete set null,
    constraint gpa_calculations_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade,
    constraint gpa_calculations_verified_by_lecture_id_foreign
        foreign key (verified_by_lecture_id) references lectures (id)
            on delete set null,
    constraint check_completion_percentage
        check ((`completion_percentage` is null) or
               ((`completion_percentage` >= 0) and (`completion_percentage` <= 100))),
    constraint check_course_counts
        check ((`total_courses` >= 0) and (`completed_courses` >= 0) and (`failed_courses` >= 0) and
               (`withdrawn_courses` >= 0) and (`incomplete_courses` >= 0) and
               ((((`completed_courses` + `failed_courses`) + `withdrawn_courses`) + `incomplete_courses`) <=
                `total_courses`)),
    constraint check_credit_hours_positive
        check ((`credit_hours_attempted` >= 0) and (`credit_hours_earned` >= 0) and (`credit_hours_gpa` >= 0) and
               (`credit_hours_earned` <= `credit_hours_attempted`)),
    constraint check_gpa_valid
        check ((`gpa` is null) or ((`gpa` >= 0) and (`gpa` <= 4))),
    constraint check_grade_counts
        check ((`a_grades` >= 0) and (`b_grades` >= 0) and (`c_grades` >= 0) and (`d_grades` >= 0) and
               (`f_grades` >= 0)),
    constraint check_percentile
        check ((`percentile` is null) or ((`percentile` >= 0) and (`percentile` <= 100))),
    constraint check_quality_points_positive
        check (`quality_points` >= 0)
)
create table program_change_requests
(
    id                     bigint unsigned auto_increment
        primary key,
    student_id             bigint unsigned                                            not null,
    from_program_id        bigint unsigned                                            not null,
    to_program_id          bigint unsigned                                            not null,
    from_specialization_id bigint unsigned                                            null,
    to_specialization_id   bigint unsigned                                            null,
    reason                 text                                                       not null,
    status                 enum ('pending', 'approved', 'rejected') default 'pending' not null,
    approved_by            bigint unsigned                                            null,
    approved_at            timestamp                                                  null,
    approval_notes         text                                                       null,
    affected_credits       json                                                       null,
    created_at             timestamp                                                  null,
    updated_at             timestamp                                                  null,
    constraint program_change_requests_approved_by_foreign
        foreign key (approved_by) references users (id)
            on delete set null,
    constraint program_change_requests_from_program_id_foreign
        foreign key (from_program_id) references programs (id)
            on delete cascade,
    constraint program_change_requests_from_specialization_id_foreign
        foreign key (from_specialization_id) references specializations (id)
            on delete set null,
    constraint program_change_requests_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade,
    constraint program_change_requests_to_program_id_foreign
        foreign key (to_program_id) references programs (id)
            on delete cascade,
    constraint program_change_requests_to_specialization_id_foreign
        foreign key (to_specialization_id) references specializations (id)
            on delete set null
)