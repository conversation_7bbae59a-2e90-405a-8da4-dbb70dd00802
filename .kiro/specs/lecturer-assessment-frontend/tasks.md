# Implementation Plan

- [ ] 1. Set up assessment module foundation and routing

  - Create assessment module directory structure in `src/lecturer/components/assessment/`
  - Add assessment routes to Vue Router with courseOfferingId parameter and proper authentication guards
  - Create base assessment layout component with navigation tabs
  - _Requirements: 1.1, 11.1_

- [ ] 2. Implement assessment data layer and API integration
- [ ] 2.1 Create assessment TypeScript interfaces aligned with API

  - Define Student, Assessment, AssessmentDetail, Score interfaces in `src/lecturer/types/models/assessment.ts` matching API response structure
  - Create GradeUpdate, BulkGradeUpdate, and filter interfaces for API requests
  - Add OverviewStatistics, StudentGradingData, ComponentGradingData response types
  - _Requirements: 1.2, 2.1, 12.1_

- [ ] 2.2 Build assessment API composable with real endpoints

  - Create `useAssessmentApi.ts` composable extending existing `useBaseApi` pattern
  - Implement getGradingDataByStudent and getGradingDataByComponent methods
  - Add updateGrade and bulkUpdateGrades methods with proper error handling
  - Implement reporting endpoints: getOverviewStatistics, getGradeMatrix, getDetailedStatistics
  - Add export methods: exportToExcel and exportToPdf with blob handling
  - _Requirements: 1.4, 2.4, 3.3, 12.2_

- [ ] 2.3 Create tab-specific composables and minimal shared store
- [ ] 2.3.1 Create useGrading composable with API integration

  - Build `useGrading.ts` with courseOfferingId parameter and mode switching
  - Implement loadStudentData and loadComponentData methods using real API
  - Add updateGrade method with unsaved changes tracking
  - Implement bulkUpdateGrades with proper error handling and UI updates
  - _Requirements: 3.1, 3.3, 12.5_

- [ ] 2.3.2 Build useAssessmentReports composable with real endpoints

  - Create `useAssessmentReports.ts` with courseOfferingId parameter
  - Implement loadOverviewStatistics, loadGradeMatrix, loadDetailedStatistics methods
  - Add exportToExcel and exportToPdf methods with blob download handling
  - Implement updateGradeMatrixFilters with auto-refresh functionality
  - _Requirements: 7.1, 7.2, 10.2_

- [ ] 2.3.3 Create minimal shared Pinia store

  - Implement `sharedAssessment.ts` store for cross-page state only
  - Add currentCourseOfferingId, lastVisitedTab, and recentlyModifiedScores
  - Ensure minimal state footprint and proper cleanup
  - _Requirements: 12.1, 12.5_

- [ ] 2.3.4 Create useAutoSave supporting composable with real API

  - Build `useAutoSave.ts` with courseOfferingId parameter and debounced save functionality
  - Implement save queue management and conflict resolution using real updateGrade API
  - Add offline support with localStorage fallback and retry mechanism
  - Implement proper error handling and user feedback for save failures
  - _Requirements: 3.5, 12.3_

- [ ] 3. Build grading interface components
- [ ] 3.1 Create AssessmentGradingView component with useGrading composable

  - Build main grading page using `useGrading` composable with courseOfferingId prop
  - Implement view mode switching between by-student and by-component
  - Add student/component selection controls with proper loading states
  - Integrate with shared store for navigation state persistence
  - _Requirements: 3.1, 3.2, 11.2, 11.3_

- [ ] 3.2 Build GradingTable component for student data display

  - Create responsive data table for displaying StudentGradingData from API
  - Implement assessment details display with score information
  - Add proper ARIA labels and accessibility features for screen readers
  - Create mobile-responsive card layout for smaller screens
  - _Requirements: 3.3, 8.1, 8.4, 11.1, 11.4_

- [ ] 3.3 Create ComponentGradingTable for assessment component view

  - Build table component for displaying ComponentGradingData from API
  - Implement student scores display with statistics
  - Add sortable columns and filtering capabilities
  - Integrate with virtual scrolling for large student lists
  - _Requirements: 3.3, 8.1, 8.4, 11.1, 11.4_

- [ ] 3.4 Implement grade editing interface with real API integration

  - Create inline grade editing components using Score interface structure
  - Integrate with updateGrade API method through useGrading composable
  - Add real-time validation for points_earned, percentage_score, and letter_grade
  - Implement auto-save functionality using useAutoSave composable
  - _Requirements: 3.3, 3.5, 12.1, 12.3, 12.5_

- [ ] 4. Build advanced grading features using API fields
- [ ] 4.1 Create late submission handling interface

  - Implement late penalty display using is_late, minutes_late, and late_penalty_applied fields
  - Add late excuse approval interface using late_excuse_approved and late_excuse_reason fields
  - Create visual indicators for late submissions with proper color coding
  - _Requirements: 3.6, 3.7, 5.5_

- [ ] 4.2 Implement academic integrity management

  - Create plagiarism flagging interface using plagiarism_suspected, plagiarism_score, and plagiarism_notes fields
  - Add integrity status workflow using integrity_status field with proper visual indicators
  - Implement appeals processing using appeal_requested, appeal_reason, and appeal_status fields
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 4.3 Build score adjustment and exclusion controls

  - Create bonus points interface using bonus_points and bonus_reason fields with validation
  - Implement score exclusion controls using score_excluded and exclusion_reason fields
  - Add visual indicators for adjusted scores using icons and colors
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 5. Implement bulk operations using real API
- [ ] 5.1 Create bulk grading operations interface

  - Build bulk update modal using BulkGradeUpdate interface structure
  - Implement batch validation before calling bulkUpdateGrades API
  - Add progress tracking and proper error handling for partial failures
  - Handle bulk_errors response and display appropriate user feedback
  - _Requirements: 3.3, 12.4_

- [ ] 5.2 Build keyboard navigation and shortcuts

  - Implement keyboard shortcuts for common grading actions
  - Add proper tab order and focus management throughout interface
  - Create skip links and navigation aids for accessibility
  - _Requirements: 11.2, 11.3_

- [ ] 6. Create assessment reporting and analytics using real API
- [ ] 6.1 Build AssessmentReportView component with useAssessmentReports composable

  - Create main reporting interface using `useAssessmentReports` composable with courseOfferingId prop
  - Implement overview statistics display using OverviewStatistics interface
  - Add grade matrix display with filtering controls using GradeMatrixFilters
  - Integrate with shared store for navigation state persistence
  - _Requirements: 7.1, 7.2, 7.3, 10.2, 10.4_

- [ ] 6.2 Set up Chart.js integration for statistics visualization

  - Install and configure Chart.js with Vue.js integration
  - Create ChartContainer wrapper component with responsive design
  - Transform API statistics data into Chart.js compatible format
  - Implement chart loading states and error handling
  - _Requirements: 7.3, 10.1, 10.5_

- [ ] 6.3 Implement grade distribution visualizations from API data

  - Create histogram charts using score_statistics from OverviewStatistics
  - Display average_score, highest_score, lowest_score with visual indicators
  - Implement completion_rate visualization with progress indicators
  - Add interactive chart features with hover details
  - _Requirements: 7.3, 10.1, 10.2, 10.5_

- [ ] 7. Build comprehensive grade matrix using API data
- [ ] 7.1 Create grade matrix component using API response

  - Build responsive grade matrix using grade_matrix data from API
  - Display students and component_scores in tabular format
  - Implement weighted_total calculations with hover tooltips
  - Add proper handling for missing scores and exclusions using score_excluded field
  - _Requirements: 8.1, 8.2, 8.3, 8.5, 8.6, 8.7_

- [ ] 7.2 Implement filtering capabilities using API filters

  - Create filter controls using GradeMatrixFilters interface (include_excluded, score_status, student_ids, component_ids)
  - Implement updateGradeMatrixFilters method integration with auto-refresh
  - Add search functionality for student names and IDs
  - _Requirements: 8.4, 8.5_

- [ ] 7.3 Build letter grade display using API data

  - Display letter_grade field from Score interface with proper formatting
  - Add color coding based on grade ranges for visual clarity
  - Create grade scale legend and explanation tooltips
  - Handle different score_status values (draft, provisional, final) with visual indicators
  - _Requirements: 8.7, 11.5_

- [ ] 8. Implement student identification and alert systems using API data
- [ ] 8.1 Create student performance analysis components

  - Build at-risk student identification using overall_percentage from StudentGradingData summary
  - Implement exceptional student highlighting using completion_rate and average scores
  - Add academic integrity monitoring using integrity_status and plagiarism_suspected fields
  - _Requirements: 9.1, 9.2, 9.3, 9.4_

- [ ] 8.2 Build alert and notification system

  - Create alert cards for students requiring attention based on API data
  - Implement trend indicators for late submission patterns using is_late field
  - Add notification system integration for critical alerts
  - _Requirements: 9.5, 12.1_

- [ ] 9. Implement export functionality using real API endpoints
- [ ] 9.1 Create export interface and modal

  - Build export options modal with Excel/PDF format selection
  - Implement filtering controls using ExportFilters and PdfExportFilters interfaces
  - Add options for include_statistics, include_grade_matrix, include_charts, page_orientation
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 9.2 Build export processing using API methods

  - Implement exportToExcel and exportToPdf methods from useAssessmentReports composable
  - Handle blob responses and create download links automatically
  - Add proper error handling for failed export operations
  - Create success notifications and progress indicators
  - _Requirements: 6.5, 10.4, 10.5_

- [ ] 10. Implement responsive design and mobile optimization
- [ ] 10.1 Create mobile-responsive layouts

  - Adapt all components for mobile devices with touch-friendly interfaces
  - Implement horizontal scrolling and column prioritization for grade tables
  - Create mobile-specific navigation and interaction patterns
  - _Requirements: 11.1, 11.4, 11.5_

- [ ] 10.2 Build mobile grading interface

  - Create card-based grading interface for mobile devices using StudentGradingData
  - Implement swipe gestures for navigation between students and assessments
  - Add mobile-optimized input controls for grade editing with proper touch targets
  - _Requirements: 11.1, 11.5_

- [ ] 10.3 Implement mobile chart adaptations

  - Adapt Chart.js visualizations for mobile screens using API statistics data
  - Create touch-friendly chart interactions and controls
  - Implement alternative text descriptions for accessibility
  - _Requirements: 10.5, 11.1, 11.3_

- [ ] 11. Add comprehensive accessibility features
- [ ] 11.1 Implement ARIA labels and semantic markup

  - Add proper ARIA labels to all interactive elements including grade inputs
  - Implement semantic HTML structure for screen readers in grade tables
  - Create descriptive text for complex UI elements and charts using API data
  - _Requirements: 11.3, 11.5_

- [ ] 11.2 Build keyboard navigation system

  - Implement comprehensive keyboard shortcuts for grading actions
  - Add proper focus management and visual focus indicators for grade editing
  - Create skip links and navigation aids for efficient screen reader use
  - _Requirements: 11.2, 11.3_

- [ ] 11.3 Add color accessibility and visual indicators

  - Ensure WCAG 2.1 AA color contrast compliance throughout interface
  - Implement color-independent information display for grade status and integrity_status
  - Add high contrast mode support and customizable themes
  - _Requirements: 11.5_

- [ ] 12. Implement performance optimizations
- [ ] 12.1 Add virtual scrolling and lazy loading

  - Implement virtual scrolling for large grade tables using @tanstack/vue-virtual
  - Add lazy loading for charts and heavy components in reporting view
  - Create progressive loading for large student datasets from API
  - _Requirements: 6.5, 8.1_

- [ ] 12.2 Optimize Chart.js bundle and performance

  - Implement tree-shaking for Chart.js components to reduce bundle size
  - Add data sampling for large datasets from API statistics
  - Create chart caching and memoization for frequently accessed API data
  - _Requirements: 10.1, 10.2_

- [ ] 12.3 Implement composable-based state management optimizations

  - Add selective reactivity using shallowRef for large API response datasets in composables
  - Implement computed property caching for expensive calculations within composables
  - Create proper lifecycle management and cleanup in composables
  - Optimize memory usage by isolating tab-specific state (grading vs reporting)
  - _Requirements: 12.3, 12.5_

- [ ] 13. Build comprehensive testing suite
- [ ] 13.1 Create unit tests for components and composables

  - Write unit tests for grading and reporting components using Vitest
  - Test composables (useGrading, useAssessmentReports, useAutoSave) with API mocking
  - Add validation logic tests for grade updates and API error scenarios
  - _Requirements: All requirements_

- [ ] 13.2 Implement integration tests for user workflows

  - Create integration tests for complete grading workflows using real API structure
  - Test grade editing and bulk update scenarios with proper API responses
  - Add tests for export functionality and report generation
  - _Requirements: All requirements_

- [ ] 13.3 Add accessibility and responsive testing

  - Implement automated accessibility testing using axe-core
  - Create responsive design tests for different screen sizes
  - Add keyboard navigation tests for grade editing and navigation
  - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5_

- [ ] 14. Final integration and optimization
- [ ] 14.1 Integrate with existing lecturer portal

  - Connect assessment routes with existing lecturer navigation using courseOfferingId
  - Integrate with shared authentication and notification systems
  - Connect minimal shared store with existing portal navigation state
  - Ensure consistent theming and component usage throughout
  - _Requirements: All requirements_

- [ ] 14.2 Performance testing and optimization with real API

  - Conduct performance testing with large datasets from API endpoints
  - Optimize API calls and implement proper caching strategies for grading data
  - Add monitoring and error tracking for production deployment
  - Test auto-save functionality under various network conditions
  - _Requirements: 6.5, 12.2, 12.3_

- [ ] 14.3 User acceptance testing and final polish
  - Conduct user testing with sample lecturer grading workflows
  - Test export functionality with real data and various filter combinations
  - Fix any usability issues and polish user experience
  - Add final documentation and deployment preparation
  - _Requirements: All requirements_
