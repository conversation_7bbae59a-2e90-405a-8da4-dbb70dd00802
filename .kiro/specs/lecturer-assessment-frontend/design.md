# Design Document

## Overview

The Lecturer Assessment Frontend provides a comprehensive Vue.js interface for assessment management within the Portal SwinX application. Built using Vue 3 with Composition API and TypeScript, the system delivers an intuitive, responsive, and accessible user experience for lecturers to manage course assessments, grade student submissions, and generate performance reports. The design follows the existing project architecture with role-based modules, shared components, and consistent UI patterns.

## Architecture

### System Components

The frontend follows a layered architecture aligned with the existing Portal SwinX structure:

1. **Presentation Layer**: Vue 3 components with TypeScript and Composition API
2. **State Management**: Composables for local state, Pinia stores only for cross-page shared state
3. **API Integration**: Composables using the existing `useBaseApi` pattern
4. **UI Components**: shadcn/vue components with Tailwind CSS styling
5. **Routing**: Vue Router integration with role-based access control

### Data Flow

```mermaid
graph TD
    A[Assessment Views] --> B[Tab-Specific Composables]
    B --> C[Assessment API Composable]
    C --> D[Base API Service]
    D --> E[Backend API]

    F[Chart Components] --> G[Chart.js Integration]
    G --> H[useAssessmentReports Composable]

    I[Export Components] --> J[Export Service]
    J --> C

    K[Grading Interface] --> L[useGrading Composable]
    L --> M[Real-time Validation]

    N[Mobile Components] --> O[Responsive Layouts]
    O --> A

    P[Shared State] --> Q[Pinia Store]
    Q --> R[Cross-Page Components]
```

## Components and Interfaces

### Core Vue Components

#### AssessmentManagementView.vue

- **Location**: `src/lecturer/views/AssessmentManagementView.vue`
- **Purpose**: Main assessment management interface
- **Props**: `courseId: string`
- **Features**:
  - Hierarchical assessment component display
  - Inline editing with real-time validation
  - Weight constraint checking
  - Assessment creation and modification
  - Integration with existing lecturer layout

```vue
<script setup lang="ts">
interface Props {
  courseId: string
}

const props = defineProps<Props>()
const { assessments, loading, error, createAssessment, updateAssessment, deleteAssessment } = useAssessmentManagement(
  props.courseId,
)
</script>
```

#### AssessmentGradingView.vue

- **Location**: `src/lecturer/views/AssessmentGradingView.vue`
- **Purpose**: Dedicated grading interface
- **Props**: `courseId: string, assessmentId?: string`
- **Features**:
  - Tabular grading interface with keyboard navigation
  - View mode switching (by student/by component)
  - Bulk operations support
  - Auto-save functionality
  - Late submission handling

```vue
<script setup lang="ts">
interface Props {
  courseId: string
  assessmentId?: string
}

const props = defineProps<Props>()
const {
  grades,
  students,
  currentMode,
  selectedStudentId,
  unsavedChanges,
  loading,
  error,
  updateGrade,
  bulkUpdateGrades,
  switchMode,
} = useGrading(props.courseId, props.assessmentId)
</script>
```

#### AssessmentReportView.vue

- **Location**: `src/lecturer/views/AssessmentReportView.vue`
- **Purpose**: Comprehensive reporting and analytics
- **Props**: `courseId: string`
- **Features**:
  - Interactive Chart.js visualizations
  - Statistical overview cards
  - Grade distribution analysis
  - Student performance matrix
  - Export functionality

```vue
<script setup lang="ts">
interface Props {
  courseId: string
}

const props = defineProps<Props>()
const {
  reportData,
  chartData,
  statistics,
  loading,
  error,
  filters,
  filteredData,
  generateReport,
  exportReport,
  updateFilters,
} = useAssessmentReports(props.courseId)
</script>
```

### Shared UI Components

#### AssessmentCard.vue

- **Location**: `src/lecturer/components/assessment/AssessmentCard.vue`
- **Purpose**: Reusable assessment component display
- **Features**:
  - Consistent card layout using shadcn/vue Card
  - Weight validation indicators
  - Progress tracking
  - Action buttons integration

#### GradingTable.vue

- **Location**: `src/lecturer/components/assessment/GradingTable.vue`
- **Purpose**: Responsive data table for grading
- **Features**:
  - Virtual scrolling for large datasets
  - Sortable columns
  - Inline editing capabilities
  - Mobile-responsive design

#### ChartContainer.vue

- **Location**: `src/lecturer/components/assessment/ChartContainer.vue`
- **Purpose**: Chart.js wrapper component
- **Features**:
  - Responsive chart rendering
  - Loading states
  - Error handling
  - Accessibility features

### State Management Architecture

#### Tab-Specific Composables

Each tab/view has its own dedicated composable for local state management:

##### useAssessmentManagement

- **Location**: `src/lecturer/composables/useAssessmentManagement.ts`
- **Purpose**: Manage assessment CRUD operations and validation
- **Scope**: Assessment Management tab only

```typescript
export const useAssessmentManagement = (courseId: string) => {
  // Local state
  const assessments = ref<Assessment[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const validationErrors = ref<ValidationError[]>([])

  // Computed
  const totalWeight = computed(() => assessments.value.reduce((sum, a) => sum + a.weight, 0))
  const isWeightValid = computed(() => totalWeight.value <= 100)

  // API integration
  const api = useAssessmentApi()

  // Actions
  const fetchAssessments = async () => {
    loading.value = true
    try {
      assessments.value = await api.getAssessments(courseId)
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const createAssessment = async (assessment: CreateAssessmentRequest) => {
    // Implementation
  }

  const updateAssessment = async (id: number, updates: UpdateAssessmentRequest) => {
    // Implementation
  }

  const deleteAssessment = async (id: number) => {
    // Implementation
  }

  // Initialize
  onMounted(() => {
    fetchAssessments()
  })

  return {
    assessments: readonly(assessments),
    loading: readonly(loading),
    error: readonly(error),
    validationErrors: readonly(validationErrors),
    totalWeight,
    isWeightValid,
    createAssessment,
    updateAssessment,
    deleteAssessment,
    refetch: fetchAssessments,
  }
}
```

##### useGrading

- **Location**: `src/lecturer/composables/useGrading.ts`
- **Purpose**: Manage grading interface state and operations
- **Scope**: Grading tab only

```typescript
export const useGrading = (courseId: string, assessmentId?: string) => {
  // Local state
  const grades = ref<GradeEntry[]>([])
  const students = ref<Student[]>([])
  const currentMode = ref<'by-student' | 'by-component'>('by-student')
  const selectedStudentId = ref<number | null>(null)
  const unsavedChanges = ref<GradeEntry[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Auto-save functionality
  const { debouncedSave } = useAutoSave()

  // Actions
  const updateGrade = async (gradeData: GradeUpdate) => {
    // Add to unsaved changes
    unsavedChanges.value.push(gradeData)
    // Trigger debounced save
    debouncedSave(gradeData)
  }

  const bulkUpdateGrades = async (updates: GradeUpdate[]) => {
    // Implementation
  }

  const switchMode = (mode: 'by-student' | 'by-component') => {
    currentMode.value = mode
  }

  return {
    grades: readonly(grades),
    students: readonly(students),
    currentMode: readonly(currentMode),
    selectedStudentId,
    unsavedChanges: readonly(unsavedChanges),
    loading: readonly(loading),
    error: readonly(error),
    updateGrade,
    bulkUpdateGrades,
    switchMode,
  }
}
```

##### useAssessmentReports

- **Location**: `src/lecturer/composables/useAssessmentReports.ts`
- **Purpose**: Manage reporting and analytics data
- **Scope**: Reports tab only

```typescript
export const useAssessmentReports = (courseId: string) => {
  // Local state
  const reportData = ref<ReportData | null>(null)
  const chartData = ref<ChartData[]>([])
  const statistics = ref<Statistics | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filters = ref<ReportFilters>({
    dateRange: null,
    assessmentTypes: [],
    includeExcluded: false,
  })

  // Computed
  const filteredData = computed(() => {
    if (!reportData.value) return null
    return applyFilters(reportData.value, filters.value)
  })

  // Actions
  const generateReport = async () => {
    loading.value = true
    try {
      reportData.value = await api.generateReport(courseId, filters.value)
      chartData.value = transformToChartData(reportData.value)
      statistics.value = calculateStatistics(reportData.value)
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const exportReport = async (format: ExportFormat) => {
    return await api.exportReport(courseId, format, filters.value)
  }

  const updateFilters = (newFilters: Partial<ReportFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
    generateReport() // Auto-refresh on filter change
  }

  return {
    reportData: readonly(reportData),
    chartData: readonly(chartData),
    statistics: readonly(statistics),
    loading: readonly(loading),
    error: readonly(error),
    filters: readonly(filters),
    filteredData,
    generateReport,
    exportReport,
    updateFilters,
  }
}
```

#### Shared State (Pinia Store)

Only use Pinia for state that needs to be shared across multiple pages:

##### useSharedAssessmentStore

- **Location**: `src/lecturer/stores/sharedAssessment.ts`
- **Purpose**: Share minimal state across assessment pages
- **Scope**: Cross-page shared data only

```typescript
export const useSharedAssessmentStore = defineStore('sharedAssessment', () => {
  // Only shared state that needs to persist across page navigation
  const currentCourseId = ref<string | null>(null)
  const lastVisitedTab = ref<'management' | 'grading' | 'reports'>('management')
  const recentlyModifiedAssessments = ref<number[]>([])

  // Actions
  const setCurrentCourse = (courseId: string) => {
    currentCourseId.value = courseId
  }

  const setLastVisitedTab = (tab: 'management' | 'grading' | 'reports') => {
    lastVisitedTab.value = tab
  }

  const addRecentlyModified = (assessmentId: number) => {
    const index = recentlyModifiedAssessments.value.indexOf(assessmentId)
    if (index > -1) {
      recentlyModifiedAssessments.value.splice(index, 1)
    }
    recentlyModifiedAssessments.value.unshift(assessmentId)
    // Keep only last 10
    if (recentlyModifiedAssessments.value.length > 10) {
      recentlyModifiedAssessments.value = recentlyModifiedAssessments.value.slice(0, 10)
    }
  }

  return {
    currentCourseId: readonly(currentCourseId),
    lastVisitedTab: readonly(lastVisitedTab),
    recentlyModifiedAssessments: readonly(recentlyModifiedAssessments),
    setCurrentCourse,
    setLastVisitedTab,
    addRecentlyModified,
  }
})
```

### API Integration Layer

#### Assessment API Composable

- **Location**: `src/lecturer/composables/useAssessmentApi.ts`
- **Pattern**: Extends existing `useBaseApi` pattern
- **Features**:
  - Type-safe API calls
  - Error handling
  - Request caching
  - Optimistic updates

```typescript
export const useAssessmentApi = () => {
  const api = useBaseApi()

  // Assessment management endpoints
  const getAssessments = async (courseId: string) => {
    return api.lecturer.assessments.getAll(courseId)
  }

  const createAssessment = async (courseId: string, assessment: CreateAssessmentRequest) => {
    return api.lecturer.assessments.create(courseId, assessment)
  }

  const updateAssessment = async (assessmentId: number, updates: UpdateAssessmentRequest) => {
    return api.lecturer.assessments.update(assessmentId, updates)
  }

  const deleteAssessment = async (assessmentId: number) => {
    return api.lecturer.assessments.delete(assessmentId)
  }

  // Grading endpoints
  const getGrades = async (courseId: string, assessmentId?: string) => {
    return api.lecturer.assessments.getGrades(courseId, assessmentId)
  }

  const updateGrade = async (gradeData: GradeUpdate) => {
    return api.lecturer.assessments.updateGrade(gradeData)
  }

  const bulkUpdateGrades = async (updates: GradeUpdate[]) => {
    return api.lecturer.assessments.bulkUpdateGrades(updates)
  }

  // Reporting endpoints
  const generateReport = async (courseId: string, filters: ReportFilters) => {
    return api.lecturer.assessments.generateReport(courseId, filters)
  }

  const exportData = async (courseId: string, format: ExportFormat, filters?: ReportFilters) => {
    return api.lecturer.assessments.export(courseId, format, filters)
  }

  return {
    // Assessment management
    getAssessments,
    createAssessment,
    updateAssessment,
    deleteAssessment,
    // Grading
    getGrades,
    updateGrade,
    bulkUpdateGrades,
    // Reporting
    generateReport,
    exportData,
  }
}
```

#### Supporting Composables

##### useAutoSave

- **Location**: `src/lecturer/composables/useAutoSave.ts`
- **Purpose**: Handle auto-save functionality for grading
- **Features**: Debounced saves, conflict resolution, offline support

```typescript
export const useAutoSave = () => {
  const api = useAssessmentApi()
  const saveQueue = ref<GradeUpdate[]>([])
  const isSaving = ref(false)
  const lastSaved = ref<Date | null>(null)

  const debouncedSave = useDebounceFn(async (gradeData: GradeUpdate) => {
    if (isSaving.value) {
      saveQueue.value.push(gradeData)
      return
    }

    isSaving.value = true
    try {
      await api.updateGrade(gradeData)
      lastSaved.value = new Date()

      // Process queue
      if (saveQueue.value.length > 0) {
        const nextSave = saveQueue.value.shift()
        if (nextSave) {
          debouncedSave(nextSave)
        }
      }
    } catch (error) {
      // Handle save error - maybe store in localStorage for retry
      console.error('Auto-save failed:', error)
    } finally {
      isSaving.value = false
    }
  }, 1000)

  return {
    debouncedSave,
    isSaving: readonly(isSaving),
    lastSaved: readonly(lastSaved),
  }
}
```

## Data Models

### TypeScript Interfaces

#### Assessment Models

```typescript
interface Assessment {
  id: number
  name: string
  type: AssessmentType
  weight: number
  maxPoints: number
  dueDate: Date
  isRequired: boolean
  details: AssessmentDetail[]
}

interface AssessmentDetail {
  id: number
  name: string
  weight: number
  maxPoints: number
  description?: string
}

interface GradeEntry {
  id: number
  studentId: number
  assessmentDetailId: number
  pointsEarned: number
  percentageScore: number
  letterGrade: string
  status: GradeStatus
  submissionDate?: Date
  isLate: boolean
  latePenalty: number
  feedback?: string
}
```

#### UI State Models

```typescript
interface GradingSession {
  mode: 'by-student' | 'by-component'
  selectedStudentId?: number
  selectedAssessmentId?: number
  unsavedChanges: GradeEntry[]
  lastSaved: Date
}

interface ValidationState {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

interface ExportOptions {
  format: 'excel' | 'pdf'
  includeComments: boolean
  includeStatistics: boolean
  dateRange?: DateRange
}
```

## Error Handling

### Validation Strategy

#### Client-Side Validation

- **Weight Validation**: Real-time checking of assessment weight totals
- **Grade Validation**: Input validation for score ranges and formats
- **Form Validation**: Required field checking and format validation
- **Bulk Operation Validation**: Pre-validation before bulk updates

#### Error Display Patterns

```vue
<template>
  <!-- Inline validation errors -->
  <div v-if="validationErrors.weight" class="text-sm text-red-600">Total weight cannot exceed 100%</div>

  <!-- Toast notifications for API errors -->
  <Toast v-if="error" variant="destructive">
    {{ error }}
  </Toast>

  <!-- Loading states -->
  <div v-if="loading" class="flex items-center justify-center">
    <LoadingSpinner />
  </div>
</template>
```

### Error Recovery

- **Auto-save Recovery**: Restore unsaved changes from localStorage
- **Network Error Handling**: Retry mechanisms with exponential backoff
- **Conflict Resolution**: Handle concurrent editing scenarios
- **Graceful Degradation**: Fallback UI for failed chart rendering

## Testing Strategy

### Component Testing

- **Unit Tests**: Individual component logic and computed properties
- **Integration Tests**: Component interaction with stores and APIs
- **Accessibility Tests**: Screen reader compatibility and keyboard navigation
- **Responsive Tests**: Mobile and desktop layout validation

### Testing Tools

- **Vitest**: Unit and integration testing
- **Vue Test Utils**: Component testing utilities
- **Cypress**: End-to-end testing for complete workflows
- **Axe**: Accessibility testing integration

### Test Structure

```typescript
// Component test example
describe('AssessmentCard.vue', () => {
  it('displays weight validation error when total exceeds 100%', async () => {
    const wrapper = mount(AssessmentCard, {
      props: { assessment: mockAssessment, totalWeight: 105 },
    })

    expect(wrapper.find('[data-testid="weight-error"]').exists()).toBe(true)
  })
})
```

## Performance Optimization

### Frontend Optimization Strategies

#### Virtual Scrolling

- **Implementation**: Use `@tanstack/vue-virtual` for large grade tables
- **Benefits**: Handle thousands of students without performance degradation
- **Fallback**: Progressive loading for unsupported browsers

#### Chart Performance

- **Chart.js Optimization**: Register only required components
- **Data Sampling**: Implement data point reduction for large datasets
- **Lazy Loading**: Load charts only when visible
- **Caching**: Cache chart data and configurations

#### Bundle Optimization

```typescript
// Tree-shaking Chart.js components
import { Chart, CategoryScale, LinearScale, BarElement, BarController, Tooltip, Legend } from 'chart.js'

Chart.register(CategoryScale, LinearScale, BarElement, BarController, Tooltip, Legend)
```

### State Management Optimization

- **Computed Caching**: Leverage Vue's computed property caching within composables
- **Selective Reactivity**: Use `shallowRef` for large datasets in composables
- **Composable Modularity**: Separate composables for different tab concerns
- **Memory Management**: Cleanup watchers and subscriptions in composable lifecycle
- **Minimal Shared State**: Only use Pinia for truly cross-page shared data
- **Local State Isolation**: Keep tab-specific state isolated in respective composables

### API Optimization

- **Request Debouncing**: Debounce auto-save operations
- **Batch Operations**: Group multiple grade updates
- **Optimistic Updates**: Update UI before API confirmation
- **Caching Strategy**: Cache assessment structures and student lists

## Accessibility Implementation

### WCAG 2.1 AA Compliance

#### Keyboard Navigation

- **Tab Order**: Logical tab sequence through grading interface
- **Keyboard Shortcuts**: Quick navigation between students/assessments
- **Focus Management**: Clear focus indicators and management
- **Skip Links**: Navigation shortcuts for screen readers

#### Screen Reader Support

```vue
<template>
  <!-- Semantic markup -->
  <table role="table" aria-label="Student grades">
    <thead>
      <tr>
        <th scope="col" aria-sort="ascending">Student Name</th>
        <th scope="col">Assessment Score</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="student in students" :key="student.id">
        <th scope="row">{{ student.name }}</th>
        <td>
          <input
            :aria-label="`Grade for ${student.name}`"
            :aria-describedby="`grade-help-${student.id}`"
            v-model="student.grade"
          />
        </td>
      </tr>
    </tbody>
  </table>
</template>
```

#### Visual Accessibility

- **Color Contrast**: Ensure 4.5:1 contrast ratio for all text
- **Color Independence**: Don't rely solely on color for information
- **Focus Indicators**: Clear visual focus states
- **Text Scaling**: Support up to 200% zoom without horizontal scrolling

### Mobile Accessibility

- **Touch Targets**: Minimum 44px touch target size
- **Gesture Support**: Alternative to complex gestures
- **Orientation Support**: Both portrait and landscape modes
- **Voice Control**: Compatible with voice navigation

## Responsive Design Strategy

### Breakpoint System

Following Tailwind CSS breakpoints:

- **Mobile**: < 640px (sm)
- **Tablet**: 640px - 1024px (md/lg)
- **Desktop**: > 1024px (xl)

### Mobile-First Approach

```vue
<template>
  <!-- Mobile-first responsive design -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <AssessmentCard v-for="assessment in assessments" :key="assessment.id" :assessment="assessment" class="w-full" />
  </div>

  <!-- Mobile-optimized grading table -->
  <div class="block md:hidden">
    <MobileGradingCards :students="students" />
  </div>
  <div class="hidden md:block">
    <GradingTable :students="students" />
  </div>
</template>
```

### Progressive Enhancement

- **Core Functionality**: Works without JavaScript
- **Enhanced Experience**: JavaScript adds interactivity
- **Offline Support**: Service worker for basic functionality
- **Performance**: Lazy load non-critical features

## Security Considerations

### Client-Side Security

- **Input Sanitization**: Sanitize all user inputs before display
- **XSS Prevention**: Use Vue's built-in template escaping
- **CSRF Protection**: Include CSRF tokens in API requests
- **Data Validation**: Validate all data before sending to API

### Data Protection

- **Sensitive Data**: Never store sensitive data in localStorage
- **Session Management**: Proper token handling and renewal
- **Audit Logging**: Track all grade modifications
- **Permission Checking**: Verify permissions before UI actions

### API Security

```typescript
// Secure API calls with proper error handling
const updateGrade = async (gradeData: GradeUpdate) => {
  try {
    // Validate data before sending
    if (!validateGradeData(gradeData)) {
      throw new Error('Invalid grade data')
    }

    const response = await api.lecturer.assessments.updateGrade(gradeData)

    if (!response.success) {
      throw new Error(response.message)
    }

    return response.data
  } catch (error) {
    // Log error securely (no sensitive data)
    console.error('Grade update failed:', error.message)
    throw error
  }
}
```

## Composable Architecture Benefits

### Tab-Specific State Isolation

Each tab maintains its own state through dedicated composables, providing:

- **Performance**: No unnecessary reactivity across unrelated tabs
- **Memory Efficiency**: State is garbage collected when tabs are unmounted
- **Maintainability**: Clear separation of concerns for each feature area
- **Testing**: Easier unit testing of isolated functionality

### Composable Lifecycle Management

```typescript
// Example of proper lifecycle management in composables
export const useAssessmentManagement = (courseId: string) => {
  const assessments = ref<Assessment[]>([])
  const loading = ref(false)

  // Cleanup function
  const cleanup = () => {
    assessments.value = []
    loading.value = false
  }

  // Auto-cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    assessments: readonly(assessments),
    loading: readonly(loading),
    cleanup, // Expose for manual cleanup if needed
  }
}
```

### Shared State Strategy

Only the following state is shared via Pinia:

- Current course ID for navigation context
- Last visited tab for user experience continuity
- Recently modified assessments for cross-tab notifications

All other state remains local to each tab's composable.

## Integration Points

### Existing System Integration

- **Authentication**: Use existing auth store and token management
- **Navigation**: Integrate with lecturer portal navigation and shared assessment store for tab state
- **Notifications**: Use shared notification system
- **Theming**: Follow existing Tailwind CSS theme configuration
- **Shared State**: Minimal use of Pinia for cross-page navigation state only

### Router Integration

```typescript
// Route definitions
const routes = [
  {
    path: '/lecturer/teaching/courses/:courseId/assessments',
    name: 'AssessmentManagement',
    component: () => import('@/lecturer/views/AssessmentManagementView.vue'),
    meta: { requiresAuth: true, role: 'lecturer' },
  },
  {
    path: '/lecturer/teaching/courses/:courseId/assessments/grading',
    name: 'AssessmentGrading',
    component: () => import('@/lecturer/views/AssessmentGradingView.vue'),
    meta: { requiresAuth: true, role: 'lecturer' },
  },
  {
    path: '/lecturer/teaching/courses/:courseId/assessments/report',
    name: 'AssessmentReport',
    component: () => import('@/lecturer/views/AssessmentReportView.vue'),
    meta: { requiresAuth: true, role: 'lecturer' },
  },
]
```

### Component Library Integration

- **shadcn/vue Components**: Use existing UI component library
- **Lucide Icons**: Consistent icon usage
- **Tailwind Classes**: Follow existing utility class patterns
- **Form Components**: Leverage existing form validation patterns
