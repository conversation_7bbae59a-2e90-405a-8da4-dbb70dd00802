# Design Document

## Overview

The Lecturer Assessment Frontend provides a comprehensive Vue.js interface for assessment management within the Portal SwinX application. Built using Vue 3 with Composition API and TypeScript, the system delivers an intuitive, responsive, and accessible user experience for lecturers to manage course assessments, grade student submissions, and generate performance reports. The design follows the existing project architecture with role-based modules, shared components, and consistent UI patterns.

## Architecture

### System Components

The frontend follows a layered architecture aligned with the existing Portal SwinX structure:

1. **Presentation Layer**: Vue 3 components with TypeScript and Composition API
2. **State Management**: Composables for local state, Pinia stores only for cross-page shared state
3. **API Integration**: Composables using the existing `useBaseApi` pattern
4. **UI Components**: shadcn/vue components with Tailwind CSS styling
5. **Routing**: Vue Router integration with role-based access control

### Data Flow

```mermaid
graph TD
    A[Assessment Views] --> B[Tab-Specific Composables]
    B --> C[Assessment API Composable]
    C --> D[Base API Service]
    D --> E[Backend API]

    F[Chart Components] --> G[Chart.js Integration]
    G --> H[useAssessmentReports Composable]

    I[Export Components] --> J[Export Service]
    J --> C

    K[Grading Interface] --> L[useGrading Composable]
    L --> M[Real-time Validation]

    N[Mobile Components] --> O[Responsive Layouts]
    O --> A

    P[Shared State] --> Q[Pinia Store]
    Q --> R[Cross-Page Components]
```

## Components and Interfaces

### Core Vue Components

#### AssessmentManagementView.vue

- **Location**: `src/lecturer/views/AssessmentManagementView.vue`
- **Purpose**: Main assessment management interface
- **Props**: `courseId: string`
- **Features**:
  - Hierarchical assessment component display
  - Inline editing with real-time validation
  - Weight constraint checking
  - Assessment creation and modification
  - Integration with existing lecturer layout

```vue
<script setup lang="ts">
interface Props {
  courseId: string
}

const props = defineProps<Props>()
const { assessments, loading, error, createAssessment, updateAssessment, deleteAssessment } = useAssessmentManagement(
  props.courseId,
)
</script>
```

#### AssessmentGradingView.vue

- **Location**: `src/lecturer/views/AssessmentGradingView.vue`
- **Purpose**: Dedicated grading interface
- **Props**: `courseOfferingId: number`
- **Features**:
  - Tabular grading interface with keyboard navigation
  - View mode switching (by student/by component)
  - Bulk operations support
  - Auto-save functionality
  - Late submission handling

```vue
<script setup lang="ts">
interface Props {
  courseOfferingId: number
}

const props = defineProps<Props>()
const {
  currentMode,
  selectedStudentId,
  selectedComponentId,
  studentGradingData,
  componentGradingData,
  unsavedChanges,
  loading,
  error,
  loadStudentData,
  loadComponentData,
  updateGrade,
  bulkUpdateGrades,
  switchMode,
} = useGrading(props.courseOfferingId)
</script>
```

#### AssessmentReportView.vue

- **Location**: `src/lecturer/views/AssessmentReportView.vue`
- **Purpose**: Comprehensive reporting and analytics
- **Props**: `courseOfferingId: number`
- **Features**:
  - Interactive Chart.js visualizations
  - Statistical overview cards
  - Grade distribution analysis
  - Student performance matrix
  - Export functionality

```vue
<script setup lang="ts">
interface Props {
  courseOfferingId: number
}

const props = defineProps<Props>()
const {
  overviewStatistics,
  gradeMatrix,
  detailedStatistics,
  loading,
  error,
  gradeMatrixFilters,
  loadOverviewStatistics,
  loadGradeMatrix,
  loadDetailedStatistics,
  exportToExcel,
  exportToPdf,
  updateGradeMatrixFilters,
} = useAssessmentReports(props.courseOfferingId)

// Load initial data
onMounted(() => {
  loadOverviewStatistics()
  loadGradeMatrix()
})
</script>
```

### Shared UI Components

#### AssessmentCard.vue

- **Location**: `src/lecturer/components/assessment/AssessmentCard.vue`
- **Purpose**: Reusable assessment component display
- **Features**:
  - Consistent card layout using shadcn/vue Card
  - Weight validation indicators
  - Progress tracking
  - Action buttons integration

#### GradingTable.vue

- **Location**: `src/lecturer/components/assessment/GradingTable.vue`
- **Purpose**: Responsive data table for grading
- **Features**:
  - Virtual scrolling for large datasets
  - Sortable columns
  - Inline editing capabilities
  - Mobile-responsive design

#### ChartContainer.vue

- **Location**: `src/lecturer/components/assessment/ChartContainer.vue`
- **Purpose**: Chart.js wrapper component
- **Features**:
  - Responsive chart rendering
  - Loading states
  - Error handling
  - Accessibility features

### State Management Architecture

#### Tab-Specific Composables

Each tab/view has its own dedicated composable for local state management:

##### useAssessmentManagement

- **Location**: `src/lecturer/composables/useAssessmentManagement.ts`
- **Purpose**: Manage assessment CRUD operations and validation
- **Scope**: Assessment Management tab only

```typescript
export const useAssessmentManagement = (courseId: string) => {
  // Local state
  const assessments = ref<Assessment[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const validationErrors = ref<ValidationError[]>([])

  // Computed
  const totalWeight = computed(() => assessments.value.reduce((sum, a) => sum + a.weight, 0))
  const isWeightValid = computed(() => totalWeight.value <= 100)

  // API integration
  const api = useAssessmentApi()

  // Actions
  const fetchAssessments = async () => {
    loading.value = true
    try {
      assessments.value = await api.getAssessments(courseId)
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const createAssessment = async (assessment: CreateAssessmentRequest) => {
    // Implementation
  }

  const updateAssessment = async (id: number, updates: UpdateAssessmentRequest) => {
    // Implementation
  }

  const deleteAssessment = async (id: number) => {
    // Implementation
  }

  // Initialize
  onMounted(() => {
    fetchAssessments()
  })

  return {
    assessments: readonly(assessments),
    loading: readonly(loading),
    error: readonly(error),
    validationErrors: readonly(validationErrors),
    totalWeight,
    isWeightValid,
    createAssessment,
    updateAssessment,
    deleteAssessment,
    refetch: fetchAssessments,
  }
}
```

##### useGrading

- **Location**: `src/lecturer/composables/useGrading.ts`
- **Purpose**: Manage grading interface state and operations
- **Scope**: Grading tab only

```typescript
export const useGrading = (courseOfferingId: number) => {
  // Local state
  const currentMode = ref<'by-student' | 'by-component'>('by-student')
  const selectedStudentId = ref<number | null>(null)
  const selectedComponentId = ref<number | null>(null)
  const studentGradingData = ref<StudentGradingData | null>(null)
  const componentGradingData = ref<ComponentGradingData | null>(null)
  const unsavedChanges = ref<Array<{ scoreId: number } & GradeUpdate>>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // API integration
  const api = useAssessmentApi()
  const { debouncedSave } = useAutoSave(courseOfferingId)

  // Actions
  const loadStudentData = async (studentId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getGradingDataByStudent(courseOfferingId, studentId)
      if (response.success) {
        studentGradingData.value = response.data
        selectedStudentId.value = studentId
      } else {
        error.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to load student data'
    } finally {
      loading.value = false
    }
  }

  const loadComponentData = async (componentId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getGradingDataByComponent(courseOfferingId, componentId)
      if (response.success) {
        componentGradingData.value = response.data
        selectedComponentId.value = componentId
      } else {
        error.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to load component data'
    } finally {
      loading.value = false
    }
  }

  const updateGrade = async (scoreId: number, gradeData: GradeUpdate) => {
    // Add to unsaved changes
    const existingIndex = unsavedChanges.value.findIndex((change) => change.scoreId === scoreId)
    if (existingIndex >= 0) {
      unsavedChanges.value[existingIndex] = { scoreId, ...gradeData }
    } else {
      unsavedChanges.value.push({ scoreId, ...gradeData })
    }

    // Trigger debounced save
    debouncedSave(scoreId, gradeData)
  }

  const bulkUpdateGrades = async (updates: BulkGradeUpdate) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.bulkUpdateGrades(courseOfferingId, updates)
      if (response.success) {
        // Clear unsaved changes for successfully updated scores
        const updatedScoreIds = response.data.updated_scores.map((score) => score.id)
        unsavedChanges.value = unsavedChanges.value.filter((change) => !updatedScoreIds.includes(change.scoreId))

        // Refresh current data
        if (currentMode.value === 'by-student' && selectedStudentId.value) {
          await loadStudentData(selectedStudentId.value)
        } else if (currentMode.value === 'by-component' && selectedComponentId.value) {
          await loadComponentData(selectedComponentId.value)
        }
      } else {
        error.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to update grades'
    } finally {
      loading.value = false
    }
  }

  const switchMode = (mode: 'by-student' | 'by-component') => {
    currentMode.value = mode
    // Clear current data when switching modes
    studentGradingData.value = null
    componentGradingData.value = null
    selectedStudentId.value = null
    selectedComponentId.value = null
  }

  return {
    currentMode: readonly(currentMode),
    selectedStudentId: readonly(selectedStudentId),
    selectedComponentId: readonly(selectedComponentId),
    studentGradingData: readonly(studentGradingData),
    componentGradingData: readonly(componentGradingData),
    unsavedChanges: readonly(unsavedChanges),
    loading: readonly(loading),
    error: readonly(error),
    loadStudentData,
    loadComponentData,
    updateGrade,
    bulkUpdateGrades,
    switchMode,
  }
}
```

##### useAssessmentReports

- **Location**: `src/lecturer/composables/useAssessmentReports.ts`
- **Purpose**: Manage reporting and analytics data
- **Scope**: Reports tab only

```typescript
export const useAssessmentReports = (courseOfferingId: number) => {
  // Local state
  const overviewStatistics = ref<OverviewStatistics | null>(null)
  const gradeMatrix = ref<any | null>(null)
  const detailedStatistics = ref<any | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const gradeMatrixFilters = ref<GradeMatrixFilters>({
    include_excluded: false,
    score_status: 'final',
    student_ids: [],
    component_ids: [],
  })

  // API integration
  const api = useAssessmentApi()

  // Actions
  const loadOverviewStatistics = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getOverviewStatistics(courseOfferingId)
      if (response.success) {
        overviewStatistics.value = response.data
      } else {
        error.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to load overview statistics'
    } finally {
      loading.value = false
    }
  }

  const loadGradeMatrix = async (filters?: GradeMatrixFilters) => {
    loading.value = true
    error.value = null
    try {
      const filtersToUse = filters || gradeMatrixFilters.value
      const response = await api.getGradeMatrix(courseOfferingId, filtersToUse)
      if (response.success) {
        gradeMatrix.value = response.data
      } else {
        error.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to load grade matrix'
    } finally {
      loading.value = false
    }
  }

  const loadDetailedStatistics = async (type?: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getDetailedStatistics(courseOfferingId, type)
      if (response.success) {
        detailedStatistics.value = response.data
      } else {
        error.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to load detailed statistics'
    } finally {
      loading.value = false
    }
  }

  const exportToExcel = async (filters?: ExportFilters) => {
    try {
      const response = await api.exportToExcel(courseOfferingId, filters)
      // Handle blob download
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `assessment-report-${courseOfferingId}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (err) {
      error.value = err.message || 'Failed to export to Excel'
      throw err
    }
  }

  const exportToPdf = async (filters?: PdfExportFilters) => {
    try {
      const response = await api.exportToPdf(courseOfferingId, filters)
      // Handle blob download
      const blob = new Blob([response], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `assessment-report-${courseOfferingId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (err) {
      error.value = err.message || 'Failed to export to PDF'
      throw err
    }
  }

  const updateGradeMatrixFilters = (newFilters: Partial<GradeMatrixFilters>) => {
    gradeMatrixFilters.value = { ...gradeMatrixFilters.value, ...newFilters }
    loadGradeMatrix() // Auto-refresh on filter change
  }

  return {
    overviewStatistics: readonly(overviewStatistics),
    gradeMatrix: readonly(gradeMatrix),
    detailedStatistics: readonly(detailedStatistics),
    loading: readonly(loading),
    error: readonly(error),
    gradeMatrixFilters: readonly(gradeMatrixFilters),
    loadOverviewStatistics,
    loadGradeMatrix,
    loadDetailedStatistics,
    exportToExcel,
    exportToPdf,
    updateGradeMatrixFilters,
  }
}
```

#### Shared State (Pinia Store)

Only use Pinia for state that needs to be shared across multiple pages:

##### useSharedAssessmentStore

- **Location**: `src/lecturer/stores/sharedAssessment.ts`
- **Purpose**: Share minimal state across assessment pages
- **Scope**: Cross-page shared data only

```typescript
export const useSharedAssessmentStore = defineStore('sharedAssessment', () => {
  // Only shared state that needs to persist across page navigation
  const currentCourseOfferingId = ref<number | null>(null)
  const lastVisitedTab = ref<'management' | 'grading' | 'reports'>('management')
  const recentlyModifiedScores = ref<number[]>([])

  // Actions
  const setCurrentCourseOffering = (courseOfferingId: number) => {
    currentCourseOfferingId.value = courseOfferingId
  }

  const setLastVisitedTab = (tab: 'management' | 'grading' | 'reports') => {
    lastVisitedTab.value = tab
  }

  const addRecentlyModifiedScore = (scoreId: number) => {
    const index = recentlyModifiedScores.value.indexOf(scoreId)
    if (index > -1) {
      recentlyModifiedScores.value.splice(index, 1)
    }
    recentlyModifiedScores.value.unshift(scoreId)
    // Keep only last 10
    if (recentlyModifiedScores.value.length > 10) {
      recentlyModifiedScores.value = recentlyModifiedScores.value.slice(0, 10)
    }
  }

  return {
    currentCourseOfferingId: readonly(currentCourseOfferingId),
    lastVisitedTab: readonly(lastVisitedTab),
    recentlyModifiedScores: readonly(recentlyModifiedScores),
    setCurrentCourseOffering,
    setLastVisitedTab,
    addRecentlyModifiedScore,
  }
})
```

### API Integration Layer

#### Assessment API Composable

- **Location**: `src/lecturer/composables/useAssessmentApi.ts`
- **Pattern**: Extends existing `useBaseApi` pattern
- **Features**:
  - Type-safe API calls
  - Error handling
  - Request caching
  - Optimistic updates

```typescript
export const useAssessmentApi = () => {
  const api = useBaseApi()

  // Grading endpoints - aligned with actual API
  const getGradingDataByStudent = async (courseOfferingId: number, studentId: number) => {
    return api.get(`/lecturer/courses/${courseOfferingId}/assessments/grade/student/${studentId}`)
  }

  const getGradingDataByComponent = async (courseOfferingId: number, assessmentComponentId: number) => {
    return api.get(`/lecturer/courses/${courseOfferingId}/assessments/grade/component/${assessmentComponentId}`)
  }

  const updateGrade = async (courseOfferingId: number, scoreId: number, gradeData: GradeUpdate) => {
    return api.put(`/lecturer/courses/${courseOfferingId}/assessments/scores/${scoreId}`, gradeData)
  }

  const bulkUpdateGrades = async (courseOfferingId: number, updates: BulkGradeUpdate) => {
    return api.post(`/lecturer/courses/${courseOfferingId}/assessments/scores/bulk-update`, updates)
  }

  // Reporting endpoints - aligned with actual API
  const getOverviewStatistics = async (courseOfferingId: number) => {
    return api.get(`/lecturer/courses/${courseOfferingId}/assessments/report/overview`)
  }

  const getGradeMatrix = async (courseOfferingId: number, filters?: GradeMatrixFilters) => {
    const params = new URLSearchParams()
    if (filters?.include_excluded) params.append('include_excluded', 'true')
    if (filters?.score_status) params.append('score_status', filters.score_status)
    if (filters?.student_ids?.length) {
      filters.student_ids.forEach((id) => params.append('student_ids[]', id.toString()))
    }
    if (filters?.component_ids?.length) {
      filters.component_ids.forEach((id) => params.append('component_ids[]', id.toString()))
    }

    const queryString = params.toString()
    const url = `/lecturer/courses/${courseOfferingId}/assessments/report/grade-matrix${queryString ? '?' + queryString : ''}`
    return api.get(url)
  }

  const getDetailedStatistics = async (courseOfferingId: number, type?: string) => {
    const params = type ? `?type=${type}` : ''
    return api.get(`/lecturer/courses/${courseOfferingId}/assessments/report/statistics${params}`)
  }

  const exportToExcel = async (courseOfferingId: number, filters?: ExportFilters) => {
    const params = new URLSearchParams()
    if (filters?.include_excluded) params.append('include_excluded', 'true')
    if (filters?.score_status) params.append('score_status', filters.score_status)
    if (filters?.include_statistics) params.append('include_statistics', 'true')
    if (filters?.include_grade_matrix) params.append('include_grade_matrix', 'true')

    const queryString = params.toString()
    const url = `/lecturer/courses/${courseOfferingId}/assessments/report/export/excel${queryString ? '?' + queryString : ''}`
    return api.get(url, { responseType: 'blob' })
  }

  const exportToPdf = async (courseOfferingId: number, filters?: PdfExportFilters) => {
    const params = new URLSearchParams()
    if (filters?.include_charts) params.append('include_charts', 'true')
    if (filters?.include_statistics) params.append('include_statistics', 'true')
    if (filters?.include_grade_matrix) params.append('include_grade_matrix', 'true')
    if (filters?.page_orientation) params.append('page_orientation', filters.page_orientation)
    if (filters?.include_student_details) params.append('include_student_details', 'true')

    const queryString = params.toString()
    const url = `/lecturer/courses/${courseOfferingId}/assessments/report/export/pdf${queryString ? '?' + queryString : ''}`
    return api.get(url, { responseType: 'blob' })
  }

  return {
    // Grading
    getGradingDataByStudent,
    getGradingDataByComponent,
    updateGrade,
    bulkUpdateGrades,
    // Reporting
    getOverviewStatistics,
    getGradeMatrix,
    getDetailedStatistics,
    exportToExcel,
    exportToPdf,
  }
}
```

#### Supporting Composables

##### useAutoSave

- **Location**: `src/lecturer/composables/useAutoSave.ts`
- **Purpose**: Handle auto-save functionality for grading
- **Features**: Debounced saves, conflict resolution, offline support

```typescript
export const useAutoSave = (courseOfferingId: number) => {
  const api = useAssessmentApi()
  const saveQueue = ref<Array<{ scoreId: number; gradeData: GradeUpdate }>>([])
  const isSaving = ref(false)
  const lastSaved = ref<Date | null>(null)
  const saveErrors = ref<Array<{ scoreId: number; error: string }>>([])

  const debouncedSave = useDebounceFn(async (scoreId: number, gradeData: GradeUpdate) => {
    if (isSaving.value) {
      // Add to queue if already saving
      const existingIndex = saveQueue.value.findIndex((item) => item.scoreId === scoreId)
      if (existingIndex >= 0) {
        saveQueue.value[existingIndex] = { scoreId, gradeData }
      } else {
        saveQueue.value.push({ scoreId, gradeData })
      }
      return
    }

    isSaving.value = true
    try {
      const response = await api.updateGrade(courseOfferingId, scoreId, gradeData)
      if (response.success) {
        lastSaved.value = new Date()
        // Remove any previous errors for this score
        saveErrors.value = saveErrors.value.filter((error) => error.scoreId !== scoreId)

        // Process queue
        if (saveQueue.value.length > 0) {
          const nextSave = saveQueue.value.shift()
          if (nextSave) {
            debouncedSave(nextSave.scoreId, nextSave.gradeData)
          }
        }
      } else {
        // Handle API error response
        saveErrors.value.push({ scoreId, error: response.message })
      }
    } catch (error) {
      // Handle network/unexpected errors
      const errorMessage = error.message || 'Auto-save failed'
      saveErrors.value.push({ scoreId, error: errorMessage })

      // Store in localStorage for retry on next page load
      const failedSave = { scoreId, gradeData, timestamp: Date.now() }
      const existingFailures = JSON.parse(localStorage.getItem('failed_grade_saves') || '[]')
      existingFailures.push(failedSave)
      localStorage.setItem('failed_grade_saves', JSON.stringify(existingFailures))

      console.error('Auto-save failed:', error)
    } finally {
      isSaving.value = false
    }
  }, 1000)

  const retryFailedSaves = async () => {
    const failedSaves = JSON.parse(localStorage.getItem('failed_grade_saves') || '[]')
    const courseSaves = failedSaves.filter(
      (save) =>
        // Filter saves for current course (you might need to store courseId in failed saves)
        true, // For now, retry all failed saves
    )

    for (const save of courseSaves) {
      try {
        await api.updateGrade(courseOfferingId, save.scoreId, save.gradeData)
        // Remove from failed saves on success
        const updatedFailures = failedSaves.filter((f) => f.scoreId !== save.scoreId || f.timestamp !== save.timestamp)
        localStorage.setItem('failed_grade_saves', JSON.stringify(updatedFailures))
      } catch (error) {
        console.error('Retry failed for score:', save.scoreId, error)
      }
    }
  }

  const clearSaveErrors = () => {
    saveErrors.value = []
  }

  return {
    debouncedSave,
    isSaving: readonly(isSaving),
    lastSaved: readonly(lastSaved),
    saveErrors: readonly(saveErrors),
    retryFailedSaves,
    clearSaveErrors,
  }
}
```

## Data Models

### TypeScript Interfaces

#### Assessment Models (Aligned with API)

```typescript
interface Student {
  id: number
  student_id: string
  name: string
  first_name: string
  last_name: string
  email: string
}

interface Assessment {
  id: number
  name: string
  code: string
  type: string
  type_name: string
  weight: number
  due_date: string
  is_required_to_sit_final_exam: boolean
  details: AssessmentDetail[]
}

interface AssessmentDetail {
  id: number
  name: string
  description?: string
  weight: number
  max_points: number
  due_date: string
  score?: Score
  student_scores?: StudentScore[]
  statistics?: AssessmentStatistics
}

interface Score {
  id: number
  points_earned: number
  percentage_score: number
  letter_grade: string
  status: string
  score_status: 'draft' | 'provisional' | 'final'
  is_late: boolean
  minutes_late: number
  late_penalty_applied: number
  bonus_points: number
  score_excluded: boolean
  plagiarism_suspected: boolean
  integrity_status: 'clean' | 'flagged' | 'under_review' | 'violation_confirmed' | 'cleared'
  instructor_feedback?: string
  private_notes?: string
  graded_at?: string
  graded_by_lecture_id?: number
  last_modified_at?: string
  last_modified_by_lecture_id?: number
}

interface StudentScore {
  student: Student
  score: Score
}

interface AssessmentStatistics {
  total_students: number
  submitted_count: number
  graded_count: number
  average_score: number
  highest_score: number
  lowest_score: number
  completion_rate: number
}
```

#### API Request/Response Models

````typescript
interface GradeUpdate {
  points_earned?: number
  percentage_score?: number
  letter_grade?: string
  status?: string
  score_status?: 'draft' | 'provisional' | 'final'
  instructor_feedback?: string
  private_notes?: string
  bonus_points?: number
  bonus_reason?: string
  score_excluded?: boolean
  exclusion_reason?: string
  late_excuse_approved?: boolean
  late_excuse_reason?: string
  plagiarism_suspected?: boolean
  plagiarism_score?: number
  plagiarism_notes?: string
  integrity_status?: 'clean' | 'flagged' | 'under_review' | 'violation_confirmed' | 'cleared'
  appeal_requested?: boolean
  appeal_reason?: string
  appeal_status?: 'pending' | 'under_review' | 'approved' | 'denied'
}

interface BulkGradeUpdate {
  scores: Array<{
    id: number
  } & GradeUpdate>
}

interface GradeMatrixFilters {
  include_excluded?: boolean
  score_status?: 'draft' | 'provisional' | 'final'
  student_ids?: number[]
  component_ids?: number[]
}

interface ExportFilters {
  include_excluded?: boolean
  score_status?: 'draft' | 'provisional' | 'final'
  student_ids?: number[]
  component_ids?: number[]
  include_statistics?: boolean
  include_grade_matrix?: boolean
}

interface PdfExportFilters {
  include_charts?: boolean
  include_statistics?: boolean
  include_grade_matrix?: boolean
  page_orientation?: 'portrait' | 'landscape'
  include_student_details?: boolean
}

interface StudentGradingData {
  student: Student
  assessments: Assessment[]
  summary: {
    total_assessments: number
    completed_assessments: number
    pending_assessments: number
    overall_percentage: number
    weighted_total_score: number
    total_weight_completed: number
  }
}

interface ComponentGradingData {
  assessment_component: Assessment
  details: AssessmentDetail[]
  statistics: {
    total_students: number
    total_submissions: number
    graded_submissions: number
    average_score: number
    completion_rate: number
  }
}

interface OverviewStatistics {
  course_offering: {
    id: number
    course_code: string
    course_title: string
    section_code: string
    semester: {
      id: number
      name: string
      year: number
    }
    instructor: {
      id: number
      name: string
    }
  }
  statistics: {
    enrollment_statistics: {
      total_enrolled_students: number
      active_students: number
    }
    assessment_structure: {
      total_components: number
      total_details: number
      total_weight: number
      weight_complete: boolean
    }
    score_statistics: {
      average_score: number
      highest_score: number
      lowest_score: number
      total_graded_submissions: number
    }
    completion_statistics: {
      overall_completion_rate: number
    }
    component_breakdown: any[]
  }
}

#### UI State Models

```typescript
interface GradingSession {
  mode: 'by-student' | 'by-component'
  selectedStudentId?: number
  selectedComponentId?: number
  unsavedChanges: Array<{ scoreId: number } & GradeUpdate>
  lastSaved?: Date
}

interface ValidationState {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

interface ValidationError {
  field: string
  message: string
  code: string
}

interface ValidationWarning {
  field: string
  message: string
  code: string
}
````

## Error Handling

### Validation Strategy

#### Client-Side Validation

- **Weight Validation**: Real-time checking of assessment weight totals
- **Grade Validation**: Input validation for score ranges and formats
- **Form Validation**: Required field checking and format validation
- **Bulk Operation Validation**: Pre-validation before bulk updates

#### Error Display Patterns

```vue
<template>
  <!-- Inline validation errors -->
  <div v-if="validationErrors.weight" class="text-sm text-red-600">Total weight cannot exceed 100%</div>

  <!-- Toast notifications for API errors -->
  <Toast v-if="error" variant="destructive">
    {{ error }}
  </Toast>

  <!-- Loading states -->
  <div v-if="loading" class="flex items-center justify-center">
    <LoadingSpinner />
  </div>
</template>
```

### Error Recovery

- **Auto-save Recovery**: Restore unsaved changes from localStorage
- **Network Error Handling**: Retry mechanisms with exponential backoff
- **Conflict Resolution**: Handle concurrent editing scenarios
- **Graceful Degradation**: Fallback UI for failed chart rendering

## Testing Strategy

### Component Testing

- **Unit Tests**: Individual component logic and computed properties
- **Integration Tests**: Component interaction with stores and APIs
- **Accessibility Tests**: Screen reader compatibility and keyboard navigation
- **Responsive Tests**: Mobile and desktop layout validation

### Testing Tools

- **Vitest**: Unit and integration testing
- **Vue Test Utils**: Component testing utilities
- **Cypress**: End-to-end testing for complete workflows
- **Axe**: Accessibility testing integration

### Test Structure

```typescript
// Component test example
describe('AssessmentCard.vue', () => {
  it('displays weight validation error when total exceeds 100%', async () => {
    const wrapper = mount(AssessmentCard, {
      props: { assessment: mockAssessment, totalWeight: 105 },
    })

    expect(wrapper.find('[data-testid="weight-error"]').exists()).toBe(true)
  })
})
```

## Performance Optimization

### Frontend Optimization Strategies

#### Virtual Scrolling

- **Implementation**: Use `@tanstack/vue-virtual` for large grade tables
- **Benefits**: Handle thousands of students without performance degradation
- **Fallback**: Progressive loading for unsupported browsers

#### Chart Performance

- **Chart.js Optimization**: Register only required components
- **Data Sampling**: Implement data point reduction for large datasets
- **Lazy Loading**: Load charts only when visible
- **Caching**: Cache chart data and configurations

#### Bundle Optimization

```typescript
// Tree-shaking Chart.js components
import { Chart, CategoryScale, LinearScale, BarElement, BarController, Tooltip, Legend } from 'chart.js'

Chart.register(CategoryScale, LinearScale, BarElement, BarController, Tooltip, Legend)
```

### State Management Optimization

- **Computed Caching**: Leverage Vue's computed property caching within composables
- **Selective Reactivity**: Use `shallowRef` for large datasets in composables
- **Composable Modularity**: Separate composables for different tab concerns
- **Memory Management**: Cleanup watchers and subscriptions in composable lifecycle
- **Minimal Shared State**: Only use Pinia for truly cross-page shared data
- **Local State Isolation**: Keep tab-specific state isolated in respective composables

### API Optimization

- **Request Debouncing**: Debounce auto-save operations
- **Batch Operations**: Group multiple grade updates
- **Optimistic Updates**: Update UI before API confirmation
- **Caching Strategy**: Cache assessment structures and student lists

## Accessibility Implementation

### WCAG 2.1 AA Compliance

#### Keyboard Navigation

- **Tab Order**: Logical tab sequence through grading interface
- **Keyboard Shortcuts**: Quick navigation between students/assessments
- **Focus Management**: Clear focus indicators and management
- **Skip Links**: Navigation shortcuts for screen readers

#### Screen Reader Support

```vue
<template>
  <!-- Semantic markup -->
  <table role="table" aria-label="Student grades">
    <thead>
      <tr>
        <th scope="col" aria-sort="ascending">Student Name</th>
        <th scope="col">Assessment Score</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="student in students" :key="student.id">
        <th scope="row">{{ student.name }}</th>
        <td>
          <input
            :aria-label="`Grade for ${student.name}`"
            :aria-describedby="`grade-help-${student.id}`"
            v-model="student.grade"
          />
        </td>
      </tr>
    </tbody>
  </table>
</template>
```

#### Visual Accessibility

- **Color Contrast**: Ensure 4.5:1 contrast ratio for all text
- **Color Independence**: Don't rely solely on color for information
- **Focus Indicators**: Clear visual focus states
- **Text Scaling**: Support up to 200% zoom without horizontal scrolling

### Mobile Accessibility

- **Touch Targets**: Minimum 44px touch target size
- **Gesture Support**: Alternative to complex gestures
- **Orientation Support**: Both portrait and landscape modes
- **Voice Control**: Compatible with voice navigation

## Responsive Design Strategy

### Breakpoint System

Following Tailwind CSS breakpoints:

- **Mobile**: < 640px (sm)
- **Tablet**: 640px - 1024px (md/lg)
- **Desktop**: > 1024px (xl)

### Mobile-First Approach

```vue
<template>
  <!-- Mobile-first responsive design -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <AssessmentCard v-for="assessment in assessments" :key="assessment.id" :assessment="assessment" class="w-full" />
  </div>

  <!-- Mobile-optimized grading table -->
  <div class="block md:hidden">
    <MobileGradingCards :students="students" />
  </div>
  <div class="hidden md:block">
    <GradingTable :students="students" />
  </div>
</template>
```

### Progressive Enhancement

- **Core Functionality**: Works without JavaScript
- **Enhanced Experience**: JavaScript adds interactivity
- **Offline Support**: Service worker for basic functionality
- **Performance**: Lazy load non-critical features

## Security Considerations

### Client-Side Security

- **Input Sanitization**: Sanitize all user inputs before display
- **XSS Prevention**: Use Vue's built-in template escaping
- **CSRF Protection**: Include CSRF tokens in API requests
- **Data Validation**: Validate all data before sending to API

### Data Protection

- **Sensitive Data**: Never store sensitive data in localStorage
- **Session Management**: Proper token handling and renewal
- **Audit Logging**: Track all grade modifications
- **Permission Checking**: Verify permissions before UI actions

### API Security

```typescript
// Secure API calls with proper error handling
const updateGrade = async (gradeData: GradeUpdate) => {
  try {
    // Validate data before sending
    if (!validateGradeData(gradeData)) {
      throw new Error('Invalid grade data')
    }

    const response = await api.lecturer.assessments.updateGrade(gradeData)

    if (!response.success) {
      throw new Error(response.message)
    }

    return response.data
  } catch (error) {
    // Log error securely (no sensitive data)
    console.error('Grade update failed:', error.message)
    throw error
  }
}
```

## Composable Architecture Benefits

### Tab-Specific State Isolation

Each tab maintains its own state through dedicated composables, providing:

- **Performance**: No unnecessary reactivity across unrelated tabs
- **Memory Efficiency**: State is garbage collected when tabs are unmounted
- **Maintainability**: Clear separation of concerns for each feature area
- **Testing**: Easier unit testing of isolated functionality

### Composable Lifecycle Management

```typescript
// Example of proper lifecycle management in composables
export const useAssessmentManagement = (courseId: string) => {
  const assessments = ref<Assessment[]>([])
  const loading = ref(false)

  // Cleanup function
  const cleanup = () => {
    assessments.value = []
    loading.value = false
  }

  // Auto-cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    assessments: readonly(assessments),
    loading: readonly(loading),
    cleanup, // Expose for manual cleanup if needed
  }
}
```

### Shared State Strategy

Only the following state is shared via Pinia:

- Current course ID for navigation context
- Last visited tab for user experience continuity
- Recently modified assessments for cross-tab notifications

All other state remains local to each tab's composable.

## Integration Points

### Existing System Integration

- **Authentication**: Use existing auth store and token management
- **Navigation**: Integrate with lecturer portal navigation and shared assessment store for tab state
- **Notifications**: Use shared notification system
- **Theming**: Follow existing Tailwind CSS theme configuration
- **Shared State**: Minimal use of Pinia for cross-page navigation state only

### Router Integration

```typescript
// Route definitions - aligned with API structure using courseOfferingId
const routes = [
  {
    path: '/lecturer/teaching/courses/:courseOfferingId/assessments',
    name: 'AssessmentManagement',
    component: () => import('@/lecturer/views/AssessmentManagementView.vue'),
    meta: { requiresAuth: true, role: 'lecturer' },
    props: (route) => ({ courseOfferingId: parseInt(route.params.courseOfferingId) }),
  },
  {
    path: '/lecturer/teaching/courses/:courseOfferingId/assessments/grading',
    name: 'AssessmentGrading',
    component: () => import('@/lecturer/views/AssessmentGradingView.vue'),
    meta: { requiresAuth: true, role: 'lecturer' },
    props: (route) => ({ courseOfferingId: parseInt(route.params.courseOfferingId) }),
  },
  {
    path: '/lecturer/teaching/courses/:courseOfferingId/assessments/report',
    name: 'AssessmentReport',
    component: () => import('@/lecturer/views/AssessmentReportView.vue'),
    meta: { requiresAuth: true, role: 'lecturer' },
    props: (route) => ({ courseOfferingId: parseInt(route.params.courseOfferingId) }),
  },
]
```

### Component Library Integration

- **shadcn/vue Components**: Use existing UI component library
- **Lucide Icons**: Consistent icon usage
- **Tailwind Classes**: Follow existing utility class patterns
- **Form Components**: Leverage existing form validation patterns
